#!/bin/sh

. /lib/functions.sh
. /lib/functions/network.sh
. /lib/zone/zone_api.sh

pppoeserver_nat_load_rule_napt() {
    local rule_napt_interface="$1"
    local rule_napt_ipaddr="$2"

    local ifaces=$(zone_get_effect_ifaces "${rule_napt_interface}")
    for iface in $ifaces;do
        local device=$(zone_get_effect_devices "${iface}")
        
        [ -n "$iface" -a -n "$device" ] && {
                echo "add multiNat chain ,device is $device,iface is $iface ">>   /tmp/.nat/hotplug.log
                iptables -w -t nat -A postrouting_rule_multi_nat -o $device -m set --match-set $rule_napt_ipaddr src -j MASQUERADE -m comment --comment "PPPOE_SERVER_${rule_napt_interface}"
            }
    done
}

pppoeserver_nat_delete_rule_napt() {
    local rule_napt_interface="$1"
    local napt_postrouting_chain="postrouting_rule_multi_nat"

    local napt_rule_num=`iptables -w -t nat -nv --line-number -L ${napt_postrouting_chain} | grep -w "PPPOE_SERVER_${rule_napt_interface}" | cut -d " " -f 1`
    #echo "rule_napt_sym is $rule_napt_sym,napt_rule_num is $napt_rule_num " > /dev/console
    if [ -n "$napt_rule_num" ];then 
        iptables -w -t nat -D ${napt_postrouting_chain} $napt_rule_num
    fi
}

