#!/bin/sh

. /lib/functions.sh
. /lib/functions/network.sh
. /lib/zone/zone_api.sh
DHCPD_BOOTSTRAP_PID_FILE=/var/run/dhcpd6_bootstrap.pid

is_dhcpd_bootstrap_sh_started(){
	if [ -f ${DHCPD_BOOTSTRAP_PID_FILE} ]
	then
		ps_res=`ps | grep "dhcpd6_boot*" | grep -v "grep" | awk '{print $1}'`
		if [[ "$ps_res" != "" ]];then
			split=`echo $ps_res | cut -d " " -f 1`
			if [[ $split == `cat ${DHCPD_BOOTSTRAP_PID_FILE}` ]];then
				return 1
			fi
		fi
	fi
	return 0
}

is_dhcpd_bootstrap_sh_started
if [ $? -eq 1 ]; then
	[ "$DEVICE" == "lo" ] && exit 0
	case "$ACTION" in
			ifup)
				zone_dev_is_lan $DEVICE
				[ $? -eq 0 ] && /etc/init.d/dhcpd6 reload
			;;
			ifdown)
				# do nothing
			;;
			ifupdate)
				# do nothing
			;;
	esac
fi

