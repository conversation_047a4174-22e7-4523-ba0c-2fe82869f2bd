#
#vpn
#
vpn l2tp_server_global l2tphellointerval
vpn l2tp_server_global lcpechointerval
vpn l2tp_client_global l2tphellointerval
vpn l2tp_client_global lcpechointerval
vpn pptp_server_global pptphellointerval
vpn pptp_server_global lcpechointerval
vpn pptp_client_global pptphellointerval
vpn pptp_client_global lcpechointerval
vpn ipsec_global route_enable
vpn sdwan_global enable

uhttpd main listen_http_lan
uhttpd main listen_https



upnpd config enable_upnp


system sys timezone
system sys is_factory
system sys bridge_set_enable
system sys has_set_pwd
system sys go_wizard
system sys apmng_detection

system metric static
system metric dhcp
system metric pppoe
system metric pptp
system metric l2tp

system time_type type
system ntp enable_server
system ntp pri_ntp
system ntp snd_ntp

system service_port https_port
system service_port web_timeout
system service_port http_port
system service_port max_trytimes
system service_port lock_time
system service_port http_port_switch

system syslog level_switch
system syslog level
system syslog module_switch
system syslog module
system syslog log_send
system syslog send_server
system syslog log_size

service all ref
service ftp ref
service ssh ref
service telnet ref
service smtp ref
service dns ref
service http ref
service pop3 ref
service sntp ref
service h323 ref
service icmp_all ref

#
# SFE configuration
#
shortcut_fe main enable

#
# alarm configuration
#
alarm event
alarm email

#
# wireless configuration
#

wireless wlan_host_2g enable
wireless wlan_host_2g ssidbrd
wireless wlan_host_2g ssid
wireless wlan_host_2g encryption
wireless wlan_host_2g channel
wireless wlan_host_2g mode
wireless wlan_host_2g bandwidth
wireless wlan_host_2g power
wireless wlan_host_2g isolate

wireless wlan_wds_2g enable
wireless wlan_wds_2g encryption
wireless wlan_wds_2g address_form

wireless wlan_host_5g enable
wireless wlan_host_5g ssidbrd
wireless wlan_host_5g ssid
wireless wlan_host_5g encryption
wireless wlan_host_5g channel
wireless wlan_host_5g mode
wireless wlan_host_5g bandwidth
wireless wlan_host_5g power
wireless wlan_host_5g isolate
wireless wlan_host_5g vhtmubfer

wireless wlan_wds_5g enable
wireless wlan_wds_5g encryption
wireless wlan_wds_5g address_form

wireless freq0 macaddr
wireless freq1 macaddr

network MGMT proto
network MGMT macaddr
network MGMT ipaddr
network MGMT netmask
network MGMT t_reference
network MGMT t6_reference

network 1 proto
network 1 macaddr
network 1 t_reference
network 1 t6_reference

network 2 proto
network 2 macaddr
network 2 t_reference
network 2 t6_reference

network 3 proto
network 3 macaddr
network 3 t_reference
network 3 t6_reference

network 4 proto
network 4 macaddr
network 4 t_reference
network 4 t6_reference

network 5 proto
network 5 macaddr
network 5 t_reference
network 5 t6_reference

network 6 proto
network 6 macaddr
network 6 t_reference
network 6 t6_reference

network 7 proto
network 7 macaddr
network 7 t_reference
network 7 t6_reference

network 8 proto
network 8 macaddr
network 8 t_reference
network 8 t6_reference
network 8 speed

network 9 proto
network 9 macaddr
network 9 t_reference
network 9 t6_reference
network 9 speed

network bridge_v6

nwadditional MGMT proto
nwadditional MGMT ipaddr
nwadditional MGMT netmask

opkg global allow_any_src
opkg global check_pkg_sig

#
# webpasswd configuration


# flood_defense configuration
flood_defense threshold tcp_conn_lim
flood_defense threshold tcp_conn_bst
flood_defense threshold udp_conn_lim
flood_defense threshold udp_conn_bst
flood_defense threshold icmp_conn_lim
flood_defense threshold icmp_conn_bst
flood_defense threshold tcp_src_lim
flood_defense threshold tcp_src_bst
flood_defense threshold udp_src_lim
flood_defense threshold udp_src_bst
flood_defense threshold icmp_src_lim
flood_defense threshold icmp_src_bst
flood_defense global tcp_conn_en
flood_defense global udp_conn_en
flood_defense global icmp_conn_en
flood_defense global tcp_src_en
flood_defense global udp_src_en
flood_defense global icmp_src_en

# dos_defense configuration
dos_defense global ip_frag
dos_defense global tcp_noflag
dos_defense global ping_death
dos_defense global ping_large
dos_defense global tcp_winnuke
dos_defense global land
dos_defense global teardrop
dos_defense global tcp_fin_syn
dos_defense global tcp_fin_noack
dos_defense global ip_option
dos_defense global ipopt_secure
dos_defense global ipopt_strict_route
dos_defense global ipopt_stream
dos_defense global ipopt_noop
dos_defense global ipopt_loose_route
dos_defense global ipopt_record_route
dos_defense global ipopt_timestamp

# netscan_defense configuration
netscan_defense global portscan_rate
netscan_defense global portscan_ageing_time
netscan_defense global ipscan_def
netscan_defense global ipscan_rate
netscan_defense global portscan_def
netscan_defense global ipscan_ageing_time

# mac_filter configuration
mac_filter global filter_mode
mac_filter global enable
mac_filter global interfaces

# ipv6 firewall configuration
ipv6_firewall basic ipv6_firewall_state

# apmng_set configuration
apmng_set settings apmngr_status
apmng_set settings auto_firm
apmng_set settings auto_reboot
apmng_set settings reboot_day
apmng_set settings reboot_time

apmng_set aplog_setting ac_log_switch
apmng_set aplog_setting remote_server_switch
apmng_set aplog_setting ac_log_level
apmng_set aplog_setting remote_log_level
apmng_set aplog_setting remote_server
apmng_set aplog_setting remote_report_interval

#apmng_rrm configuration
apmng_rrm settings pwr_enable
apmng_rrm settings pwr_threshold
apmng_rrm settings pwr_max_pwr
apmng_rrm settings pwr_min_pwr
apmng_rrm settings chnl_enable
apmng_rrm settings bw_2g
apmng_rrm settings chnl_2g_set
apmng_rrm settings bw_5g
apmng_rrm settings chnl_5g1_set
apmng_rrm settings chnl_5g2_set
apmng_rrm settings autorrm_enable
apmng_rrm settings autorrm_date
apmng_rrm settings autorrm_time

#apmng_report configuration
apmng_report settings interval
apmng_report settings max_load
apmng_report settings protocol
apmng_report settings state
apmng_report settings content
apmng_report settings apgroup

# apmng_mesh configuration
apmng_mesh settings enable
apmng_mesh settings backhaul_type
apmng_mesh settings re_auto_switch

#apmng_roam configuration
apmng_roam roam 2g_roaming_threshold_type
apmng_roam roam 5g_roaming_threshold_type
apmng_roam roam 11k_switch
apmng_roam roam 11v_switch
apmng_roam roam 11r_switch
apmng_roam roam 2g_rssi_trigger_threshold
apmng_roam roam 2g_low_rssi_kick
apmng_roam roam 2g_low_rssi_threshold
apmng_roam roam 2g_rate_trigger_threshold
apmng_roam roam 2g_low_rate_kick
apmng_roam roam 2g_low_rate_threshold
apmng_roam roam 5g_rssi_trigger_threshold
apmng_roam roam 5g_low_rssi_kick
apmng_roam roam 5g_low_rssi_threshold
apmng_roam roam 5g_rate_trigger_threshold
apmng_roam roam 5g_low_rate_kick
apmng_roam roam 5g_low_rate_threshold
apmng_roam roam threshold_check_interval
apmng_roam roam roaming_dif
apmng_roam roam blacklist_restrict_time
apmng_roam roam stats_switch
apmng_roam roam stats_interval

# free_strategy configuration


#  session_limits configuration
session_limits global status

# arp_defense configuration
arp_defense global enable
arp_defense global interval
arp_defense global imb_pass
arp_defense global garp
arp_defense global interface
arp_defense scan_range ip_start
arp_defense scan_range ip_end


# behave_audit configuration
behave_audit setting log_send_enable
behave_audit setting send_server

# security_audit configuration
security_audit setting

# default_behavior_audit configuration
audit_policy global default_behavior_audit

# nat configuration
nat alg_glb ftp
nat alg_glb h323
nat alg_glb pptp
nat alg_glb sip

# statistics configuration
system ip_gbl status
system ip_gbl ip
system ip_gbl mask
system ip_gbl interval

# security_policy configuration
security_policy security_policy_default action
security_global_config global_config http_resume_enable
security_global_config global_config ftp_resume_enable

webpasswd admin password
webpasswd admin username

# snmp configuration
snmp setting

#
# apmng_balance configuration

# apmng_bcast_storm_ctrl configuration
#

# apmng_band_navigation configuration
apmng_band_navigation settings enable
apmng_band_navigation settings 5g_threshold
apmng_band_navigation settings dif_threshold
apmng_band_navigation settings max_fail_num

#
# apmng_backup_link configuration
apmng_backup_link settings enable
apmng_backup_link settings priority
apmng_backup_link settings backup_ip
apmng_backup_link settings backup_ip6

#
# apmng_rate configuration
apmng_rate rate_11a 11a_base
apmng_rate rate_11a 11a_supp
apmng_rate rate_11a 11a_mult
apmng_rate rate_11b 11b_base
apmng_rate rate_11b 11b_supp
apmng_rate rate_11b 11b_mult
apmng_rate rate_11g 11g_base
apmng_rate rate_11g 11g_supp
apmng_rate rate_11g 11g_mult
apmng_rate rate_11n 11n_base
apmng_rate rate_11n 11n_supp
apmng_rate rate_11ac 11ac_base
apmng_rate rate_11ac 11ac_supp

#

#
# wlanwarn configuration
#

#
# wlan_timer configuration
#

#
# u_hwnat configuration
#

#
# shortcut_fe configuration
#

#
# reboot_timer configuration
#

#apmng_audit
apmng_audit settings enable
apmng_audit settings remote_ip

# authentication configuration
authentication portal age_enable
authentication portal age_time
authentication portal portal_port
authentication portal https_enable
authentication portal portal_https_port
authentication portal auth_mode
authentication portal cmcc_port
authentication portal cmcc_portal_port
authentication portal escape_enable
authentication portal escape_interval
authentication portal escape_strategy
authentication portal cert_expire_time
authentication portal cert_crt_md5
authentication portal cert_key_md5
authentication portal wechat_enable
authentication db_wechat_domain url
authentication freestrategyParam id

#
# mwan3 / load_balance configuration
#
mwan3 global state

mwan3 special_rule state
mwan3 special_rule use_policy

mwan3 MGMT enabled
mwan3 MGMT balance
mwan3 MGMT ref

mwan3 1 enabled
mwan3 1 balance
mwan3 1 ref

mwan3 2 enabled
mwan3 2 balance
mwan3 2 ref

mwan3 3 enabled
mwan3 3 balance
mwan3 3 ref

mwan3 4 enabled
mwan3 4 balance
mwan3 4 ref

mwan3 5 enabled
mwan3 5 balance
mwan3 5 ref

mwan3 6 enabled
mwan3 6 balance
mwan3 6 ref

mwan3 7 enabled
mwan3 7 balance
mwan3 7 ref

mwan3 8 enabled
mwan3 8 balance
mwan3 8 ref

mwan3 9 enabled
mwan3 9 balance
mwan3 9 ref

mwan3 special_policy use_if

load_balance basic balance_state

remote_mngt MGMT enabled
remote_mngt 1 enabled
remote_mngt 2 enabled
remote_mngt 3 enabled
remote_mngt 4 enabled
remote_mngt 5 enabled
remote_mngt 6 enabled
remote_mngt 7 enabled
remote_mngt 8 enabled
remote_mngt 9 enabled

#
# isp_route configuration
#
isp_route global state

#
# online_check configuration
#
online_check MGMT mode
online_check MGMT gateway
online_check MGMT dns

online_check 1 mode
online_check 1 gateway
online_check 1 dns

online_check 2 mode
online_check 2 gateway
online_check 2 dns

online_check 3 mode
online_check 3 gateway
online_check 3 dns

online_check 4 mode
online_check 4 gateway
online_check 4 dns

online_check 5 mode
online_check 5 gateway
online_check 5 dns

online_check 6 mode
online_check 6 gateway
online_check 6 dns

online_check 7 mode
online_check 7 gateway
online_check 7 dns

online_check 8 mode
online_check 8 gateway
online_check 8 dns

online_check 9 mode
online_check 9 gateway
online_check 9 dns

# qos configuration
qos MGMT t_name
qos MGMT uplink
qos MGMT downlink

qos 1 t_name
qos 1 uplink
qos 1 downlink

qos 2 t_name
qos 2 uplink
qos 2 downlink

qos 3 t_name
qos 3 uplink
qos 3 downlink

qos 4 t_name
qos 4 uplink
qos 4 downlink

qos 5 t_name
qos 5 uplink
qos 5 downlink

qos 6 t_name
qos 6 uplink
qos 6 downlink

qos 7 t_name
qos 7 uplink
qos 7 downlink

qos 8 t_name
qos 8 uplink
qos 8 downlink

qos 9 t_name
qos 9 uplink
qos 9 downlink

qos setting interface
qos setting qos_enable
qos setting qos_threshold
qos setting threshold_enable

qos polling time_unit
qos polling enable_time
qos polling disable_time
qos polling trust_threshold

#ipgroup configuration
ipgroup ipgroup_any ref

time_mngt any ref

#
#luci
#
luci sauth sessiontime

#diagnose
diagnose settings enable
diagnose settings telnet_port

#
# cloud_config configuration
#
cloud_config bind username
cloud_config bind password


cloud_config register username
cloud_config register password
cloud_config register verify_code
cloud_config register account_type

cloud_config reset_account_pwd username
cloud_config reset_account_pwd password
cloud_config reset_account_pwd verify_code
cloud_config reset_account_pwd account_type


cloud_config get_reg_verify_code username
cloud_config get_reg_verify_code account_type

cloud_config get_reset_pwd_verify_code username
cloud_config get_reset_pwd_verify_code account_type



cloud_config new_firmware fw_new_notify
cloud_config new_firmware fw_update_type
cloud_config new_firmware not_show

cloud_config device_legality illegal
cloud_config device_legality illegal_type
cloud_config device_legality reconnect_time

cloud_config device_status bind_status
cloud_config device_status login_status
cloud_config device_status account_status

cloud_config upgrade_info type
cloud_config upgrade_info version
cloud_config upgrade_info release_date
cloud_config upgrade_info download_url
cloud_config upgrade_info location
cloud_config upgrade_info release_log
cloud_config upgrade_info release_log_url
cloud_config upgrade_info global_flag

cloud_config info alias

cloud_config bind_tip not_show
cloud_config brand_mac renew_time
cloud_config brand_mac check_value

cloud_config bind_info bind_status
cloud_config bind_info first_check_bind
cloud_config bind_info site_id
cloud_config bind_info site_name

cloud_config static switch
cloud_config static report_time

cloud_config conf_app switch
cloud_config conf_app report_time

cloud_config conf_web switch
cloud_config conf_web report_time

cloud_config conn_dev switch
cloud_config conn_dev report_time

cloud_config surr_dev switch
cloud_config surr_dev report_time

cloud_config conf_mngt mngt_switch

cloud_config group_info group_id

cloud_config report_setting time_interval
cloud_config report_setting report_switch

cloud_config ui_notify_info top_bar_notify_again
cloud_config ui_notify_info setting_function_selected

cloud_config tums_info username
cloud_config tums_info server
cloud_config tums_info port
cloud_config tums_info enable
cloud_config tums_info mfrs_id
cloud_config tums_info comment

cloud_config cloud_type cloud_type

cloud_sync config_id config_id
cloud_sync config_sync apmng_set
#
# cloud_status configuration
#
cloud_status check_fw_ver owner
cloud_status check_fw_ver action_status
cloud_status check_fw_ver err_code

cloud_status download_fw owner
cloud_status download_fw action_status
cloud_status download_fw err_code

cloud_status download_file owner
cloud_status download_file action_status
cloud_status download_file err_code

cloud_status get_account_stat owner
cloud_status get_account_stat action_status
cloud_status get_account_stat err_code

cloud_status reset_account_pwd owner
cloud_status reset_account_pwd action_status
cloud_status reset_account_pwd err_code

cloud_status modify_account_pwd owner
cloud_status modify_account_pwd action_status
cloud_status modify_account_pwd err_code

cloud_status get_reg_verify_code owner
cloud_status get_reg_verify_code action_status
cloud_status get_reg_verify_code err_code

cloud_status get_reset_pwd_verify_code owner
cloud_status get_reset_pwd_verify_code action_status
cloud_status get_reset_pwd_verify_code err_code

cloud_status check_reg_verify_code owner
cloud_status check_reg_verify_code action_status
cloud_status check_reg_verify_code err_code

cloud_status check_reset_pwd_verify_code owner
cloud_status check_reset_pwd_verify_code action_status
cloud_status check_reset_pwd_verify_code err_code

cloud_status notify_event owner
cloud_status notify_event action_status
cloud_status notify_event err_code

cloud_status bind owner
cloud_status bind action_status
cloud_status bind err_code

cloud_status login owner
cloud_status login action_status
cloud_status login err_code

cloud_status resend_email owner
cloud_status resend_email action_status
cloud_status resend_email err_code

cloud_status unbind owner
cloud_status unbind action_status
cloud_status unbind err_code

cloud_status register owner
cloud_status register action_status
cloud_status register err_code

cloud_status get_plugins owner
cloud_status get_plugins action_status
cloud_status get_plugins err_code

cloud_status get_can_update_plugins owner
cloud_status get_can_update_plugins action_status
cloud_status get_can_update_plugins err_code

cloud_status get_not_installed_plugins owner
cloud_status get_not_installed_plugins action_status
cloud_status get_not_installed_plugins err_code

cloud_status check_plugin_update owner
cloud_status check_plugin_update action_status
cloud_status check_plugin_update err_code

cloud_status get_plugin_info owner
cloud_status get_plugin_info action_status
cloud_status get_plugin_info err_code

cloud_status download_plugin owner
cloud_status download_plugin action_status
cloud_status download_plugin err_code

cloud_status synchronize_plugin owner
cloud_status synchronize_plugin action_status
cloud_status synchronize_plugin err_code

cloud_status plugin_service owner
cloud_status plugin_service action_status
cloud_status plugin_service err_code

cloud_status client_info connect_status
cloud_status client_info reconnect_time
cloud_status client_info disconnect_reason
cloud_status client_info fw_download_progress
cloud_status client_info fw_download_status

cloud_status offline offline_reason

#
# cloud sdk configuration
#
cloud_sdk cloud defaultValidTime

cloud_sdk default heartbeat_interval_ms


#
# PPPoE server configuration
#
pppoe_server setting enable
pppoe_server setting force_enable
pppoe_server setting user_isolation
pppoe_server setting max_sessions
pppoe_server setting max_noreply_lcp
pppoe_server setting idle_time
pppoe_server setting auth_type

#
# UCL configuration
#
ucl global enable
ucl_auth settings enable

# SSH switch
dropbear default ssh_port_switch

intelligent_ops_data_report apmng_set module_switch

# ZONE
zone NORMAL iface
zone trust iface
zone MGMT
zone 1
zone 2
zone 3
zone 4
zone 5
zone 6
zone 7
zone 8
zone 9

# istart
istart global
istart server

# Plugin
plugin_config plugin sync_status
plugin_config plugin manual_installed
plugin_config plugin not_installed
plugin_config plugin can_update
plugin_config plugin sync_status

# Signature_db
signature_db app
signature_db av
signature_db ips
signature_db url
signature_db hotsite

#
# app_library configuration
#
app_library global smart_mode_enable

#
# IPS configuration
#
ips signature_filter_web action
ips signature_filter_file action
ips signature_filter_dns action
ips signature_filter_mail action
ips signature_filter_intra action
ips signature_filter_internet action
ips signature_filter_dmz action
ips signature_filter_ids action
ips signature_filter_maxsec action
ips signature_filter_default action

# sdwan
sdwan link_stat_check
sdwan config
sdwan bypass

# snmpd configuration
snmpd setting enable
snmpd setting local_engine
snmpd setting trap_enable

#
# ecm
#
ecm global

#
# projection
#
projection setting qr_code_band_switch
projection setting device_name_alter_switch
projection setting bind_mode
