local e = require "luci.ltn12"
local r = require "luci.http.protocol"
local s = require "luci.util"
local h = require "string"
local e = require "coroutine"
local d = require "table"
local c, u, o, i, a, f = ipairs, pairs, next, type, tostring, error
local n = print
module "luci.http"
context = s.threadlocal()
local l = 4096
local t = {}
local n = 0
Request = s.class()
function Request.__init__(e, n, t, o)
--l.getenv(), o(io.stdin, tonumber(l.getenv("CONTENT_LENGTH"))),i.sink.file(io.stderr)
    e.input = t
    e.error = o
    e.filehandler = function() end
    e.message = {
        env = n,
        headers = {},
        params = r.urldecode_params(n.QUERY_STRING or ""),
        json = nil,
        raw_data = nil
    }
    e.parsed_input = false
end
function Request.formvalue(e, n, t)
    if not t and not e.parsed_input then e:_parse_input() end
    if n then
        return e.message.params[n]
    else
        return e.message.params
    end
end
function Request.formvaluetable(n, e)
    local t = {}
    e = e and e .. "." or "."
    if not n.parsed_input then n:_parse_input() end
    local r = n.message.params[nil]
    for n, r in u(n.message.params) do
        if n:find(e, 1, true) == 1 then t[n:sub(#e + 1)] = a(r) end
    end
    return t
end
function Request.jsondata(e)
    if not e.parsed_input then e:_parse_input() end
    return e.message.json
end
function Request.get_raw_data(e)
    if not e.parsed_input then e:_parse_input() end
    return e.message.raw_data
end
function Request.content(e)
    if not e.parsed_input then e:_parse_input() end
    return e.message.content, e.message.content_length
end
function Request.getcookie(e, n)
    local e = h.gsub(";" .. (e:getenv("HTTP_COOKIE") or "") .. ";", "%s*;%s*",
                     ";")
    local n = ";" .. n .. "=(.-);"
    local n, n, e = e:find(n)
    return e and urldecode(e)
end
function Request.getenv(n, e)
    if e then
        return n.message.env[e]
    else
        return n.message.env
    end
end
function Request.setfilehandler(n, e) n.filehandler = e end
function Request._parse_input(e)
    r.parse_message_body(e.input, e.message, e.filehandler)
    e.parsed_input = true
end
function close()
    if not context.eoh then flush_header() end
    flush()
    if not context.closed then
        context.closed = true
        e.yield(5)
    end
end
function content() return context.request:content() end
function formvalue(e, n) return context.request:formvalue(e, n) end
function formvaluetable(e) return context.request:formvaluetable(e) end
function jsondata() return context.request:jsondata() end
function get_raw_data() return context.request:get_raw_data() end
function formvalue(n, e) return context.request:formvalue(n, e) end
function getcookie(e) return context.request:getcookie(e) end
function getenv(e)
    if context and context.request then return context.request:getenv(e) end
    return nil
end
function setfilehandler(e) return context.request:setfilehandler(e) end
function header(n, t)
    if not context.headers then context.headers = {} end
    context.headers[n:lower()] = t
    e.yield(2, n, t)
end
function prepare_content(e)
    if not context.headers or not context.headers["content-type"] then
        if e == "application/xhtml+xml" then
            if not getenv("HTTP_ACCEPT") or
                not getenv("HTTP_ACCEPT"):find("application/xhtml+xml", nil,
                                               true) then
                e = "text/html; charset=UTF-8"
            end
            header("Vary", "Accept")
        end
        header("Content-Type", e)
    end
end
function source() return context.request.input end
function status(n, t)
    n = n or 200
    t = t or "OK"
    context.status = n
    e.yield(1, n, t)
end
function flush()
    if n > 0 then
        e.yield(4, d.concat(t))
        t = {}
        n = 0
    end
end
function flush_header()
    if not context.status then status() end
    if not context.headers or not context.headers["content-type"] then
        header("Content-Type", "text/html; charset=utf-8")
    end
    if not context.headers["cache-control"] then
        header("Cache-Control", "no-cache")
        header("Expires", "0")
    end
    header("X-Frame-Options", "SAMEORIGIN")
    context.eoh = true
    e.yield(3)
end
function write(e, r)
    if not e then
        if r then
            flush()
            f(r)
        else
            close()
        end
        return true
    else
        local r = #e
        if r == 0 then return true end
        t[#t + 1] = e
        n = n + r
        if n > l then
            if not context.eoh then flush_header() end
            flush()
        end
        return true
    end
end
function splice(t, n) e.yield(6, t, n) end
function redirect(e)
    status(302, "Found")
    header("Location", e)
    close()
end
function build_querystring(n)
    local e = {"?"}
    for t, n in u(n) do
        if #e > 1 then e[#e + 1] = "&" end
        e[#e + 1] = urldecode(t)
        e[#e + 1] = "="
        e[#e + 1] = urldecode(n)
    end
    return d.concat(e, "")
end
urldecode = r.urldecode
urlencode = r.urlencode
function write_json(e, n)
    if e == nil then
        write("null")
    elseif i(e) == "table" then
        local t, t
        if #e == 0 and o(e) then
            write("{")
            for t, r in u(e) do
                write("%q:" % t)
                write_json(r, n)
                if o(e, t) then write(",") end
            end
            write("}")
        else
            write("[")
            for t, r in c(e) do
                write_json(r, n)
                if o(e, t) then write(",") end
            end
            write("]")
        end
    elseif i(e) == "number" or i(e) == "boolean" then
        if (e ~= e) then
            write("Number.NaN")
        else
            write(a(e))
        end
    else
        local e = e
        if n then e = n(e) end
        write('"%s"' % e)
    end
end
