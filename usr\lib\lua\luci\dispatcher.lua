local s = require "nixio.fs"
local T = require "luci.sys"
local e = require "luci.init"
local r = require "luci.util"
local o = require "luci.http"
require("luci.template")
local a = require "nixio", require "nixio.util"
local i = require("luci.websys")
local n = require("luci.controller.ds")
local e = require("luci.torchlight.error")
local h = require("luci.torchlight.setting")
local b = require("luci.torchlight.model")
local u = require("luci.torchlight.util")
local I = require("luci.torchlight.validator")
local l = require("luci.torchlight.import_export")
local p = require("luci.model.uci")
local v = require "luci.sauth"
local y = require("luci.fs")
local g = require("luci.model.log")
local x = require("luci.model.userconfig")
local c = require("luci.ip")
local k = require("luci.torchlight.debug")
local E = require("socket")
module("luci.dispatcher", package.seeall)
context = r.threadlocal()
p = require "luci.model.uci"
_M.fs = s
authenticator = {}
HTTP_CLIENT_FULL = 0
HTTP_CLIENT_LOCK = 1
HTTP_CLIENT_TIMEOUT = 2
HTTP_CLIENT_PSWERR = 3
HTTP_CLIENT_NORMAL = 4
HTTP_CLIENT_INVALID = 5
HTTP_CLIENT_PSWIlegal = 6
KEY_CODE = "code"
KEY_GROUP = "group"
KEY_TIME = "time"
local m = 100
local N = 15801
local w = 15803
local A = 15804
encryppath = "/tmp/luci-encryptpath"
index_lock_file = "/tmp/luci-index-locked"
extend_index_lock_file = "/tmp/luci-extend-index-locked"
index_lock_check_interval = .3
index_lock_timeout = 10
extend_indexcache = "/tmp/luci-extend-indexcache"
NO_POSTFIX_URL = ""
local d = nil
local f
CB_KEY_TYPE_NAME = "name"
CB_KEY_TYPE_TYPE = "type"
ACCOUNT_UCI = "administrator"
ACCOUNT_SEC = "account"
WEBPASSWD_UCI = "webpasswd"
SECURITY_SEC = "security"
local t = nil
CI_IS_PC = "is_pc"
client_info = {}
CFG_SEC_SEP = ":"
function create_dataindex()
    if dataindexcache then
        local e = s.stat(dataindexcache, "mtime")
        if e then
            local e = loadfile(dataindexcache)
            if type(e) == "function" then
                t = e()
                if t ~= nil then return t end
            end
        end
    end
    local l = luci.util.libpath() .. "/data/"
    local i = {".lua", ".lua.gz"}
    local e = {}
    for r, n in ipairs(i) do
        a.util.consume((s.glob(l .. "*" .. n)), e)
        a.util.consume((s.glob(l .. "*/*" .. n)), e)
    end
    t = {}
    for e, n in ipairs(e) do
        local e = "luci.data." .. n:sub(#l + 1, #n):gsub("/", ".")
        for t, n in ipairs(i) do e = e:gsub(n .. "$", "") end
        local l = require(e)
        assert(l ~= true, "Invalid dataer file found\n" .. "The file '" .. n ..
                   "' contains an invalid module line.\n" ..
                   "Please verify whether the module name is set to '" .. e ..
                   "' - It must correspond to the file path!")
        local l = l.index
        assert(type(l) == "function",
               "Invalid dataer file found\n" .. "The file '" .. n ..
                   "' contains no index() function.\n" ..
                   "Please make sure that the controller contains a valid " ..
                   "index function and verify the spelling!")
        t[e] = l
    end
    if dataindexcache then
        local e = a.open(dataindexcache, "w", 600)
        e:writeall(r.get_bytecode(t))
        e:close()
    end
end
function create_datacbs()
    if not t then create_dataindex() end
    local e = context
    e.datacbs = {}
    local e = setmetatable({}, {__index = luci.dispatcher})
    for t, n in pairs(t) do
        e._NAME = t
        setfenv(n, e)
        n()
    end
    return datacbs
end
function dataentry(n, e, t, l, r)
    local i = n .. CFG_SEC_SEP .. e
    local n = getfenv(2)._NAME
    local e = context.datacbs[i] or
                  {["chkfunc"] = {}, ["srvfunc"] = {}, ["beforesavefunc"] = {}}
    if t ~= nil then e.chkfunc[n] = t end
    if l ~= nil then e.srvfunc[n] = l end
    if r ~= nil then e.beforesavefunc[n] = r end
    context.datacbs[i] = e
    return e
end
function cbentry(n, e, l, r, o, i, t)
    if type(n) ~= "string" or type(e) ~= "string" or type(l) ~= "string" then
        return nil
    end
    context.datacbs = context.datacbs or {}
    local c = context.datacbs[n] or {}
    context.datacbs[n] = c
    local a = c[e] or {}
    c[e] = a or {}
    local n = getfenv(3)._NAME
    local e = a[l] or
                  {["chkfunc"] = {}, ["srvfunc"] = {}, ["beforesavefunc"] = {}}
    a[l] = e
    if r ~= nil then
        if t ~= nil then
            e.chkfunc[t] = {module = n, func = r}
        else
            table.insert(e.chkfunc, {module = n, func = r})
        end
    end
    if o ~= nil then
        if t ~= nil then
            e.srvfunc[t] = {module = n, func = o}
        else
            table.insert(e.srvfunc, {module = n, func = o})
        end
    end
    if i ~= nil then
        if t ~= nil then
            e.beforesavefunc[t] = {module = n, func = i}
        else
            table.insert(e.beforesavefunc, {module = n, func = i})
        end
    end
    return e
end
function register_secname_cb(l, t, n, r, i, e)
    return cbentry(l, CB_KEY_TYPE_NAME, t, n, r, i, e)
end
function register_sectype_cb(r, i, l, e, n, t)
    return cbentry(r, CB_KEY_TYPE_TYPE, i, l, e, n, t)
end
register_keyword_filter_options_data = n.register_keyword_filter_options_data
register_sectype_filter = n.register_sectype_filter
register_secname_filter = n.register_secname_filter
register_keyword_action = n.register_keyword_action
register_sectype_userlist = n.register_sectype_userlist
register_secname_userlist = n.register_secname_userlist
register_keyword_data = n.register_keyword_data
register_keyword_count = n.register_keyword_count
register_keyword_set_data = n.register_keyword_set_data
register_keyword_del_data = n.register_keyword_del_data
register_keyword_add_data = n.register_keyword_add_data
register_secname_force_return = n.register_secname_force_return
register_sectype_force_return = n.register_sectype_force_return
register_module = n.register_module
register_uci_option_type = n.register_uci_option_type
register_json_datatype = n.register_json_datatype
register_sectype_limit = n.register_sectype_limit
register_module_spec_info = n.register_module_spec_info
register_import_pre_callback = l.register_import_pre_callback
register_import_export = l.register_import_export
import_handler = l.import_handler
export_handler = l.export_handler
register_ds_pre_callback = n.register_ds_pre_callback
function build_url(...)
    local t = {...}
    local e = {o.getenv("SCRIPT_NAME") or ""}
    local n, n
    for n, t in pairs(context.urltoken) do
        e[#e + 1] = "/"
        e[#e + 1] = o.urlencode(n)
        e[#e + 1] = "="
        e[#e + 1] = o.urlencode(t)
    end
    local n
    for t, n in ipairs(t) do
        if n:match("^[a-zA-Z0-9_%-%.%%/,;]+$") then
            e[#e + 1] = "/"
            e[#e + 1] = n
        end
    end
    return table.concat(e, "")
end
function node_visible(e)
    if e then
        return not ((not e.title or #e.title == 0) or
                   (not e.target or e.hidden == true) or
                   (type(e.target) == "table" and e.target.type == "firstchild" and
                       (type(e.nodes) ~= "table" or not next(e.nodes))))
    end
    return false
end
function node_childs(e)
    local n = {}
    if e then
        local t, t
        for t, e in r.spairs(e.nodes, function(t, n)
            return (e.nodes[t].order or 100) < (e.nodes[n].order or 100)
        end) do if node_visible(e) then n[#n + 1] = t end end
    end
    return n
end
function error404(e)
    luci.http.status(404, "Not Found")
    e = e or "Not Found"
    require("luci.template")
    if not luci.util.copcall(luci.template.render, "error404") then
        luci.http.prepare_content("text/plain")
        luci.http.write(e)
    end
    return false
end
function error500(e)
    luci.util.perror(e)
    if not context.template_header_sent then
        luci.http.status(500, "Internal Server Error")
        luci.http.prepare_content("text/plain")
        luci.http.write(e)
    else
        require("luci.template")
        if not luci.util
            .copcall(luci.template.render, "error500", {message = e}) then
            luci.http.prepare_content("text/plain")
            luci.http.write(e)
        end
    end
    return false
end
function write_json(e)
    o.prepare_content("application/json")
    o.write_json(e, urlencode)
end
function authenticator.htmlauth(n, n, n)
    local n = {}
    local l = o.getenv("PATH_INFO") or ""
    l = string.gsub(l, "^[/]*", "")
    local a = h.DEFAULT_USER
    local r = e.EUNAUTH
    local t = string.upper(o.getenv("REQUEST_METHOD"))
    if "GET" == t and u.is_apmng_detection_enable() then
        u.set_apmng_detection_disable()
    end
    if "GET" == t and "" == l then
        luci.template.render("admin/Index")
        return false
    end
    local t = luci.http.getenv("REMOTE_ADDR") or "noip"
    if c.IPv4(t) ~= nil then
        t = string.gsub(t, "%.", "_")
    elseif c.IPv6(t) ~= nil then
        t = c.IPv6(t):string()
        t = string.gsub(t, ":", "_")
    else
        t = string.gsub(t, "%.", "_")
    end
    local d = require("luci.json")
    local c = require("luci.http.protocol")
    local t = o.jsondata()
    t = t or d.decode(o.get_raw_data() or "", c.urldecode) or {}
    if t["query_auth_log"] then
        if t.method ~= "do" then
            n[e.NAME] = e.EINVARG
            write_json(n)
            return false
        end
        return action_get_unauth_log(t["query_auth_log"])
    end
    if t["get_domain_array"] then
        if t.method ~= "do" then
            n[e.NAME] = e.EINVARG
            write_json(n)
            return false
        end
        return action_get_domain_array(t["get_domain_array"])
    end
    local o, c = i.is_locked()
    if o then
        n[e.NAME] = e.EUNAUTH
        n.data = get_unauth_data(c)
        write_json(n)
        return false
    end
    if "" ~= l then
        luci.http.redirect("/")
        return false
    end
    if t.login then
        if t.method ~= "do" then
            n[e.NAME] = e.EINVARG
            write_json(n)
            return false
        end
        return action_login(t.login)
    end
    if t["set_password"] then
        if t.method ~= "do" then
            n[e.NAME] = e.EINVARG
            write_json(n)
            return false
        end
        return action_set_pwd(t["set_password"])
    end
    n[e.NAME] = e.EUNAUTH
    local t, l = i.is_locked()
    if t then
        r = l
    else
        r = i.has_webpasswd_user(a) and e.ESYSCLIENTNORMAL or e.ESYSRESET
    end
    n.data = get_unauth_data(r)
    write_json(n)
    return false
end
function get_unauth_data(l, n)
    local e = require("luci.fs")
    local e = {}
    o.status(401)
    local t = require("luci.torchlight.util")
    e[KEY_GROUP] = t.get_user_group()
    e[KEY_CODE] = l
    if n then e[KEY_TIME] = n end
    return e
end
function clear_ip_pwd_err(t, e)
    if y.isfile(e) then
        local n = loadfile(e)()
        if type(n) ~= "table" then return false end
        n[t] = nil
        local e = a.open(e, "w", 600)
        e:writeall(r.get_bytecode(n))
        e:close()
    end
    return true
end
function get_authuser()
    local n = p.cursor()
    local e = {}
    n:foreach(WEBPASSWD_UCI, SECURITY_SEC,
              function(n) if n[".name"] then e[#e + 1] = n[".name"] end end)
    if 0 == #e then
        return h.DEFAULT_USER
    else
        return e
    end
end
function find_user_by_username(r)
    local t = p.cursor()
    local e = false
    local n = nil
    t:foreach(WEBPASSWD_UCI, SECURITY_SEC, function(t)
        local l = t["username"]
        if not e and nil ~= l and r == l then
            n = t[".name"]
            e = true
            return
        end
    end)
    if not e or nil == n then n = h.DEFAULT_USER end
    return e, n
end
function find_role_by_user(e)
    if nil == e then return "" end
    local n = p.cursor()
    local e = n:get(ACCOUNT_UCI, e, "role_name") or ""
    return e
end
local function h(l, n)
    local e = require("luci.torchlight.event_api")
    e.event_trigger(e.EVENT_LOGIN, n)
    local t = require("luci.model.nms_report_api")
    if nil ~= t then
        local e = {}
        e["userName"] = n
        local r = require("luci.torchlight.nms_event_id")
        if nil ~= u then
            local n = u.get_http_src_ip()
            if nil ~= n and "noip" ~= n then e["sourceIp"] = n end
        end
        if nil ~= l then e["errorCode"] = l end
        t.notifyNmsReportEvent(r.NMS_EVENT_ID_LOG_IN, nil, e)
    end
end
function action_login(n)
    local t = {}
    local u = n.password or ""
    local l = n.username or ""
    local d, r = find_user_by_username(l)
    if not i.has_webpasswd_user(r) then
        h(1, l)
        t[e.NAME] = e.EUNAUTH
        t.data = get_unauth_data(e.ESYSRESET)
        write_json(t)
        return false
    end
    local n = require("luci.fs")
    local a = i.get_pwd_err_path()
    local n = luci.http.getenv("REMOTE_ADDR") or "noip"
    local o = n
    if c.IPv4(n) ~= nil then
        n = string.gsub(n, "%.", "_")
    elseif c.IPv6(n) ~= nil then
        n = c.IPv6(n):string()
        n = string.gsub(n, ":", "_")
    else
        n = string.gsub(n, "%.", "_")
    end
    local c, s = i.is_locked()
    if c then
        t[e.NAME] = e.EUNAUTH
        t.data = get_unauth_data(s)
        write_json(t)
        return false
    end
    if r and i.check_webpasswd(r, l, u) then
        clear_ip_pwd_err(n, a)
        g.logger_reg(m, N, l .. "(IP:" .. o .. ")")
        h(0, l)
        return r
    end
    require("luci.template")
    context.path = {}
    local n = i.log_errinfo()
    t[e.NAME] = e.EUNAUTH
    local e = e.EUNAUTH
    local r, a = i.is_locked()
    if r then
        h(3, l)
        e = a
        g.logger_reg(m, A, l .. "(IP:" .. o .. ")")
    else
        if false == d then
            h(5, l)
        else
            h(2, l)
        end
    end
    g.logger_reg(m, w, l .. "(IP:" .. o .. ")")
    t.data = get_unauth_data(e)
    t.data.time = i.MAX_LOGIN_TIME - n.count
    write_json(t)
    return false
end
function action_set_pwd(l)
    local t = {}
    local a = require("luci.torchlight.validator")
    local n = require("luci.torchlight.setting")
    local r = l.password or ""
    local d = l.username or ""
    local l = e.ENONE
    o.prepare_content("application/json")
    if i.changed_passwd(n.DEFAULT_USER) then
        t[e.NAME] = e.EHASINITPWD
        write_json(t)
        return false
    end
    l = a.check_passwd(r)
    if e.ENONE ~= l then
        t[e.NAME] = l
        write_json(t)
        return false
    end
    if not i.set_webpasswd(n.DEFAULT_USER, d, r, true) then
        t[e.NAME] = e.EEXPT
        write_json(t)
        return false
    end
    local r = n.DEFAULT_USER
    local l = luci.sys.uniqueid(16)
    local n = luci.http.getenv("REMOTE_ADDR")
    if c.IPv4(n) ~= nil then
        n = string.gsub(n, "%.", "_")
    elseif c.IPv6(n) ~= nil then
        n = c.IPv6(n):string()
        n = string.gsub(n, ":", "_")
    else
        n = string.gsub(n, "%.", "_")
    end
    v.write(n, {user = r, token = l})
    t[e.NAME] = e.ENONE
    t["stok"] = l
    write_json(t)
    return false
end
function action_get_unauth_log(n)
    local o = {}
    local d = i.get_pwd_err_path()
    local s = {}
    local u = {}
    local a = {}
    local n = {}
    local l = 0
    local t = nil
    local r = e.ENONE
    local _, f = i.is_locked()
    if _ then r = f end
    function _add_record(t, e)
        u = e.record or {}
        for r, e in pairs(u) do
            l = l + 1
            a = {}
            n = {}
            a["unauth_log_list_" .. tostring(l)] = n
            n.ip = string.gsub(t, "_", "%.")
            if c.IPv4(n.ip) == nil then
                n.ip = string.gsub(n.ip, "%.", ":")
            end
            n.mac = e.mac
            n.time = e.time
            s[l] = a
        end
    end
    if y.isfile(d) then
        local n = loadfile(d)()
        if e.ESYSLOCKED == r then
            local e = luci.http.getenv("REMOTE_ADDR")
            if c.IPv4(e) ~= nil then
                t = string.gsub(e, "%.", "_")
            elseif c.IPv6(e) ~= nil then
                t = c.IPv6(e):string()
                t = string.gsub(e, ":", "_")
            else
                t = string.gsub(e, "%.", "_")
            end
            local e = n[t] or {}
            _add_record(t, e)
        else
            for t, n in pairs(n) do
                if (e.ESYSLOCKEDFOREVER == r) or
                    ((n.atime and n.atime + i.LOCK_TIME > T.uptime()) and
                        (n.count and n.count >= i.MAX_LOGIN_TIME)) then
                    _add_record(t, n)
                end
            end
        end
    end
    o[e.NAME] = r
    o["unauth_log_list"] = s
    write_json(o)
    return false
end
function action_get_domain_array(n)
    local _ = "name"
    local f = "ip"
    local h = "mac"
    local g = "type"
    local x = "0"
    local m = "1"
    local E = "2"
    local d = "domain_array"
    local i = p.cursor()
    local r = b.uciDeviceInfo
    local t = b.uciNetwork
    local a = {}
    local l = {}
    local u = u.get_dev_discover()
    local o = {}
    local n = {}
    local p = i:get(r.fileName, r.secName.info, r.optName.domainName)
    local c = i:get(t.fileName, t.secName.lan, t.optName.mac)
    c = string.gsub(c, ":", "-")
    local s = i:get(t.fileName, t.secName.lan, t.optName.ip)
    local t = i:get(t.fileName, t.secName.lan, t.optName.netmask)
    o[d .. "_" .. (#l + 1)] = n
    n[_] = i:get(r.fileName, r.secName.info, r.optName.deviceName)
    n[f] = s
    n[h] = c
    n[g] = x
    l[#l + 1] = o
    for r, e in pairs(u) do
        if e.domain == p then
            o = {}
            n = {}
            o[d .. "_" .. (#l + 1)] = n
            n[_] = e.alias ~= "" and e.alias or e.dev_model
            n[f] = e.ipaddr
            n[h] = e.macaddr
            n[g] = I.is_same_network(s, t, e.ipaddr) and m or E
            l[#l + 1] = o
        end
    end
    a[e.NAME] = e.ENONE
    a[d] = l
    write_json(a)
    return false
end
function httpdispatch(n, t)
    luci.http.context.request = n
    local i = true
    local e = {}
    context.request = e
    context.urltoken = {}
    local n = o.urldecode(n:getenv("PATH_INFO") or "", true)
    -- 解析url路径
    if t then for t, n in ipairs(t) do e[#e + 1] = n end end
    local t = true
    for l in n:gmatch("[^/]+") do
        local n, r
        if t then n, r = l:match("^(%w+)=([a-fA-F0-9]*)") end
        if n then
            context.urltoken[n] = r
        else
            t = false
            e[#e + 1] = l
            i = false
        end
    end
    -- 调用主分发函数
    local e, e = r.coxpcall(function()
        x.set_user_modify_trigger()
        dispatch(context.request, i)
        x.check_cfg_trigger_modify()
    end, error500)
    luci.http.close()
end

function dispatch(a, p)
    local t = context
    t.path = a
    local n = require "luci.config"
    assert(n.main,
           "/etc/config/luci seems to be corrupt, unable to find section 'main'")
    if not t.datacbs then create_datacbs() end
    handle_client_info()
    local n = t.tree
    local l
    if not n then n = createtree() end
    local l = {}
    local d = {}
    t.args = d
    t.requestargs = t.requestargs or d
    local h
    local _ = t.urltoken
    local f = {}
    local _ = {}
    if n then r.update(l, n) end
    for t, e in ipairs(a) do
        f[#f + 1] = e
        _[#_ + 1] = e
        n = n.nodes[e]
        h = t
        if not n then break end
        r.update(l, n)
        if n.leaf then break end
    end
    if n and n.leaf then
        for e = h + 1, #a do
            d[#d + 1] = a[e]
            _[#_ + 1] = a[e]
        end
    end
    if n and n.userlist then
        local t = require("luci.torchlight.util")
        local l = t.get_user_group()
        if t.index(n.userlist, l) == nil then
            local n = {}
            n[e.NAME] = e.EFORBID
            write_json(n)
            return
        end
    end
    t.requestpath = t.requestpath or _
    t.path = f
    if (n and n.index) or not l.notemplate then
        local n = require("luci.template")
        local i = l.mediaurlbase or luci.config.main.mediaurlbase
        local function l(t, e, l)
            if t then
                local n = getfenv(3)
                local t = (type(n.self) == "table") and n.self
                return string.format(' %s="%s"', tostring(e),
                                     luci.util
                                         .pcdata(
                                         tostring(
                                             l or
                                                 (type(n[e]) ~= "function" and
                                                     n[e]) or
                                                 (t and type(t[e]) ~= "function" and
                                                     t[e]) or "")))
            else
                return ''
            end
        end
        n.context.viewns = setmetatable({
            write = luci.http.write,
            include = function(e) n.Template(e):render(getfenv(2)) end,
            export = function(e, t)
                if n.context.viewns[e] == nil then
                    n.context.viewns[e] = t
                end
            end,
            striptags = r.striptags,
            pcdata = r.pcdata,
            media = i,
            theme = s.basename(i),
            resource = luci.config.main.resourcebase,
            ifattr = function(...) return l(...) end,
            attr = function(...) return l(true, ...) end
        }, {
            __index = function(n, e)
                if e == "controller" then
                    return build_url()
                elseif e == "REQUEST_URI" then
                    return build_url(unpack(t.requestpath))
                elseif e == "NO_POSTFIX_URL" then
                    return NO_POSTFIX_URL
                else
                    return rawget(n, e) or _G[e]
                end
            end
        })
    end
    l.dependent = (l.dependent ~= false)
    assert(not l.dependent or not l.auto,
           "Access Violation\nThe page at '" .. table.concat(a, "/") .. "/' " ..
               "has no parent node so the access to this location has been denied.\n" ..
               "This is a software bug, please report this message at " ..
               "http://luci.subsignal.org/trac/newticket")
    if o.getenv("REQUEST_METHOD") == "GET" and u.is_apmng_detection_enable() then
        u.set_apmng_detection_disable()
    end
    if p and o.getenv("REQUEST_METHOD") == "GET" then
        luci.template.render("admin/Index")
        return
    end
    if l.sysauth then
        local a = require "luci.sauth"
        local d = type(l.sysauth_authenticator) == "function" and
                      l.sysauth_authenticator or
                      authenticator[l.sysauth_authenticator]
        local _ = (type(l.sysauth) == "string") and l.sysauth
        local u = _ and {l.sysauth} or l.sysauth
        local s = t.authsession
        local f = false
        local n = luci.http.getenv("REMOTE_ADDR")
        if c.IPv4(n) ~= nil then
            n = string.gsub(n, "%.", "_")
        elseif c.IPv6(n) ~= nil then
            n = c.IPv6(n):string()
            n = string.gsub(n, ":", "_")
        else
            n = string.gsub(n, "%.", "_")
        end
        if not s then
            s = t.urltoken.stok
            f = true
        end
        local l = a.read(n)
        local c
        if l then
            if not f or t.urltoken.stok == l.token then
                local t = {}
                local o, r = i.is_locked()
                if o then
                    t[e.NAME] = e.EUNAUTH
                    t.data = get_unauth_data(r)
                    write_json(t)
                    return
                end
                c = l.user
                local e = i.get_pwd_err_path()
                clear_ip_pwd_err(n, e)
            end
        else
            local e = o.getenv("HTTP_AUTH_USER")
            local n = o.getenv("HTTP_AUTH_PASS")
            if e and n and luci.sys.user.checkpasswd(e, n) then
                d = function() return e end
            end
        end
        if (l and t.urltoken.stok and t.urltoken.stok ~= l.token) or
            (not l and t.urltoken.stok) then
            local n = {}
            local t = e.EUNAUTH
            n[e.NAME] = e.EUNAUTH
            local e, l = i.is_locked()
            if e then
                t = l
                n.data = get_unauth_data(t)
            else
                n.data = get_unauth_data(t)
            end
            write_json(n)
            return
        end
        if not r.contains(u, c) then
            if d then
                t.urltoken.stok = nil
                local i, o = d(luci.sys.user.checkpasswd, u, _)
                if not i or not r.contains(u, i) then
                    return
                else
                    local l = l and l.token or luci.sys.uniqueid(16)
                    if not o then
                        a.reap()
                        a.clear_sessions()
                        a.write(n, {user = i, token = l})
                        t.urltoken.stok = l
                    end
                    t.authsession = l
                    t.authuser = i
                    local n = {}
                    n[e.NAME] = e.ENONE
                    n["stok"] = l
                    n["role"] = find_role_by_user(i)
                    write_json(n)
                    return
                end
            else
                luci.http.status(403, "Forbidden")
                return
            end
        else
            t.authsession = s
            t.authuser = c
        end
    end
    if l.setgroup then luci.sys.process.setgroup(l.setgroup) end
    if l.setuser then luci.sys.process.setuser(l.setuser) end
    local e = nil
    if n then
        if type(n.target) == "function" then
            e = n.target
        elseif type(n.target) == "table" then
            e = n.target.target
        end
    end
    if n and (n.index or type(e) == "function") then
        t.dispatched = n
        t.requested = t.requested or t.dispatched
    end
    if n and n.index then
        local e = require "luci.template"
        if r.copcall(e.render, "indexer", {}) then return true end
    end
    if type(e) == "function" then
        r.copcall(function()
            local t = getfenv(e)
            local n = require(n.module)
            r.update(t, n)
            setfenv(e, t)
        end)
        local t, l
        if type(n.target) == "table" then
            t, l = r.copcall(e, n.target, unpack(d))
        else
            t, l = r.copcall(e, unpack(d))
        end
        assert(t, "Failed to execute " ..
                   (type(n.target) == "function" and "function" or n.target.type or
                       "unknown") .. " dispatcher target for entry '/" ..
                   table.concat(a, "/") .. "'.\n" ..
                   "The called action terminated with an exception:\n" ..
                   tostring(l or "(unknown)"))
    else
        local e = node()
        if not e or not e.target then
            error404(
                "No root node was registered, this usually happens if no module was installed.\n" ..
                    "Install luci-mod-admin-full and retry. " ..
                    "If the module is already installed, try removing the /tmp/luci-indexcache file.")
        else
            error404("URL /" .. table.concat(a, "/") .. " Not Found")
        end
    end
end
function createindex()
    local n = luci.util.libpath() .. "/controller/"
    local e = {".lua", ".lua.gz"}
    if luci.util.copcall(require, "luci.fastindex") then
        createindex_fastindex(n, e)
    else
        createindex_plain(n, e)
    end
end
function createindex_fastindex(n, e)
    d = {}
    if not f then
        f = luci.fastindex.new("index")
        for t, e in ipairs(e) do
            f.add(n .. "*" .. e)
            f.add(n .. "*/*" .. e)
        end
    end
    f.scan()
    for n, e in pairs(f.indexes) do d[e[2]] = e[1] end
end
function createindex_plain(t, l)
    local e = 0
    while a.fs.access(index_lock_file) ~= nil do
        E.sleep(index_lock_check_interval)
        e = e + index_lock_check_interval
        if e >= index_lock_timeout then a.fs.unlink(index_lock_file) end
    end
    if indexcache then
        local e = s.stat(indexcache, "mtime")
        if e then
            local e = loadfile(indexcache)
            if type(e) == "function" then
                d = e()
                if d ~= nil then return d end
            end
        end
    end
    local e = io.open(index_lock_file, "w")
    e:close()
    local e = {}
    for l, n in ipairs(l) do
        a.util.consume((s.glob(t .. "*" .. n)), e)
        a.util.consume((s.glob(t .. "*/*" .. n)), e)
    end
    d = {}
    for e, n in ipairs(e) do
        local e = "luci.controller." .. n:sub(#t + 1, #n):gsub("/", ".")
        for t, n in ipairs(l) do e = e:gsub(n .. "$", "") end
        local t = require(e)
        if t == true then
            a.fs.unlink(index_lock_file)
            assert(false,
                   "Invalid controller file found\n" .. "The file '" .. n ..
                       "' contains an invalid module line.\n" ..
                       "Please verify whether the module name is set to '" .. e ..
                       "' - It must correspond to the file path!")
        end
        local t = t.index
        if type(t) ~= "function" then
            a.fs.unlink(index_lock_file)
            assert(false,
                   "Invalid controller file found\n" .. "The file '" .. n ..
                       "' contains no index() function.\n" ..
                       "Please make sure that the controller contains a valid " ..
                       "index function and verify the spelling!")
        end
        d[e] = t
    end
    if indexcache then
        local e = a.open(indexcache, "w", 600)
        e:writeall(r.get_bytecode(d))
        e:close()
    end
    a.fs.unlink(index_lock_file)
end
local t = {}
function register_index_extend(e)
    if "string" ~= type(e) then return false end
    if t[e] then
        k("Index name [" .. e .. "] has been registered.")
        return false
    end
    t[e] = true
    return true
end
function load_extend_index_cache()
    local e = 0
    while a.fs.access(extend_index_lock_file) ~= nil do
        E.sleep(index_lock_check_interval)
        e = e + index_lock_check_interval
        if e >= index_lock_timeout then
            a.fs.unlink(extend_index_lock_file)
        end
    end
    if extend_indexcache then
        local e = s.stat(extend_indexcache, "mtime")
        if e then
            local e = loadfile(extend_indexcache)
            if type(e) == "function" then
                d = e()
                if d ~= nil then return d end
            end
        end
    end
end
function store_extend_index_cache(n)
    local e = io.open(extend_index_lock_file, "w")
    e:close()
    if extend_indexcache then
        local e = a.open(extend_indexcache, "w", 600)
        e:writeall(r.get_bytecode(n))
        e:close()
    end
    a.fs.unlink(extend_index_lock_file)
end
function createtree()
    if not d then createindex() end
    local e = context
    local i = {nodes = {}, inreq = true}
    local l = {}
    e.treecache = setmetatable({}, {__mode = "v"})
    e.tree = i
    e.modifiers = l
    local e = setmetatable({}, {__index = luci.dispatcher})
    for t, n in pairs(d) do
        e._NAME = t
        setfenv(n, e)
        n()
    end
    if nil ~= next(t) then
        local n = load_extend_index_cache()
        if not n then
            n = {}
            for e, l in pairs(d) do
                n[e] = {}
                local r = require(e)
                for l, t in pairs(t) do
                    local t = r[l]
                    if "function" == type(t) then n[e][l] = t end
                end
            end
            store_extend_index_cache(n)
        end
        for n, t in pairs(n) do
            e._NAME = n
            for t, n in pairs(t) do
                setfenv(n, e)
                n()
            end
        end
    end
    local function t(n, e) return l[n].order < l[e].order end
    for t, n in r.spairs(l, t) do
        e._NAME = n.module
        setfenv(n.func, e)
        n.func()
    end
    return i
end
function modifier(n, e)
    context.modifiers[#context.modifiers + 1] = {
        func = n,
        order = e or 0,
        module = getfenv(2)._NAME
    }
end
function assign(e, n, l, t)
    local e = node(unpack(e))
    e.nodes = nil
    e.module = nil
    e.title = l
    e.order = t
    setmetatable(e, {__index = _create_node(n)})
    return e
end
function entry(e, r, l, t, n)
    local e = node(unpack(e))
    e.target = r
    e.title = l
    e.order = t
    e.module = getfenv(2)._NAME
    e.userlist = n
    return e
end
function get(...) return _create_node({...}) end
function node(...)
    local e = _create_node({...})
    e.module = getfenv(2)._NAME
    e.auto = nil
    return e
end
function _create_node(n)
    if #n == 0 then return context.tree end
    local t = table.concat(n, ".")
    local e = context.treecache[t]
    if not e then
        local l = table.remove(n)
        local r = _create_node(n)
        e = {nodes = {}, auto = true}
        if r.inreq and context.path[#n + 1] == l then e.inreq = true end
        r.nodes[l] = e
        context.treecache[t] = e
    end
    return e
end
function _firstchild()
    local t = {unpack(context.path)}
    local e = table.concat(t, ".")
    local n = context.treecache[e]
    local e
    if n and n.nodes and next(n.nodes) then
        local t, t
        for t, l in pairs(n.nodes) do
            if not e or (l.order or 100) < (n.nodes[e].order or 100) then
                e = t
            end
        end
    end
    assert(e ~= nil,
           "The requested node contains no childs, unable to redispatch")
    t[#t + 1] = e
    dispatch(t)
end
function firstchild() return {type = "firstchild", target = _firstchild} end
function alias(...)
    local e = {...}
    return function(...)
        for t, n in ipairs({...}) do e[#e + 1] = n end
        dispatch(e)
    end
end
function rewrite(t, ...)
    local n = {...}
    return function(...)
        local e = r.clone(context.dispatched)
        for n = 1, t do table.remove(e, 1) end
        for n, t in ipairs(n) do table.insert(e, n, t) end
        for t, n in ipairs({...}) do e[#e + 1] = n end
        dispatch(e)
    end
end
local function t(n, ...)
    local e = getfenv()[n.name]
    assert(e ~= nil, 'Cannot resolve function "' .. n.name ..
               '". Is it misspelled or local?')
    assert(type(e) == "function",
           'The symbol "' .. n.name ..
               '" does not refer to a function but data ' .. 'of type "' ..
               type(e) .. '".')
    if #n.argv > 0 then
        return e(unpack(n.argv), ...)
    else
        return e(...)
    end
end
function call(e, ...) return {type = "call", argv = {...}, name = e, target = t} end
local n = function(e, ...) require"luci.template".render(e.view) end
function template(e) return {type = "template", view = e, target = n} end
local function d(e, ...)
    local l = require "luci.cbi"
    local c = require "luci.template"
    local t = require "luci.http"
    local n = e.config or {}
    local l = l.load(e.model, ...)
    local e = nil
    for l, t in ipairs(l) do
        t.flow = n
        local n = t:parse()
        if n and (not e or n < e) then e = n end
    end
    local function r(e)
        return type(e) == "table" and build_url(unpack(e)) or e
    end
    if n.on_valid_to and e and e > 0 and e < 2 then
        t.redirect(r(n.on_valid_to))
        return
    end
    if n.on_changed_to and e and e > 1 then
        t.redirect(r(n.on_changed_to))
        return
    end
    if n.on_success_to and e and e > 0 then
        t.redirect(r(n.on_success_to))
        return
    end
    if n.state_handler then if not n.state_handler(e, l) then return end end
    t.header("X-CBI-State", e or 0)
    if not n.noheader then c.render("cbi/header", {state = e}) end
    local r
    local t
    local a = false
    local o = true
    local i = {}
    for n, e in ipairs(l) do
        if e.apply_needed and e.parsechain then
            local n
            for n, e in ipairs(e.parsechain) do i[#i + 1] = e end
            a = true
        end
        if e.redirect then r = r or e.redirect end
        if e.pageaction == false then o = false end
        if e.message then
            t = t or {}
            t[#t + 1] = e.message
        end
    end
    for n, e in ipairs(l) do
        e:render({
            firstmap = (n == 1),
            applymap = a,
            redirect = r,
            messages = t,
            pageaction = o,
            parsechain = i
        })
    end
    if not n.nofooter then
        c.render("cbi/footer", {
            flow = n,
            pageaction = o,
            redirect = r,
            state = e,
            autoapply = n.autoapply
        })
    end
end
function cbi(e, n) return {type = "cbi", config = n, model = e, target = d} end
local function l(e, ...)
    local t = {...}
    local n = #t > 0 and e.targets[2] or e.targets[1]
    setfenv(n.target, e.env)
    n:target(unpack(t))
end
function arcombine(n, e)
    return {type = "arcombine", env = getfenv(), target = l, targets = {n, e}}
end
local function i(e, ...)
    local n = require "luci.cbi"
    local t = require "luci.template"
    local r = require "luci.http"
    local l = luci.cbi.load(e.model, ...)
    local e = nil
    for t, n in ipairs(l) do
        local n = n:parse()
        if n and (not e or n < e) then e = n end
    end
    r.header("X-CBI-State", e or 0)
    t.render("header")
    for n, e in ipairs(l) do e:render() end
    t.render("footer")
end
function form(e) return {type = "cbi", model = e, target = i} end
function _(e) return e end
function handle_client_info()
    client_info = {}
    return true
end
function get_client_info()
    client_info[CI_IS_PC] = true
    return client_info
end
