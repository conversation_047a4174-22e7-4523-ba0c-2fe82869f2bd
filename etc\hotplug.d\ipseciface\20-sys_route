#!/bin/sh
#author lijun4


#目标子网非全0时，添加系统路由条目到table 20表中
TABLE_NUM="20"
CONFIG_NAME="network"
SECTION_NAME="sys_route"
OPTION_KEY="virtualif"
OPTION_TARGET="target"
OPTION_NETMASK="netmask"
OPTION_METRIIC="metric"

add_sys_route()
{
	section_name=$1
	xfrmi_key=$2
	# 读取当前section的xfrmi接口值，存在变量$section_xfrmi中
	config_get section_xfrmi $section_name $OPTION_KEY
	if [ $section_xfrmi != $xfrmi_key ]; then
		return
	fi
	#local target netmask metric
	config_get target $section_name $OPTION_TARGET
	config_get netmask $section_name $OPTION_NETMASK
	config_get metric $section_name $OPTION_METRIIC

	ret=`ip route add $target/$netmask dev $section_xfrmi metric $metric table $TABLE_NUM 2>&1`
}

. /lib/functions.sh
. /lib/zone/zone_api.sh
. /lib/route/route_api.sh

{
flock -x 120
case "$ACTION" in
		ifup)
		{
			{
				#遍历 所有sys_route section，查找本接口的系统路由配置，并生成系统路由条目
				config_load $CONFIG_NAME
				config_foreach add_sys_route $SECTION_NAME ${XFRMI_IF}
			}
		};;
		ifdown)
			# 直接删除接口后，路由自动消失
			# do nothing
		;;
		ifupdate)
			# do nothing
		;;
esac
flock -u 120
} 120<>/tmp/ipsec_120.lock