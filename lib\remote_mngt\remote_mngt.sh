# Copyight(c) 2008-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     remtoe_mngt.sh
# bief     The Api for remote_mngt
# author   <PERSON><PERSON>
# vesion   1.0.0
# history  1.0.0, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ceate the file

# this is the managable inteface

. /lib/zone/zone_api.sh

export RM_CHAIN="remote_mngt_chain"
export accept_ip="rsa_rule"
export accept_ip_v4="rsa_rule_v4"
export accept_ip_v6="rsa_rule_v6"

is_wan="0"

check_is_nat(){
	if [ "$is_wan" == "1" ];then
		return
	fi

	config_get if $1 if
	config_get name $1 name

	if [ "$if" == "$2" ];then
		is_wan="1"
	else
		is_wan="0"
	fi
}

check_iface_iswan(){
	is_wan="0"
	config_load nat
	config_foreach check_is_nat rule_napt $1
}

get_service_port(){ #$service
	local port http_list https_list ssh_list
	case "$1" in
	http|HTTP|Http)
		port=$(uci get uhttpd.main.listen_http_lan |awk -F ":" '{print $NF}')
		[ -z "$port" ] && port=80
		;;
	https|HTTPS|Https)
		port=$(uci get uhttpd.main.listen_https |awk -F ":" '{print $NF}')
		[ -z "$port" ] && port=443
		;;
	ssh|SSH|Ssh)
		port="$(uci get dropbear.@dropbear[0].Port)"
		;;
	PORTAL)
		port="$(uci get authentication.portal.portal_port)"
		https_portal_port="$(uci get authentication.portal.portal_https_port)"
		port="$port,$https_portal_port,2060,2061"
		;;
	TDDP)
		port="20002"
		;;
	webcli|WEBCLI|WebCli)
		port="$(uci get cli_server.websocket.listen_port)"
		;;
	*)
		port=$(uci get remote_mngt.port.$1)
		;;
	esac

	echo "$port"
}

ipset_add_elem(){ #<ipset> <ip>
	local ret
	ret=$(ipset add "$1" "$2")
	[ -z "$ret" ] && return 0||return 1
}

#$1: iface
get_dev_by_iface(){
	local dev
	local count=0

	dev=`zone_get_effect_devices "$1"`
	while [ -z "$dev" -a "$count" -lt 5 ] ;do
		sleep 1
		dev=`zone_get_effect_devices "$1"`
		[ -n "$dev" ] && break
		let count++

		if [ "$count" -ge 5 ];then
			break
		fi
	done

	echo "$dev"
}

rmc_add_iface_cmd(){ # <chain> <interface> <ports>
	local ret4
	local ret6
	ret4=$(iptables -w -A $1 -i $2 -p tcp -m multiport --dport "$3" -j ACCEPT 2>&1)
	ret6=$(ip6tables -w -A $1 -i $2 -p tcp -m multiport --dport "$3" -j ACCEPT 2>&1)
	[ -z "$ret4" -a -z "$ret6" ] && return 0||return 1
}

rmc_add_reject_iface_cmd(){ # <chain> <interface> <ports>
	local ret4
	local ret6
	ret4=$(iptables -w -A $1 -i $2 -p tcp -m multiport --dport "$3" -j REJECT 2>&1)
	ret6=$(ip6tables -w -A $1 -i $2 -p tcp -m multiport --dport "$3" -j REJECT 2>&1)
	[ -z "$ret4" -a -z "$ret6" ] && return 0||return 1
}

rmc_add_mngt_iface_list(){
	local len iface state service dev serv
	local accept_iface_set
	local ports=$1
	len=$(uci show remote_mngt | grep -i mngt_iface |grep -i "enabled" |wc -l)

	if [ $len -lt 1 ];then
		return 0
	else
		len=$(expr $len - 1)
	fi

	accept_iface_set="remote_mngt_accept_iface_set"

	service="$(uci get remote_mngt.@support[0].service)"

	for i in `seq 0 $len`; do
		state=$(uci get remote_mngt.@mngt_iface[${i}].enabled)
		[ $state == "off" ] && continue

		iface=$(uci get remote_mngt.@mngt_iface[${i}].iface)
		dev=$(get_dev_by_iface "$iface")
		[ $? -ne 0 -a -z "$dev" ] && return 1

		rmc_add_iface_cmd $RM_CHAIN $dev "$ports"
	done

	return 0
}

# 或许可以进行拆分，从而支持IPv6
# 查找ipaddr中是否有"."号
# 从而用于区分IPv4还是IPv6地址
rmc_add_rsa_rule_list(){
	local len ipaddr state ret
	local ipset_used_ipv4=0
	local ipset_used_ipv6=0
	local accept_ip_set_ipv4
	local accept_ip_set_ipv6
	local ports=$1
	local proto
	len=$(uci show system | grep -i remote_rule |grep -i "enabled" |wc -l)

	# ipv4 set
	accept_ip_set_ipv4="remote_mngt_accept_ip_set_ipv4"
	ret=$(ipset destroy "$accept_ip_set_ipv4")
	ret=$(ipset create "$accept_ip_set_ipv4" hash:net -exist 2>&1)

	# ipv6 set
	accept_ip_set_ipv6="remote_mngt_accept_ip_set_ipv6"
	ret=$(ipset destroy "$accept_ip_set_ipv6")
	ret=$(ipset create "$accept_ip_set_ipv6" hash:net family inet6 -exist 2>&1)

	if [ $len -lt 1 ];then
		return 0
	else
		len=$(expr $len - 1)
	fi

	service="$(uci get remote_mngt.@support[0].service)"

	for i in `seq 0 $len`; do
		state=$(uci get system.@remote_rule[${i}].enabled)
		[ $state == "0" ] && continue

		ipaddr=$(uci get system.@remote_rule[${i}].ipaddr)
		[ -z "$ipaddr" ] && continue
		# ipaddr is ipv4 or ipv6
		proto=$(echo $ipaddr | grep :)
		if [[ "$proto" != "" ]]
		then
			proto="ipv6"
		else
			proto="ipv4"
		fi

		case $proto in
		ipv4)
			[ "$ipaddr" == "0.0.0.0/0" ] && {
				ipset_used_ipv4=1
				continue
			}
			`ipset_add_elem "$accept_ip_set_ipv4" "$ipaddr"`
		;;
		ipv6)
			[ "$ipaddr" == "0:0:0:0:0:0:0:0/0" ] && {
				ipset_used_ipv6=1
				continue
			}
			`ipset_add_elem "$accept_ip_set_ipv6" "$ipaddr"`
		;;
		esac
	done

	[ -n "$ports" ] && {
		[ $ipset_used_ipv4 = 0 ] && \
		ret=$(iptables -w -A "$RM_CHAIN" -m set --match-set "$accept_ip_set_ipv4" src -p tcp -m multiport --dport "$ports" -m comment --comment remote_mngt -j ACCEPT) \
		|| ret=$(iptables -w -A "$RM_CHAIN" -p tcp -m multiport --dport "$ports" -m comment --comment remote_mngt -j ACCEPT)
	} && {
		[ $ipset_used_ipv6 = 0 ] && \
		ret=$(ip6tables -w -A "$RM_CHAIN" -m set --match-set "$accept_ip_set_ipv6" src -p tcp -m multiport --dport "$ports" -m comment --comment remote_mngt -j ACCEPT) \
		|| ret=$(ip6tables -w -A "$RM_CHAIN" -p tcp -m multiport --dport "$ports" -m comment --comment remote_mngt -j ACCEPT)
	}
	[ $? -ne 0 ] && return 1

	return 0
}

rmc_add_reject_iface_list(){
	local len iface port state service dev
	local ports=$1
	len=$(uci show remote_mngt | grep -i mngt_iface |grep -i "enabled" | wc -l)

	if [ $len -lt 1 ];then
		return 0
	else
		len=$(expr $len - 1)
	fi

	service="$(uci get remote_mngt.@support[0].service)"

	for i in `seq 0 $len`; do
		state=$(uci get remote_mngt.@mngt_iface[${i}].enabled)
		[ $state == "on" ] && continue
		iface=$(uci get remote_mngt.@mngt_iface[${i}].iface)
		dev=$(get_dev_by_iface "$iface")
		[ $? -ne 0 -a -z "$dev" ] && return 1
		[ -n "$ports" ] && rmc_add_reject_iface_cmd $RM_CHAIN $dev "$ports"
	done

	return 0
}

rmc_add_iface()
{
	config_get enabled $1 enabled
	config_get iface $1 iface

	local service
	local dev
	local ports
	service="$(uci get remote_mngt.@support[0].service)"
	dev=$(get_dev_by_iface "$iface")
	ports=$2

	[ $? -ne 0 -a -z "$dev" ] && return 1
	portal_port=$(get_service_port PORTAL)
	[ ! -f "/tmp/support_tddp" ]&&tddp_port=$(get_service_port TDDP)
	if [ $enabled == "on" ]; then
		rmc_add_iface_cmd $RM_CHAIN $dev "$ports"
		check_iface_iswan $iface
		if [ "$is_wan" == "1" ];then
			[ -n "$portal_port" ] && rmc_add_reject_iface_cmd $RM_CHAIN $dev "$portal_port"
		fi
		[ -n "$tddp_port" ] && rmc_add_reject_iface_cmd $RM_CHAIN $dev "$tddp_port"
	else
		check_iface_iswan $iface
		if [ "$is_wan" == "1" ];then
			if [ -n "$ports" ];then
				[ -n "$portal_port" ]&&ports="$ports,$portal_port"
			else
				[ -n "$portal_port" ]&&ports="$portal_port"
			fi
		fi
		if [ -n "$ports" ];then
			[ -n "$tddp_port" ]&&ports="$ports,$tddp_port"
		else
			[ -n "$tddp_port" ]&&ports="$tddp_port"
		fi
		[ -n "$ports" ] && rmc_add_reject_iface_cmd $RM_CHAIN $dev "$ports"
	fi

	return 0
}

rmc_add_iface_list()
{
	config_load remote_mngt
	config_foreach rmc_add_iface mngt_iface $1
}

rmc_start(){
	[ ! -f /tmp/remote_mngt_lock ] && {
		touch /tmp/remote_mngt_lock
		[ ! -f /etc/config/remote_mngt ] && touch /etc/config/remote_mngt

		# 创建remote_mngt_chain
		# ipv4
		local ret
		ret=$(iptables -w -N $RM_CHAIN)
		[ -n "$ret" ] && $(iptables -w -F $RM_CHAIN)
		# ipv6
		ret=$(ip6tables -w -N $RM_CHAIN)
		[ -n "$ret" ] && $(ip6tables -w -F $RM_CHAIN)

		# 若INPUT中无remote_mngt_chain，则添加该链
		# ipv4
		local rule_number="$(iptables -w -nL INPUT --line-numbers |grep -i $RM_CHAIN |wc -l)"
		[ $rule_number -eq 0 ] && ret=$(iptables -w -I INPUT -j $RM_CHAIN)
		# ipv6
		rule_number="$(ip6tables -w -nL INPUT --line-numbers |grep -i $RM_CHAIN |wc -l)"
		[ $rule_number -eq 0 ] && ret=$(ip6tables -w -I INPUT -j $RM_CHAIN)

		# 获取端口号
		local port=""
		local ports=""

		local service="$(uci get remote_mngt.@support[0].service)"
		for serv in $service;do
			port=$(get_service_port $serv)
			[ -z "$port" ] && continue
			[ -z "$ports" ] && {
				ports="$port"
				continue
			}
			ports="$ports,$port"
		done

		# 远程管理规则（不是针对接口），将IP添加进ipset中
		# 对于LAN/WAN设备，开启后，可通过WAN口访问设备
		rmc_add_rsa_rule_list $ports
		# 针对接口，可在接口开启远程管理
		rmc_add_iface_list $ports

		# update some port don't dmz
		lua /lib/nat/nat_reload.lua dmz_remote_update

		rm /tmp/remote_mngt_lock
		return 0
	}

	return 1
}

rmc_restart(){
	rmc_stop
	rmc_start
}

rmc_stop(){
	[ ! -f /tmp/remote_mngt_lock ] && {
		touch /tmp/remote_mngt_lock
		local ret
		[ -z "$RM_CHAIN" ] && return 1

		ret=$(iptables -w -F $RM_CHAIN; ip6tables -w -F $RM_CHAIN)
		[ -n "$ret" ] && return 1

		local rule_index_ipv4
		rule_index_ipv4="$(iptables -w -nL INPUT --line-numbers |grep -i $RM_CHAIN |cut -d ' ' -f 1)"
		[ "$rule_index_ipv4" -ne 0 ] && `iptables -w -D INPUT $rule_index_ipv4`
		local rule_index_ipv6="$(ip6tables -w -nL INPUT --line-numbers |grep -i $RM_CHAIN |cut -d ' ' -f 1)"
		[ "$rule_index_ipv6" -ne 0 ] && `ip6tables -w -D INPUT $rule_index_ipv6`

		local ln_ipv4
		ln_ipv4=`iptables -t nat -w -nvL prerouting_rule_dmz --line| grep -w "\* remote_mngt \*" | awk '{print $1}'`
		[ "$ln_ipv4" != "" ] && iptables -t nat -w -D prerouting_rule_dmz $ln_ipv4

		local ln_ipv6
		ln_ipv6=`ip6tables -t nat -w -nvL prerouting_rule_dmz --line| grep -w "\* remote_mngt \*" | awk '{print $1}'`
		[ "$ln_ipv6" != "" ] && ip6tables -t nat -w -D prerouting_rule_dmz $ln_ipv6

		rm /tmp/remote_mngt_lock
		return 0
	}

	return 1
}

get_rule_num(){
	config_get interface $1 interface
	config_get enable $1 enable

	if [ -n "${interface}" -a "${enable}" == "on" ];then
		let dmz_rule_num++
	fi
}

get_dmz_ruleNum()
{
	config_load nat
	config_foreach get_rule_num rule_dmz
}

rmc_reload(){
	echo -e "start to load the remote_mngt_chain" > /dev/console
	rmc_restart
	[ -f "/lib/security_policy/security_policy_exception_handler.lua" ] && lua /lib/security_policy/security_policy_exception_handler.lua reload mngt_if
	echo -e "finish load the remote_mngt_chain" > /dev/console
}
