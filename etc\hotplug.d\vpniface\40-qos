#!/bin/sh
# hotplug event for qos-tplink

# ACTION triggered by netifd
[ "$INTERFACE" = loopback ] && exit

# IF qos not in rc.d, we shall NOT response hotplug
#[ `ls /etc/rc.d | grep -c "qos"` -eq 0 ] && exit


# record the log
mkdir -p "/tmp/.qos"
local HOTP_LOG="/tmp/.qos/hotplug.log"
[ ! -f $HOTP_LOG ] && echo -e "time \ttrigger \taction" > $HOTP_LOG
[ "`ls -l $HOTP_LOG | awk '{print $5}'`" -gt 100000 ] && echo -e "time \ttrigger \taction" > $HOTP_LOG
echo -e "`date +%Y%m%d-%H:%M:%S` $INTERFACE $ACTION" >> $HOTP_LOG

# check if qos module is ready
{
    flock -x 25
    [ ! -f /tmp/.qos/.ready ] && echo -e "`date +%Y%m%d-%H:%M:%S` \tqos not start, ignore hotplug msg!" >>$HOTP_LOG && exit
    flock -u 25
} 25<> /tmp/.qos/qos.lock

case "$ACTION" in
		ifup)
			# for ifup, INTERFACE init and rule install
			#sh /lib/qos-tplink/api.sh ifup $INTERFACE
			. /lib/qos-tplink/api.sh stop
			. /lib/qos-tplink/api.sh start

			;;
		ifdown)
			# for ifdown, just flush nf and tc concerned by this interface!
				# Thus improve some performance of ipt-mangle
			#sh /lib/qos-tplink/api.sh ifdown $INTERFACE
			. /lib/qos-tplink/api.sh stop
			. /lib/qos-tplink/api.sh start
			;;
		*)
			echo -e "`date +%Y%m%d-%H:%M:%S` \tillegal $ACTION!" >> $HOTP_LOG
			;;
esac