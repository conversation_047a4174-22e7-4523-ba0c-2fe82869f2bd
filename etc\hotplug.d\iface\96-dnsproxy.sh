#!/bin/sh

. /lib/zone/zone_api.sh
DNSPROXY_READY=/tmp/dnsproxy.ready 
echo -e "`date +%Y%m%d-%H:%M:%S` $INTERFACE $ACTION" > /dev/console

[ ! -f $DNSPROXY_READY ] && {
    echo -e "DNSPROXY not ready,ingore this evnet" > /dev/console
    exit 0
}
case "$ACTION" in
    ifup)
        if [ -f /etc/init.d/pdnsd ]; then
            /etc/init.d/pdnsd reload
        else
            /etc/init.d/dnsproxy reload
        fi
    ;;
    ifdown)
        if [ -f /etc/init.d/pdnsd ]; then
            /etc/init.d/pdnsd reload
        else
            /etc/init.d/dnsproxy reload
        fi
    ;;
esac
