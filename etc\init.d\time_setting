#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

START=26

TS_LIBDIR=/lib/time_setting

#TIME_SETTINGS_MOD_ID=279
#LOG_INF_SERVICE_START=504
#LOG_INF_SERVICE_STOP=505
#LOG_INF_SERVICE_RESTART=508

start() {
	$TS_LIBDIR/set_time

	sign=`cat /tmp/rsa_check/rsa_result`
	rtc=$(uci get profile.@global[0].rtc)
	if [ "$rtc" == "1" ]; then
		#Check time is valid(in range of 1970-1-1 to 2030-12-31)
		year=`hwclock -r | awk '{ print $5}`
		if [ "$sign" == "PASS" -a -n "$year" -a "$year" -ge "1970" -a "$year" -le "2030" ]; then
			hwclock -s -u
		else
			ori_time=$(uci get profile.@global[0].factory_time)
			date -s "$ori_time"
			hwclock -w -u
		fi
	else
		ori_time=$(uci get profile.@global[0].factory_time)
		date -s "$ori_time"
	fi

	#logx -p $$ $TIME_SETTINGS_MOD_ID $LOG_INF_SERVICE_START
}

stop() {
	echo "do nothing"
	#logx -p $$ $TIME_SETTINGS_MOD_ID $LOG_INF_SERVICE_STOP
}

restart() {
	$TS_LIBDIR/set_time
	#logx -p $$ $TIME_SETTINGS_MOD_ID $LOG_INF_SERVICE_RESTART
}

reload() {
	$TS_LIBDIR/set_time
	#logx -p $$ $TIME_SETTINGS_MOD_ID $LOG_INF_SERVICE_RESTART
}
