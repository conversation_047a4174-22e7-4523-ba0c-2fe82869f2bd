#!/bin/sh /etc/rc.common
#author: wangdechu

START=95

start()
{
	. /lib/l2tp/l2tp-functions.sh
	[ -f ${pppox_l2tp_main_path}/${l2tp_started} ] && { echo "l2tp has started"; exit 0; }
#do not use the default ppp options files
	[ -f "/etc/ppp/options" ] && rm -f /etc/ppp/options
#	ln -s ../iface/50-l2tp-lowerif-up-down.sh /etc/hotplug.d/vpniface/50-l2tp-lowerif-up-down.sh 2>/dev/null
	ps |grep "xl2tpd" | grep -qv "grep" || /etc/init.d/xl2tpd start
#start ipsec
	sleep 1
	/lib/l2tp/l2tp-init.sh
	touch ${pppox_l2tp_main_path}/${l2tp_started}
}


restart()
{
	. /lib/l2tp/l2tp-functions.sh
	killall xl2tpd-control
	/etc/init.d/xl2tpd restart
	sleep 1
#restart ipsec
	#/lib/l2tp/l2tp-doipsec.sh delete all

	#kill all pppd process of l2tp if there is any
	ps -w|grep "plugin pppol2tp.so"|grep -v grep|awk '{print $1}'|xargs -n 1 kill

	for dir in `ls /tmp/l2tp`; do
		[ "${dir}" != "l2tpclient-ipsec-info" -a "${dir}" != "l2tpserver-ipsec-info" ] && {
			rm -rf /tmp/l2tp/${dir}
		}
	done
	rm -f ${pppox_l2tp_main_path}/${l2tp_ifdevice_info}
	/lib/l2tp/l2tp-init.sh
	#/etc/init.d/ipsec reload
}
