#!/bin/sh
. /lib/zone/zone_api.sh
. /lib/pppox/pppox-default-variables.sh
local dnsserver=""

get_pptp_dns()
{
	/lib/pptp/pptp-get-tuunel-info.sh -p
	local i=0
	while true; do
		local bb=`uci -c $pppox_pptp_path get pptp-tunnel-info.@info["${i}"].ifname`
		if [ $bb != "" ]; then
			if [ $bb == $1 ]; then
					break
			fi
		else
			break
		fi
		let "i++"
	done

	dnsserver=`uci -c $pppox_pptp_path get pptp-tunnel-info.@info["${i}"].dns`
}

get_l2tp_dns()
{
	/lib/l2tp/l2tp-get-tunnel-info.sh -p
	local i=0
	while true; do
		local tmp=`uci -c $pppox_l2tp_main_path get l2tp-tunnel-info.@lacinfo["${i}"].ifname`
		if [ $tmp != "" ]; then
			if [ $tmp == $1 ]; then
				break
			fi
		else
			break
		fi
		let "i++"
	done

	dnsserver=`uci -c $pppox_l2tp_main_path get l2tp-tunnel-info.@lacinfo["${i}"].dns`
}

case "$ACTION" in
	ifup)
		userif=`zone_get_userif_bydev $DEVICE`
		[ -n "${userif}" ] && {
			sleep 1
			get_pptp_dns $DEVICE
			[ "${dnsserver}" == "" ] && get_l2tp_dns $DEVICE

			dns1=`echo $dnsserver | cut -d ' ' -f 1`
			dns2=`echo $dnsserver | cut -d ' ' -f 2`
			[ "$dns1" == "" ] && dns1="0.0.0.0"
			[ "$dns2" == "" ] && dns2="0.0.0.0"

			local current_status
			current_status=`ubus call online_check get_iface | grep -w "\"$userif\""`
			if [ -z "$current_status" ]; then
				echo "[online_check] new interface: $userif" > /dev/console
				ubus call online_check add_if "{\"name\":\"$userif\"}"
			fi

			echo "[online_check] $userif $DEVICE $dns1 $dns2" > /dev/console
			ubus call online_check update_dns "{\"name\":\"$userif\",\"dev\":\"$DEVICE\",\"dns1\":\"$dns1\",\"dns2\":\"$dns2\"}"
		}
	;;
	*)
	;;
esac

