#!/bin/sh /etc/rc.common
# Copyright (C) 2006-2010 OpenWrt.org
# Copyright (C) 2006 <PERSON>

START=50
STOP=50

SERVICE_USE_PID=1

NAME=dropbear
PROG=/usr/sbin/dropbear
PIDCOUNT=0
EXTRA_COMMANDS="killclients"
EXTRA_HELP="	killclients Kill ${NAME} processes except servers and yourself"

getNewPasswd()
{
	if [ -f "/usr/sbin/dbg_passwd_gen" ]
	then
		local key_code=`/usr/sbin/dbg_passwd_gen`
		local key=$(echo ${key_code:0:6})
		local code=$(echo ${key_code:6:38})
		#echo "Password: $key" > /dev/console
		echo "Authorized code: $code" > /dev/console
		echo ${key}
		echo ${code}
	else
		. /lib/functions.sh
		local macAddr=""
		macAddr=$(ubus call tddpServer getInfo '{"infoMask":1,"sep":"-"}' | sed 's/-//g' | jsonfilter -e '@.mac')
		echo "macAddr from tddp config is $macAddr" > /dev/console

		local key=$(echo -n "$macAddr" | md5sum)
		key=$(echo ${key:0:16})
		#echo "key is $key" > /dev/console
		echo ${key}
	fi

}

setNewPasswd()
{
	local ret=`getNewPasswd`
	local newPasswd=`echo $ret | awk '{print $1}'`
	local newAuthCode=`echo $ret | awk '{print $2}'`
	#echo "newPasswd is $newPasswd" > /dev/console
	(echo "$newPasswd";sleep 1;echo "$newPasswd") | passwd > /dev/null
	uci set ${NAME}.$1.AuthCode="$newAuthCode"
	uci commit ${NAME}
}

setDefaultPasswd()
{
	cp /rom/etc/passwd /etc/passwd &> /dev/null
}

dropbear_start()
{
	append_ports()
	{
		local ifname="$1"
		local port="$2"

		grep -qs "^ *$ifname:" /proc/net/dev || {
			append args "-p $port"
			return
		}

		for addr in $(
			ifconfig "$ifname" | sed -ne '
				/addr: *fe[89ab][0-9a-f]:/d
				s/.* addr: *\([0-9a-f:\.]*\).*/\1/p
			'
		); do
			append args "-p $addr:$port"
		done
	}


	local section="$1"

	# Customized kill switch
	config_get_bool ssh_port_switch "${section}" ssh_port_switch 0
	echo "ssh_port_switch is $ssh_port_switch" > /dev/console
	if [ "$ssh_port_switch" != "1" ]; then
		echo "set default passwd" > /dev/console
		setDefaultPasswd
		return 0
	fi

	# check if section is enabled (default)
	local enabled
	config_get_bool enabled "${section}" enable 1
	[ "${enabled}" -eq 0 ] && return 1

	# debug firmware use default password
	setDefaultPasswd

	# verbose parameter
	local verbosed
	config_get_bool verbosed "${section}" verbose 0

	# increase pid file count to handle multiple instances correctly
	PIDCOUNT="$(( ${PIDCOUNT} + 1))"

	# prepare parameters (initialise with pid file)
	local pid_file="/var/run/${NAME}.${PIDCOUNT}.pid"
	local args="-P $pid_file"
	local val
	# A) password authentication
	config_get_bool val "${section}" PasswordAuth 1
	[ "${val}" -eq 0 ] && append args "-s"
	# B) listen interface and port
	local port
	local interface
	config_get interface "${section}" Interface
	config_get interface "${interface}" ifname "$interface"
	config_get port "${section}" Port 22
	append_ports "$interface" "$port"
	# C) banner file
	config_get val "${section}" BannerFile
	[ -f "${val}" ] && append args "-b ${val}"
	# D) gatewayports
	config_get_bool val "${section}" GatewayPorts 0
	[ "${val}" -eq 1 ] && append args "-a"
	# E) root password authentication
	config_get_bool val "${section}" RootPasswordAuth 1
	[ "${val}" -eq 0 ] && append args "-g"
	# F) root login
	config_get_bool val "${section}" RootLogin 1
	[ "${val}" -eq 0 ] && append args "-w"
	# G) host keys
	config_get val "${section}" rsakeyfile
	[ -f "${val}" ] && append args "-r ${val}"
	config_get val "${section}" dsskeyfile
	[ -f "${val}" ] && append args "-d ${val}"

	# execute program and return its exit code
	[ "${verbosed}" -ne 0 ] && echo "${initscript}: section ${section} starting ${PROG} ${args}"
	SERVICE_PID_FILE="$pid_file" service_start ${PROG} ${args}
}

keygen()
{
	for keytype in rsa dss; do
		# check for keys
		key=dropbear/dropbear_${keytype}_host_key
		[ -f /tmp/$key -o -s /etc/$key ] || {
			# generate missing keys
			mkdir -p /tmp/dropbear
			[ -x /usr/bin/dropbearkey ] && {
				/usr/bin/dropbearkey -t $keytype -f /tmp/$key 2>&- >&- && exec /etc/rc.common "$initscript" start
			} &
		exit 0
		}
	done

	lock /tmp/.switch2jffs
	mkdir -p /etc/dropbear
	mv /tmp/dropbear/dropbear_* /etc/dropbear/
	lock -u /tmp/.switch2jffs
	chown root /etc/dropbear
	chmod 0700 /etc/dropbear
}

start()
{
	[ -s /etc/dropbear/dropbear_rsa_host_key -a \
	  -s /etc/dropbear/dropbear_dss_host_key ] || keygen

	config_load "${NAME}"
	config_foreach dropbear_start dropbear
}

stop()
{
	local pid_file pid_files

	pid_files=`ls /var/run/${NAME}.*.pid 2>/dev/null`

	[ -z "$pid_files" ] && return 1

	for pid_file in $pid_files; do
		SERVICE_PID_FILE="$pid_file" service_stop ${PROG} && {
			rm -f ${pid_file}
		}
	done
}

killclients()
{
	local ignore=''
	local server
	local pid

	# if this script is run from inside a client session, then ignore that session
	pid="$$"
	while [ "${pid}" -ne 0 ]
	 do
		# get parent process id
		pid=`cut -d ' ' -f 4 "/proc/${pid}/stat"`
		[ "${pid}" -eq 0 ] && break

		# check if client connection
		grep -F -q -e "${PROG}" "/proc/${pid}/cmdline" && {
			append ignore "${pid}"
			break
		}
	done

	# get all server pids that should be ignored
	for server in `cat /var/run/${NAME}.*.pid`
	 do
		append ignore "${server}"
	done

	# get all running pids and kill client connections
	local skip
	for pid in `pidof "${NAME}"`
	 do
		# check if correct program, otherwise process next pid
		grep -F -q -e "${PROG}" "/proc/${pid}/cmdline" || {
			continue
		}

		# check if pid should be ignored (servers, ourself)
		skip=0
		for server in ${ignore}
		 do
			if [ "${pid}" == "${server}" ]
			 then
				skip=1
				break
			fi
		done
		[ "${skip}" -ne 0 ] && continue

		# kill process
		echo "${initscript}: Killing ${pid}..."
		kill -KILL ${pid}
	done
}
