#!/bin/sh /etc/rc.common
# Copyright (C) 2008 OpenWrt.org
START=50

DEFAULT=/etc/default/snmpd
LIB_D=/var/lib/snmp
LOG_D=/var/log
RUN_D=/var/run
PID_F=$RUN_D/snmpd.pid
RUN_C=$RUN_D/snmpd.conf
DEFAULT_ENGINEID="80002E5703001478000000"

snmpd_agent_add() {
	local cfg="$1"

	config_get agentaddress "$cfg" agentaddress
	[ -n "$agentaddress" ] || return 0
	echo "agentaddress $agentaddress" >> $RUN_C
}


snmpd_system_add() {
	local cfg="$1"
	config_get syslocation "$cfg" sysLocation
	[ -n "$syslocation" ] && echo "sysLocation $syslocation" >> $RUN_C
	config_get syscontact "$cfg" sysContact
	[ -n "$syscontact" ] && echo "sysContact $syscontact" >> $RUN_C
	config_get sysname "$cfg" sysName
	[ -n "$sysname" ] && echo "sysName $sysname" >> $RUN_C
	config_get sysservice "$cfg" sysService
	[ -n "$sysservice" ] && echo "sysService $sysservice" >> $RUN_C
	config_get sysdescr "$cfg" sysDescr
	[ -n "$sysdescr" ] && echo "sysDescr $sysdescr" >> $RUN_C
	config_get sysobjectid "$cfg" sysObjectID
	[ -n "$sysobjectid" ] && echo "sysObjectID $sysobjectid" >> $RUN_C
}


snmpd_view_add() {
	local cfg="$1"

	config_get view_name "$cfg" name
	[ -n "$view_name" ] || return 0

	config_get view_type "$cfg" type
	[ -n "$view_type" ] || return 0

	config_get view_oid "$cfg" oid
	[ -n "$view_oid" ] || return 0

	# optional mask
	#config_get mask "$cfg" mask

	echo "view $view_name $view_type $view_oid" >> $RUN_C
}

snmpd_group_add() {
	local cfg="$1"

	config_get group_name "$cfg" name
	[ -n "$group_name" ] || return 0

	config_get group_context "$cfg" context
	[ -n "$group_context" ] || group_context='""'

	config_get group_sec_model "$cfg" sec_model
	[ -n "$group_sec_model" ] || return 0
	[ "$group_sec_model" == "v3" ] && group_sec_model="usm"

	config_get group_sec_level "$cfg" sec_level
	[ -n "$group_sec_level" ] || group_sec_level="noauth"

	config_get group_prefix "$cfg" prefix
	[ -n "$group_prefix" ] || group_prefix="exact"

	config_get group_read "$cfg" ro_view
	[ -n "$group_read" ] || return 0

	config_get group_write "$cfg" rw_view
	[ -n "$group_write" ] || group_write="none"

	config_get group_notify "$cfg" notify_view
	[ -n "$group_notify" ] || group_notify="none"

	echo "access $group_name $group_context $group_sec_model $group_sec_level $group_prefix $group_read $group_write $group_notify" >> $RUN_C
}


snmpd_comm_add() {
	local cfg="$1"

	config_get status "$cfg" status
	[ -n "$status" ] || return 0
	[ "$status" -eq "1" ] || return 0

	config_get comm_name "$cfg" name
	[ -n "$comm_name" ] || return 0

	config_get comm_type "$cfg" type
	[ -n "$comm_type" ] || return 0

	config_get comm_view "$cfg" view
	[ -n "$comm_view" ] || return 0

	config_get comm_source "$cfg" source
	[ -n "$comm_source" ] || comm_source="default"

	local cmd
	if [[ "$comm_type" == 'rw' ]]; then
		cmd="rwcommunity"
	elif [[ "$comm_type" == 'ro' ]]; then
		cmd="rocommunity"
	else
		return 0
	fi

	echo "$cmd $comm_name $comm_source -V $comm_view" >> $RUN_C
}

snmpd_user_add() {
	local cfg="$1"

	config_get status "$cfg" status
	[ -n "$status" ] || return 0
	[ "$status" -eq "1" ] || return 0

	config_get user_name "$cfg" name
	[ -n "$user_name" ] || return 0

	local user_sec_name="sec_$user_name"

	#not used
	config_get user_type "$cfg" type

	config_get user_group "$cfg" group
	[ -n "$user_group" ] || return 0

	config_get user_sec_model "$cfg" sec_model
	[ -n "$user_sec_model" ] || return 0

	if [[ "$user_sec_model" == "v1" || "$user_sec_model" == "v2c" ]]; then
		#map user to security, source is default.
		echo "com2sec $user_sec_name default $user_name" >> $RUN_C

		#add security to group
		echo -e "group $user_group $user_sec_model $user_sec_name\n" >> $RUN_C
	elif [[ "$user_sec_model" == "v3" ]]; then
		#create security argument
		local sec_arg=""

		config_get user_sec_level "$cfg" sec_level
		[ -n "$user_sec_level" ] || return 0

		if [[ "$user_sec_level" == "auth" || "$user_sec_level" == "priv" ]]; then
			config_get user_auth_type "$cfg" auth_type
			[ -n "$user_auth_type" ] || return 0

			config_get user_auth_pass "$cfg" auth_pass
			[ -n "$user_auth_pass" ] || return 0

			sec_arg="$user_auth_type $user_auth_pass"
		fi

		if [[ "$user_sec_level" == "priv" ]]; then
			config_get user_priv_type "$cfg" priv_type
			[ -n "$user_priv_type" ] || return 0

			config_get user_priv_pass "$cfg" priv_pass
			[ -n "$user_priv_pass" ] || return 0

			sec_arg="$sec_arg $user_priv_type $user_priv_pass"
		fi

		#create v3 user
		echo -e "createUser $user_name $sec_arg" >> $RUN_C

		#add user to group, sec_model for v3 is usm
		echo -e "group $user_group usm $user_name\n" >> $RUN_C
	else
		return 0
	fi
}


snmpd_pass_add() {
	local cfg="$1"
	local pass='pass'

	config_get miboid "$cfg" miboid
	[ -n "$miboid" ] || return 0
	config_get prog "$cfg" prog
	[ -n "$prog" ] || return 0
	config_get_bool persist "$cfg" persist 0
	[ $persist -ne 0 ] && pass='pass_persist'
	config_get priority "$cfg" priority
	priority=${priority:+-p $priority}
	echo "$pass $priority $miboid $prog" >> $RUN_C
}


snmpd_exec_add() {
	local cfg="$1"

	config_get name "$cfg" name
	[ -n "$name" ] || return 0
	config_get prog "$cfg" prog
	[ -n "$prog" ] || return 0
	config_get args "$cfg" args
	config_get miboid "$cfg" miboid
	echo "exec $miboid $name $prog $args" >> $RUN_C
}

snmpd_engineID_default() {
	#get mac address from tddp
	mac=`uci get network.MGMT.t_factory_mac`
	mac=$(echo "$mac" | tr -d ':')
	#echo "mac:$mac" > /dev/console
	[ -n "$mac" ] || return 0

	#calculate engine id based on mac
	engineID="80002E5703$mac"
	#echo "engineID:$engineID" > /dev/console

	#update engineID to snmpd
	arg="snmpd.setting.local_engine=$engineID"
	#echo $arg > /dev/console
	uci set $arg
	uci commit snmpd
}


snmpd_check_modify_engineID() {
	#check if restorefactory
	cfg_status=`uci get snmpd.setting.local_engine`
	#echo "$cfg_status" > /dev/console
	[[ "$cfg_status" == $DEFAULT_ENGINEID ]] || return 0

	#set engineID to default
	snmpd_engineID_default
}


start() {
	[ -d $LIB_D ] || mkdir -p $LIB_D
	[ -d $LOG_D ] || mkdir -p $LOG_D
	[ -d $RUN_D ] || mkdir -p $RUN_D
	[ -f $RUN_C ] && rm -f $RUN_C

	#modify engineID
	snmpd_check_modify_engineID

	config_load snmpd

	#check if snmpd enable is "on"
	enable_flag=`uci get snmpd.setting.enable`
	if [[ -z "$enable_flag" || "$enable_flag" != "on" ]]; then
		#echo "do not start snmpd" > /dev/console
		return 0
	fi

	echo "master agentx" >> $RUN_C
	echo "agentxTimeout 30s" >> $RUN_C
	echo "agentxRetries 3" >> $RUN_C

	#set engine id
	engineID=`uci get snmpd.setting.local_engine`
	echo -e "\nengineID $engineID\n" >> $RUN_C

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_agent_add agent

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_system_add system

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_view_add view

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_group_add group

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_comm_add community

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_user_add user

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_pass_add pass

	echo -e "\n" >> $RUN_C
	config_foreach snmpd_exec_add exec

	[ -f $DEFAULT ] && . $DEFAULT
	$DEBUG /usr/sbin/snmpd $OPTIONS &
}


stop() {
	local pid=`ps | grep -w /usr/sbin/snmpd | grep -v grep | awk '{print $1}'`
	for x in $pid; do
		#echo "kill -9 $x" > /dev/console
		kill -9 $x
	done
	[ -f $RUN_C ] && rm -f $RUN_C
}


restart(){
	stop
	start
}