#
# Copyright (C) 2008-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# 
# brief        ip mac binding module core_global shell scripts
# author     <PERSON>
# version    1.0.0 
# date         06May2015
# history     06May2015    <PERSON>    Create the file
#                22May2015    Huang <PERSON>    Modify 'zone' and 'interface'
#				4August2015  luopei   zone becomes the list of interfaces
. /lib/zone/zone_api.sh

IMB_PREFIX="imb_ipmac_"
IP_PREFIX="imb_ip_"
IMB_ARP_FILE="/tmp/.imb_arp"
IMB_IPSET_FILE="/tmp/.imb_ipset"
local imb_device=""

fw_config_get_global() {
	fw_config_get_section "$1" global { \
		string enable "0" \
	} || return
}

fw_config_get_imb_rule() {
	fw_config_get_section "$1" imb_rule { \
		string ip "" \
		string mac "" \
		string interface "" \
		string enable "" \
		string description "" \
	} || return
}

fw_load_all() {
	fw_config_once fw_load_global global
}

fw_exit_all() {
	/etc/init.d/arp_defense stop

	while read LINE
	do
		local devname=$(echo $LINE | awk '{print $1}')
		local setname=$(echo $LINE | awk '{print $2}')
		local ipname=$(echo $LINE | awk '{print $3}')
		ipset list $ipname | sed -n '/^Members/,$p' | grep -v 'Members' >/tmp/.debind
		ipmac -d /tmp/.debind $devname
		ipset destroy $setname
		ipset destroy $ipname
	done < /tmp/state/imb

	rm /tmp/state/imb
	imb_device=""
}

create_ipset_default() {
	[ -f /tmp/state/imb ] && rm /tmp/state/imb

	create_ipset_interface

	local iface=$(uci get arp_defense.global.interface)
	for interf in $iface;do
		local eth_dev=$(zone_get_effect_devices $interf)
		if [ x"$imb_device" == "x" ];then
			imb_device=$eth_dev
		else
			imb_device="$imb_device,$eth_dev"
		fi
		local effect_device=$eth_dev
		local device_ip=$(ifconfig $effect_device | grep 'inet addr' | awk -F : '{print $2}' | awk '{print $1}')
		[ x"$device_ip" != "x" ] && {
			local device_mac=$(ifconfig $effect_device | grep 'HWaddr' | awk '{print $5}')
			if [ x"$device_mac" != "x" ];then
				arp -s -i $effect_device $device_ip $device_mac
				ipset add "${IMB_PREFIX}${effect_device}" "${device_ip},${device_mac}" -exist
				ipset add "${IP_PREFIX}${effect_device}" "${device_ip}" -exist
			fi
		}
	done
}

create_ipset_interface() {
	#无需处理VPN类型userif
	local normal_userifs=$(zone_get_normal_useriflist)
	for i in $normal_userifs; do
		local EFF_IF=$(zone_get_effect_devices $i)
		for j in $EFF_IF; do
			echo $j | grep -q '_poe$' && continue
			local effect_device=${j}
			ipset create "${IMB_PREFIX}${effect_device}" hash:ip,mac  -exist
			ipset create "${IP_PREFIX}${effect_device}"  hash:ip  -exist
			echo "${effect_device} ${IMB_PREFIX}${effect_device} ${IP_PREFIX}${effect_device}" >>/tmp/state/imb
		done
	done
}

fw_load_global() {
	local global_enable=$(uci get arp_defense.global.enable)
	local imb_pass=$(uci get arp_defense.global.imb_pass)
	local garp=$(uci get arp_defense.global.garp)
	local time=$(uci get arp_defense.global.interval)
	local accept_only_flag=0

	create_ipset_default

	lua /usr/lib/lua/imb/imb_reload.lua

	[ $global_enable = "1" -a $imb_pass = "1" ] && /etc/init.d/arp_defense start
	[ x$time == 'x' ] && time=1000
	[ -d "/sys/module/arp_garp" ] && rmmod arp_garp
	if [ $global_enable = '1' ];then
		local devices=""
		if [ x$garp != 'x1' ];then
			insmod /lib/modules/$(uname -r)/arp_garp.ko sendflag=0 interval_time=$time name=$imb_device
		else
			insmod /lib/modules/$(uname -r)/arp_garp.ko sendflag=1 interval_time=$time  name=$imb_device
		fi
    fi
}

#$1: interface
#$2: ipaddr
#$3: mac
lua_load_imb_rule() {
	ipmac -b $IMB_ARP_FILE >/dev/null 2>&1
}

fw_load_imb_rule() {
	fw_config_get_imb_rule $1

	case $imb_rule_enable in
		1 )
			local interface=$imb_rule_interface
			local R_IPADDR=$imb_rule_ip
			local R_MAC=$(echo $imb_rule_mac | tr [a-z] [A-Z])
			local IF_IP
			local effect_device=$(zone_get_effect_devices $interface)
			ipset list ${IMB_PREFIX}$effect_device >/dev/null 2>&1 && {
				ipset add "${IMB_PREFIX}${effect_device}" "${R_IPADDR},${R_MAC//-/:}" -exist
				ipset add "${IP_PREFIX}${effect_device}" "${R_IPADDR}" -exist
				arp -s -i $effect_device $R_IPADDR ${R_MAC//-/:}
			}
			;;
		0 )
			;;
	esac
}

fw_flush_arp() {
	local IFS_old=$IFS
	IFS=$'\n'
	local LINE=""
	for LINE in $(arp -n | sed '/Address/d' | awk '{ print }');
	do
		if [ $(echo $LINE | awk '{ print $2 }') != "(incomplete)" ]; then
			local IPADDR=$(echo $LINE | awk '{ print $1 }')
			local IFACE=$(echo $LINE | awk '{ print $5 }')
			arp -d -i $IFACE $IPADDR
		fi
	done
	IFS=$IFS_old
}

fw_imb_event_do_interface() {
	local iface=$2
	local $action=$3
	local device=$(zone_get_effect_devices $iface)
	[ x"$device" == "x" ] && return 1
	ipset create "${IMB_PREFIX}$device" hash:ip,mac -exist
	ipset create "${IP_PREFIX}$device" hash:ip -exist

	local device_ip=$(ifconfig $device | grep 'inet addr' | awk -F : '{print $2}' | awk '{print $1}')
	[ x"$device_ip" != "x" ] && {
		local device_mac=$(ifconfig $device | grep 'HWaddr' | awk '{print $5}')
		if [ x"$device_mac" != "x" ];then
			ipset add "${IMB_PREFIX}${device}" "${device_ip},${device_mac}" -exist
			ipset add "${IP_PREFIX}$device" "${device_ip}" -exist
		fi
	}

	fw_config_get_imb_rule $1

	case $imb_rule_enable in
		1 )
			local R_ZONE=$imb_rule_interface
			local R_IPADDR=$imb_rule_ip
			local R_MAC=$(echo $imb_rule_mac | tr [a-z] [A-Z])
			local IF_IP

			for i in $R_ZONE; do
				[ "$i" == "$iface" ] && {
					local EFF_DE=$(zone_get_effect_devices $i)
					ipset add "${IMB_PREFIX}${EFF_DE}" "${R_IPADDR},${R_MAC//-/:}" -exist
					ipset add "${IP_PREFIX}${EFF_DE}" "${R_IPADDR}" -exist
					arp -s -i $EFF_DE $R_IPADDR ${R_MAC//-/:}
					break
				}
			done
			;;
		0 )
			;;
	esac
}

fw_imb_event_interface() {
	local iface=$1
	local action=$2

	[ -z "$iface" -o -z "$action" ] && {
		return
	}

	[ "add" == "$action" ] && {
		config_load ip_mac_bind
		config_foreach fw_imb_event_do_interface user_bind $iface $action
	}

	[ "del" == "$action" ] && {
		local LINE=""
		for LINE in $(arp -n | sed '/Address/d' | awk '{ print }');
		do
			if [ $(echo $LINE | awk '{ print $2 }') != "(incomplete)" ]; then
				local IPADDR=$(echo $LINE | awk '{ print $1 }')
				local IFACE=$(echo $LINE | awk '{ print $5 }')
				local FLAGS=$(echo $LINE | awk '{ print $4 }')
				devices=$(zone_get_effect_devices $iface)
				for device in $devices; do
					[ "$IFACE" == "$device" -a "$FLAGS" == "CM" ] && {
						arp -d -i $IFACE $IPADDR
					}
				done
			fi
		done
		local device=$(zone_get_effect_devices $iface)
		[ x"$device" != 'x' ] && {
			ipset destroy "${IMB_PREFIX}$device"
			ipset destroy "${IP_PREFIX}$device"
		}
	}
}
