#!/bin/sh

local sleep_seconds=$1
local direct_reload=$2
IPSEC_DIR="/tmp/ipsec/"
IPSEC_RELOADING_DIR="/tmp/ipsec/reloading/"
IPSEC_RELOADING_PID="/tmp/ipsec/reloading/reload_pid"


if [ ! -d "$IPSEC_DIR" ]; then
	mkdir -p $IPSEC_DIR
fi

if [ ! -d "$IPSEC_RELOADING_DIR" ]; then
	mkdir -p $IPSEC_RELOADING_DIR
fi

{
flock -x 381

if [ -f $IPSEC_RELOADING_PID ]; then
	for pid in $(cat ${IPSEC_RELOADING_PID}); do
		echo "[IPSec reload] kill old reload process, pid is $pid" > /dev/console
		kill -9 $pid
	done
fi


local reload_pid=$$
echo "[IPSec reload] new reload process pid is $reload_pid" > /dev/console
echo "$reload_pid" > $IPSEC_RELOADING_PID

flock -u 381
} 381<>/tmp/ipsec_delay_reload.lock

if [ -n "$1" ]; then
	sleep $1
else
	sleep 25
fi

touch "${IPSEC_RELOADING_DIR}${reload_pid}"
rm -f $IPSEC_RELOADING_PID

# /etc/init.d/zone restart
# /etc/init.d/ipsec reload

{
flock -x 382

lua /usr/lib/lua/ipsec/ipsec_delay_handle.lua $2

flock -u 382
} 382<>/tmp/ipsec_delay_handle.lock

rm -f "${IPSEC_RELOADING_DIR}${reload_pid}"
