#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     ipgroup core.sh
# brief
# author   <PERSON> chen
# version  1.0.0
# date     18June15
# histry   arg 1.0.0, 18June15, <PERSON> chen, Create the file.

fw_check()
{
    local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
    [ -n "$ret" ] && return 0||return 1
}


fw_config_get_global() {
    fw_config_get_section "$1" global { \
        string ip_frag "1" \
        string tcp_noflag "1" \
        string ping_death "1" \
        string ping_large "" \
        string tcp_winnuke "" \
        string tcp_fin_syn "" \
        string tcp_fin_noack "" \
        string ip_option "" \
        string ipopt_secure "" \
        string ipopt_strict_route "" \
        string ipopt_stream "" \
        string ipopt_noop "" \
        string ipopt_loose_route "" \
        string ipopt_record_route "" \
        string ipopt_timestamp "" \
    } || return
}


fw_load_dos_defense()
{
    fw_config_get_global global

    config_get global_ip_frag "global" "ip_frag"
    config_get global_tcp_noflag "global" "tcp_noflag"
    config_get global_ping_death "global" "ping_death"
    config_get global_ping_large "global" "ping_large"
    config_get global_tcp_winnuke "global" "tcp_winnuke"
    config_get global_tcp_fin_syn "global" "tcp_fin_syn"
    config_get global_tcp_fin_noack "global" "tcp_fin_noack"
    config_get global_land "global" "land"
    config_get global_ip_option "global" "ip_option"
    config_get global_ipopt_noop "global" "ipopt_noop"
    config_get global_ipopt_secure "global" "ipopt_secure"
    config_get global_ipopt_strict_route "global" "ipopt_strict_route"
    config_get global_ipopt_stream "global" "ipopt_stream"
    config_get global_ipopt_loose_route "global" "ipopt_loose_route"
    config_get global_ipopt_record_route "global" "ipopt_record_route"
    config_get global_ipopt_timestamp "global" "ipopt_timestamp"

    if [ "1" == "$global_ip_frag" -o "1" == "$global_tcp_noflag" -o "1" == "$global_ping_death" -o "1" == "$global_ping_large" -o "1" == "$global_tcp_winnuke" -o "1" == "$global_tcp_fin_syn" -o "1" == "$global_tcp_fin_noack" -o "1" == "$global_ip_option" ];then

        fw add 4 f dos_defense
        fw_check "filter" "forwarding_rule" "-j dos_defense"
        [ x$? == x0 ] && {
            fw add 4 f forwarding_rule dos_defense 2
        }
        #fw_check "filter" "input_rule" "-j dos_defense"
        #[ x$? == x0 ] && {
        #   fw add 4 f input_rule dos_defense 2
        #}

        [ "1" == "$global_tcp_noflag" ] && {
            fw add 4 f dos_defense DROP $ { -p tcp --tcp-flags ALL NONE }
        }

        [ "1" == "$global_ping_death" ] && {
            fw add 4 f dos_defense DROP $ { -p icmp --icmp-type 8 -m length ! --length 1:65535 }
        }

        [ "1" == "$global_ping_large" ] && {
            fw add 4 f dos_defense DROP $ { -p icmp --icmp-type any -m length ! --length 1:1024 }
        }

        [ "1" == "$global_tcp_winnuke" ] && {
            fw add 4 f dos_defense DROP $ { -p tcp --tcp-flags URG URG -m multiport --dports 137,138,139,113,53 }
        }

        [ "1" == "$global_land" ] && {
            fw add 4 f dos_defense DROP $ { -m dos_attack --land }
        }

        [ "1" == "$global_tcp_fin_syn" ] && {
            fw add 4 f dos_defense DROP $ { -p tcp --tcp-flags FIN,SYN FIN,SYN }
        }

        [ "1" == "$global_tcp_fin_noack" ] && {
            fw add 4 f dos_defense DROP $ { -p tcp --tcp-flags FIN,ACK FIN }
        }

        [ "1" == "$global_ip_option" ] && {
            flags=""

            [ "1" == "$global_ipopt_noop" ] && flags="$flags""1"

            [ "1" == "$global_ipopt_secure" ] && flags="$flags""2"

            [ "1" == "$global_ipopt_loose_route" ] && flags="$flags""3"

            [ "1" == "$global_ipopt_timestamp" ] && flags="$flags""4"

            [ "1" ==  "$global_ipopt_record_route" ] && flags="$flags""7"

            [ "1" == "$global_ipopt_strict_route" ] && flags="$flags""9"

            flags=$(echo $flags|sed 's/[0-9]/&,/g')
            flags=${flags%?}

            [ "1" == "$global_ipopt_stream" ] && flags="$flags"",8"

            fw add 4 f dos_defense DROP $ { -m ipv4options --flags "$flags" --any }
        }

    fi
}


fw_exit_all()
{
    fw flush 4 f dos_defense
    fw_check "filter" "forwarding_rule" "-j dos_defense"

    while [ x$? == x1 ]; do
        fw del 4 f forwarding_rule dos_defense
        fw_check "filter" "forwarding_rule" "-j dos_defense"
    done

    #fw_check "filter" "input_rule" "-j dos_defense"

    #while [ x$? == x1 ]; do
    #   fw del 4 f input_rule dos_defense
    #   fw_check "filter" "input_rule" "-j dos_defense"
    #done

    fw del 4 f dos_defense
}