local err = require("luci.torchlight.error")
local uci = require("luci.model.uci")
local dbg = require ("luci.torchlight.debug")
local nat_event = require("luci.model.nat_event")
local nat_para = require("luci.model.nat_para_parse")
local sys = require("luci.sys")
local util = require("luci.torchlight.util")
local uci_r = uci.cursor()
local ipm = require("luci.ip")

-- for argv op
OP_FROM_NETWORK=1

-- reload napt
-- op :操作类型标识，标识调用的模块，目前只有hotplug和network，其中默认（nil）为hotplug
function nat_napt_reload(section, iface, op)
    -- do nothing
    return err.ENONE
end

-- reload one-nat
-- op :操作类型标识，标识调用的模块，目前只有hotplug和network，其中默认（nil）为hotplug
function nat_onenat_reload(section, iface, op)
    dbg("start to reload one nat")
    -- read uci
    if nil == section or "off" == section["enable"] then
        return err.ENONE
    end
    local data = {}
    data.ip_proto = section["ip_proto"] or "IPv4"
    data.iface = section["if"]
    data.internal_ip = section["internal_ip"]
    data.external_ip = section["external_ip"]
    data.enable = section["enable"]
    data.dmz = section["dmz"]
    data.rule_name = section["name"]
    local e_iface = nat_para.extract_effect_from_if(data.iface, data.ip_proto)

    if e_iface ~= iface then
        return err.ENONE
    end
    -- get dial method of interface
    local dial = uci_r:get("network", iface, "proto")
    -- 若拨号方式不是静态拨号，则不适合onenat，删除对应规则
    if dial ~= "static" then
        dbg("dial method is not static, so disable onenat rule")
        nat_event.nat_onenat_del(data)
        -- 禁用规则
        uci_r:set("nat", section[".name"], "enable", "off")
        return err.ENONE
    end
    -- 接口上下线时，接口中关于ipv6的地址会被删除
    if "IPv6" == data.ip_proto then
        local nat_onenat_tools = '. /lib/nat/nat_onenat_tools.sh &&'
        local device = nat_para.extract_dev_from_if(data.iface)
        if nil == device then
            return err.ENONE
        end
        local cmd = string.format([[%s set_up_sub_iface %s %s %s %s]], nat_onenat_tools, data.rule_name, data.external_ip, device, data.ip_proto)
        sys.fork_call(cmd)
    end
    -- 接口IP变动不会影响onenat的规则，它只根据用户设定的IP进行改变
    -- 非静态转静态后，不禁用规则，直接重启规则

    if nil ~= op then
        nat_event.nat_onenat_update(data, data)
    end

    return err.ENONE

end

-- reload dmz
-- op :操作类型标识，标识调用的模块，目前只有hotplug和network，其中默认（nil）为hotplug
function nat_dmz_reload(section, iface, op)
    dbg("start to reload dmz")
    -- read uci about dmz
    if nil == section or "off" == section["enable"] then
        return err.ENONE
    end

    local data = {};
    data.iface = section["if"]

    if nil == data.iface[1] then
        data.iface = {data.iface}
    end

    data.ip_proto = section["ip_proto"] or "IPv4"
    for _, var in pairs(data.iface) do
        local e_iface = nat_para.extract_effect_from_if(var, data.ip_proto)
        if iface == e_iface or nil == iface then
            data.ip = section["ip"]
            data.enable = section["enable"]
            data.rule_name = section["name"]

            -- 获取规则中的ip
            local pos = 6
            if data.ip_proto == "IPv6" then
                pos = 5
            end
            local ipaddr_rule = nat_para.get_rule_ipaddr_by_name(data, "prerouting_rule_dmz", pos)
            -- 获取接口ip
            local ipaddr_iface = nil
            if iface ~= nil then
                ipaddr_iface = nat_para.extract_IP_from_if(iface, data.ip_proto)
                if data.ip_proto == "IPv6" then
                    -- 接口上下线时 IPv6 地址更新较慢，如果拿到本地链路地址则等待再次尝试获取
                    for i=1,5 do
                        if not string.match(ipaddr_iface,"fe80*") then
                            break
                        end
                        sys.fork_call("sleep 2;")
                        ipaddr_iface = nat_para.extract_IP_from_if(iface, data.ip_proto)
                    end
                end
            end

            -- 若ip未变
            if ipaddr_rule == ipaddr_iface and nil == op then
                return err.ENONE
            end

            nat_event.nat_dmz_update(data, data)
            break
        end
    end

    return err.ENONE
end

-- reload vs
-- op :操作类型标识，标识调用的模块，目前只有hotplug和network，其中默认（nil）为hotplug
function nat_vs_reload(section, iface, op)
    local uci_r = require("luci.model.uci").cursor()
    dbg("start to reload vs")
    -- read uci about vs
    if nil == section or "off" == section["enable"] then
        return err.ENONE
    end

    local data = {};
    data.iface = section["if"]

    if nil == data.iface[1] then
        data.iface = {data.iface}
    end
    data.rule_name = section["name"]
    data.ip_proto = section["ip_proto"] or "IPv4"
    for _, var in pairs(data.iface) do
        local e_iface = nat_para.extract_effect_from_if(var, data.ip_proto)
        if e_iface == iface or nil == iface then
            data.dest_ip = section["dest_ip"]
            data.enable = section["enable"]
            data.src_port = section["src_dport"]
            data.dest_port = section["dest_port"]
            data.loopback_ipaddr = section["loopback_ipaddr"]
            data.service_proto = section["proto"]


            -- 获取规则中的ip
            local pos = 6
            if data.ip_proto == "IPv6" then
                pos = 5
            end

            local ipaddr_rule = nat_para.get_rule_ipaddr_by_name(data, "prerouting_rule_vs", pos)
            -- 获取接口ip
            local ipaddr_iface = nil


            if iface ~= nil then
                ipaddr_iface = nat_para.extract_IP_from_if(iface, data.ip_proto)
                if data.ip_proto == "IPv6" then
                    -- 接口上下线时 IPv6 地址更新较慢，如果拿到本地链路地址则等待再次尝试获取
                    for i=1,5 do
                        if not string.match(ipaddr_iface,"fe80*") then
                            break
                        end
                        sys.fork_call("sleep 2;")
                        ipaddr_iface = nat_para.extract_IP_from_if(iface, data.ip_proto)
                    end
                end
            end

            if ipaddr_rule == ipaddr_iface and nil == op then
                return err.ENONE
            end

            nat_event.nat_vs_update(data, data)
            break
        else
            local ipset_name = "_vs_"..data.ip_proto.."_"..data.rule_name
            local nat_vs_tools = '. /lib/nat/nat_vs_tools.sh &&'
            local cmd = string.format([[%s vs_rule_create_ipset %s %s]], nat_vs_tools, ipset_name, data.ip_proto)
            sys.fork_call(cmd)
            data.loopback_ipaddr = section["loopback_ipaddr"]
            if nil ~= ipset_name then
                if nil == data.loopback_ipaddr then
                    data.loopback_ipaddr = ""
                end
                cmd = string.format([[%s vs_add_loopback_snat %s %s]], nat_vs_tools, ipset_name, data.loopback_ipaddr)
                sys.fork_call(cmd)
                local wan_mode = uci_r:get("network", "if_mode", "wan_mode") or 0
                if wan_mode > 0 then
                    cmd = string.format([[%s vs_add_mul_lan_snat %s %s]], nat_vs_tools, ipset_name, data.ip_proto)
                    sys.fork_call(cmd)
                end
            else
                return err.EINVARG
            end
        end
    end



    return err.ENONE
end

-- reload napt when set interface manually
function nat_napt_reload_network(section, iface)
    dbg("start to reload nat triggered from interface change")
    
    local flag = false
    -- read uci about napt
    if nil == section or "off" == section["enable"] then
        return err.ENONE
    end

    if iface == section["if"] then
        flag = true
    end

    if false == flag and "table" == type(section["if"]) then
        for _,j in pairs(section["if"]) do
            if j == iface then
                flag = true
                break
            end
        end
    end

    if true == flag then
        local data = {}
        data.enable = section["enable"]
        data.ip_proto = section["ip_proto"] or "IPv4"
        data.out_if = section["if"]
        data.ip = section["ip"]
        data.rule_name = section["name"]
        nat_event.nat_napt_update(data, data)
    end

    return err.ENONE
end

function deal_reload_napt()
    uci_r:foreach("nat", "rule_napt",
        function(section)
            nat_napt_reload(section, arg[2])
        end
    )
end

function deal_reload_onenat()
    uci_r:foreach("nat", "rule_onenat",
        function(section)
            nat_onenat_reload(section, arg[2])
        end
    )
    uci_r:commit("nat")
end

function deal_reload_dmz()
    uci_r:foreach("nat", "rule_dmz",
        function(section)
            nat_dmz_reload(section, arg[2])
        end
    )
end

function deal_reload_vs()
    uci_r:foreach("firewall", "redirect",
        function(section)
            nat_vs_reload(section, arg[2])
        end
    )
end

-- 修改接口，有时需要进行部分nat规则的重载，但又不需要重启整个nat，例如修改接口的vlan id
function deal_reload_nat_network()
    uci_r:foreach("nat", "rule_onenat",
        function(section)
            nat_onenat_reload(section, arg[2], OP_FROM_NETWORK)
        end
    )

    uci_r:foreach("nat", "rule_napt",
        function(section)
            nat_napt_reload_network(section, arg[2])
        end
    )

    uci_r:foreach("nat", "rule_dmz",
        function(section)
            nat_dmz_reload(section, arg[2], OP_FROM_NETWORK)
        end
    )

    uci_r:foreach("firewall", "redirect",
        function(section)
            nat_vs_reload(section, arg[2], OP_FROM_NETWORK)
        end
    )
end

-- arg[1] 操作类型，针对的nat规则
-- arg[2] 接口名iface（非userif，例如lan/wan机型里的WAN1等)
if "rule_napt" == arg[1] then
    deal_reload_napt()
elseif "rule_dmz" == arg[1] then
    deal_reload_dmz()
elseif "rule_onenat" == arg[1] then
    deal_reload_onenat()
elseif "rule_vs" == arg[1] then
    deal_reload_vs()
elseif "dmz_remote_update" == arg[1] then
    nat_para.update_port_dmz_mngt()
elseif "rule_nat_network" == arg[1] then
    deal_reload_nat_network()
end
