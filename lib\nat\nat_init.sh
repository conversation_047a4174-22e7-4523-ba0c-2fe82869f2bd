#!/bin/sh /etc/rc.common
. /lib/nat/nat_chain_op.sh
. /lib/nat/nat_core.sh

nat_is_loaded() {
    local bool=$(uci_get_state nat core loaded)
    return $((! ${bool:-0}))
}

nat_build_chain()
{
    nat_build_postrouting_chains
}

nat_build_postrouting_chains()
{
    # 为napt创建初始链
    nat_add_chain "postrouting_rule" "POSTROUTING" "IPv4"
    nat_add_chain "postrouting_rule_multi_nat" "postrouting_rule" "IPv4"
}

nat_load_rule()
{
    lua /lib/nat/nat_start.lua rule_napt
    lua /lib/nat/nat_start.lua rule_dmz
    lua /lib/nat/nat_start.lua rule_onenat
    lua /lib/nat/nat_start.lua rule_vs
    env -i CLEAN=on MODULE=NAPT /sbin/hotplug-call nat
    # lua /lib/nat/nat_start.lua all &
}

nat_run()
{
    nat_build_chain
    nat_load_rule
    # 表示nat已经loaded
    uci_set_state nat core loaded 1
}

nat_clear()
{
    iptables -t nat -F
    iptables -t nat -X
    ip6tables -t nat -F
    ip6tables -t nat -X
}