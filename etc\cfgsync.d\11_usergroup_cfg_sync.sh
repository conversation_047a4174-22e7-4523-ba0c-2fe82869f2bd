#!/usr/bin/lua

--[[
FW6600v1第二版软件的用户/用户组信息使用数据库存储，需要实现配置兼容
]]

local dbg 	  = require "luci.torchlight.debug"
local uci 	  = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"
local sqlite = require "luci.torchlight.uac.wportal_sqlApi"
local usergroup = require "luci.controller.admin.usergroup"
local sys = require "luci.sys"
local wrapper = require "luci.torchlight.uac.luasqlite3_wrapper"

local user_db_path = "/etc/nouci_config/dbs/auth_user.db"
local user_user_table = "authUserTable"
local ug_db_path = "/etc/nouci_config/dbs/usergroup.db"
local ug_group_table = "group_list"
local ug_relation_table = "relation_list"

local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()

local USER_MAX_CNT = 8000

local function usergroup_sync()
	uci_r:load(conf_dir)

    local mark = false
    uci_r.foreach("usergroup", "rule_user", function(conf_section)
        if conf_section["flag"] == "user" then
            mark = true
        end
    end)
    uci_r.foreach("usergroup", "rule_group", function(conf_section)
        if conf_section["flag"] == "user" then
            mark = true
        end
    end)

    if mark == false then
        -- 不需要进行配置兼容
        return
    end

    sys.fork_call("rm -f " .. ug_db_path)

    --如果不存在表格，创建表格
    usergroup.ug_create_all_table()

    -- 获取所有user的entry_id
    local all_user = {}
    local ret_entry = {}
    local maxUser = uci_r:get_profile("wportal", "usermngr_max") or USER_MAX_CNT
    ret_entry = sqlite.get_selected_entry(user_db_path, user_user_table, maxUser, 0)
    for index, user in ipairs(ret_entry) do
        if user.note and user.entry_id then
            all_user[user.note] = user.entry_id
        end
    end

    -- 新建所有组
    local all_group = {}
    uci_r.foreach("usergroup", "rule_group", function(conf_section)
        if conf_section["comment"] ~= nil and conf_section["flag"] ~= nil and conf_section[".name"] ~= "any" then
            local ref = conf_section["ref"]
            if ref == nil then
                ref = 0
            end
            usergroup.ug_insert_group_entry(conf_section["comment"], conf_section["flag"], ref)

            -- 获取所有组的entry_id
            local data = {}
            data.key = "name"
            data.value = sqlite.str4sql(conf_section["comment"])
            local group_id = sqlite.get_index(ug_db_path, ug_group_table, data)
            if group_id ~= nil then
                all_group[conf_section[".name"]] = group_id
            end
        end
    end)

    -- 新建所有组的relation_list
    uci_r.foreach("usergroup", "rule_group", function(conf_section)
        local parent_id = all_group[conf_section[".name"]]

        if conf_section["child_list"] ~= nil and parent_id ~= nil then
            if type(conf_section["child_list"]) == "string" then
                conf_section["child_list"] = {conf_section["child_list"]}
            end
            for index, child in pairs(conf_section["child_list"]) do
                if all_user[child] ~= nil then
                    usergroup.ug_insert_relation_entry(parent_id, all_user[child], "user")
                elseif all_group[child] ~= nil then
                    usergroup.ug_insert_relation_entry(parent_id, all_group[child], "group")
                else
                    dbg("cannot get the entry_id of child " .. child)
                end
            end
        end
    end)

    -- 修改authUserTable里面的note和comment
    local user_env = wrapper.sqlite3_db_env_new()
    local user_conn = wrapper.sqlite3_db_connect(user_env, user_db_path)
    uci_r.foreach("usergroup", "rule_user", function(conf_section)
        if conf_section["username"] ~= nil then
            -- 获取entry_id
            local data = {}
            data.key = "username"
            data.value = sqlite.str4sql(conf_section["username"])
            local entry_id = sqlite.get_index(user_db_path, user_user_table, data)

            if entry_id ~= nil then
                local notedata = {}
                notedata.note = conf_section["note"]
                sqlite.user_mngr_set_user_by_index(entry_id, notedata, user_env, user_conn)
            end
        end
    end)
    wrapper.sqlite3_db_connect_close(user_conn)
    wrapper.sqlite3_env_close(user_env)

    -- 修改安全策略
    uci_r.foreach("security_policy", "sec_policy", function(conf_section)
        if conf_section["user_group"] ~= nil and conf_section["user_group"] ~= "any" then
            local entry_id = all_group[conf_section["user_group"]]
            if entry_id ~= nil then
                uci_r:set("security_policy", conf_section[".name"], "user_group", "rule_group_" .. entry_id)
            end
        end
    end)

    -- 删除uci
    uci_r.foreach("usergroup", "rule_user", function(conf_section)
        uci_r:delete("usergroup", conf_section[".name"])
    end)
    uci_r.foreach("usergroup", "rule_group", function(conf_section)
        uci_r:delete("usergroup", conf_section[".name"])
    end)

	uci_r:commit("usergroup")
    uci_r:commit("security_policy")
	cfgsync.set_config_changed()

	return
end

usergroup_sync()
