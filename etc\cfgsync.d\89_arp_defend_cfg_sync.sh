#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"

local db_arp_defend = {
	name = "arp_defend.db",
	tables = {
		{
			name = "arpDefendAp",
			columns = {
				{
					name = "ap_id",
					stype = "INTEGER",
					db_attr = {"db_index"},
					default_value = nil
				}, {
					name = "enable",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "bind_id_list",
					stype = "VARCHAR ( 288 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "key",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			},
			datas = {}
		}, {
			name = "arpDefendArp",
			columns = {
				{
					name = "arp_id",
					stype = "INTEGER",
					db_attr = {"db_index"},
					default_value = nil
				}, {
					name = "ip",
					stype = "VARCHAR ( 40 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "mac",
					stype = "VARCHAR ( 17 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "description",
					stype = "VARCHAR ( 128 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "key",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			},
			datas = {}
		}
	}
}

cfgsync.config_sync(db_arp_defend, "database")