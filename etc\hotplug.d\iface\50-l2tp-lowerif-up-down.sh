#!/bin/sh
#author: wangdechu
#brief: dealing with events from lower interface
#env: $INTERRFACE $ACTION $DEVICE

. /lib/l2tp/l2tp-functions.sh
. /lib/zone/zone_api.sh

[ -z "${INTERFACE}" -a -z "${DEVICE}" ] && exit 1
[ "${ACTION}" != "ifdown" -a "${ACTION}" != "ifup" ] && exit 1


l2tp_exist()
{
	local ltype=$1 
	local lname=$2
	local exist=0 

	if [ ${ltype} = "lac" ]; then
		xl2tpd-control status $2|head -n 1|grep -q "^00" && { exist=1; }
	elif [ ${ltype} = "lns" ]; then
		xl2tpd-control status-lns $2|head -n 1|grep -q "^00" && { exist=1; }
	fi
 	
 	echo ${exist}
 	return 0
}

check_the_l2tp()
{
	local checkpath=$1
	local checkoption=$2
	local ltype=$3
	local runfunc=$4
	local exist
	
	for dir in `ls ${checkpath}`; do
		local path=${checkpath}/${dir}/config
		local option=`uci -c ${path} get ${pppox_configname}.${dir}.${checkoption} 2>/dev/null`
		local device=`zone_get_effect_devices ${option}`
		[ -z "${device}" ] && continue
		
		[ "${device}" = "${indevice}" ] && { exist=`l2tp_exist ${ltype} ${dir}`; 
			if [ "${exist}" = "0" ]; then 
				${runfunc} ${dir}
			else

				if [ "${ltype}" = "lac" ]; then
					l2tp_cdistribute lac ${dir}
				else
					l2tp_cdistribute lns ${dir}
				fi
				${runfunc} ${dir}
			fi
		}
	done
}

if [ "${ACTION}" == "ifdown" ]; then
	if [ -n "${INTERFACE}" ]; then
		lsection=if_${INTERFACE}
		lsection=${lsection//./_}
		#lsection=${lsection//-/_}
	elif [ -n "${DEVICE}" ]; then
		lsection=dev_${DEVICE}
		lsection=${lsection//./_}
		#lsection=${lsection//-/_}
	fi
	configname=`uci -c ${pppox_l2tp_main_path} get ${l2tp_ifdevice_info}.${lsection}.server 2>/dev/null`
	for dir in ${configname}; do
		l2tp_cdistribute lns ${dir}
		echo "" >/dev/null
	done
	configname=`uci -c ${pppox_l2tp_main_path} get ${l2tp_ifdevice_info}.${lsection}.client 2>/dev/null`
	for dir in ${configname}; do
		l2tp_cdistribute lac ${dir}
#		xl2tpd-control disconnect ${dir}
		echo "" >/dev/null
	done

elif [ "${ACTION}" == "ifup" ]; then
	if [ -n "${INTERFACE}" ]; then
		if ! zone_check_if_effect $INTERFACE; then
			exit 1
		fi
		indevice=`zone_get_effect_devices ${INTERFACE}`
	elif [ -n "${DEVICE}" ]; then
		if ! zone_check_dev_effect $DEVICE; then
			exit 1
		fi
		indevice=${DEVICE}
	fi
	check_the_l2tp ${pppox_l2tp_server_path} "bindif" "lns" "l2tp_distribute lns"
	check_the_l2tp ${pppox_l2tp_client_path} "outif" "lac" "l2tp_distribute lac"
fi
