#!/bin/sh /etc/rc.common
# Copyright (C) 2006 OpenWrt.org

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/cloud-relay-client.pid

START=99

start() {
	#NMS开启远程管理时会执行/etc/init.d/cloud_relay_client start，在进程不存在时，则启动服务
	if [ -z `pidof cloud_relay_client` ];then
		service_start /usr/sbin/cloud_relay_client &
	fi
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ubus call cloud_relayc stop", "wait_time": 10, "type": "offline", "action":"add" }' &> /dev/null
	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo  4","wait_time":0,"type":"feature","action":"add"}' &> /dev/null
}

stop() {
	service_stop /usr/sbin/cloud_relay_client
	if [ -f ${SERVICE_PID_FILE} ];then
		rm -f ${SERVICE_PID_FILE}
	fi
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ubus call cloud_relayc stop", "wait_time": 10, "type": "offline", "action":"delete" }' &> /dev/null
}

restart()
{
	stop
	start
}

reload()
{
	restart
}