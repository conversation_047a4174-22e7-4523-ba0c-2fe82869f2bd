#!/bin/sh

_delete_rule() {                                        
    local cfg="$1"                                     
    local iface="$2"                                                                                       
    local interface   

    interface=$(uci_get dyn3322ddns "$cfg" interface)
    echo "cfg=$cfg,iface=$iface,interface=$interface"  >> /tmp/dyn3322ddns_vpnhook.log    	
    [ "$interface" == "$iface" ] && {      
        uci delete dyn3322ddns.$cfg    
        uci_commit dyn3322ddns  		
    }                                                         
}

case ${ACTION} in
	DELETE)  
		[ -n "${interfaces}" ] && {
		    echo "interfaces=$interfaces" >> /tmp/dyn3322ddns_vpnhook.log    
		    interfaces=${interfaces//,/ } 
			for element in $interfaces   
			do  
				echo "element=$element"  >> /tmp/dyn3322ddns_vpnhook.log    
				config_load dyn3322ddns
				config_foreach _delete_rule dyn3322ddns $element
			done  
		}
	;;
	ADD)
	;;
	*)
	;;
esac