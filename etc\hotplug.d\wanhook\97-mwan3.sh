#!/bin/sh

_delete_rule() {

	ifaces=`uci get mwan3.$1.use_if 2>/dev/null`
	for iface in $ifaces;do
		if [ "$iface" == "$2" ];then
			#delete the policy's use_if
			uci del_list mwan3.$1.use_if=$iface

			#delete the iface section
			uci delete mwan3.$iface
			uci_commit mwan3
		fi
	done
}


get_policy_name()
{
	policy_name=$1
}

_add_rule()
{
	local policy_name=""

	ret=`uci get mwan3.$1 2>&1`
	if [ "$ret" != "if" ];then
		uci set mwan3.$1=if
		uci set mwan3.$1.balance=1
		uci set mwan3.$1.enabled=1
		uci set mwan3.$1.ref=0
	fi

	config_foreach get_policy_name policy

	[ -n "$policy_name" ] && uci add_list mwan3.$policy_name.use_if=$1

	uci_commit mwan3
}


case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				config_load mwan3
				config_foreach _delete_rule policy $element
			done
		}
	;;
	ADD)
		[ -n "${interfaces}" ] && {
			config_load mwan3
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _add_rule $element
			done
		}
	;;
	*)
	;;
esac