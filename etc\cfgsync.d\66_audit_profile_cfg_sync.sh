#!/usr/bin/lua

--[[
根据TDCP接口开关的参数添加"_enable"词尾,需要在启动时实现配置兼容
]]

local dbg 	  = require "luci.torchlight.debug"
local uci 	  = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"
local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()

function addEnable(conf_section)
    if (nil == conf_section)
    then
        return 0;
    end

    uci_r:set("audit_policy", conf_section[".name"], "audit_profile")

    uci_r:set("audit_policy", conf_section[".name"], "http_url_enable", conf_section["http_url"])
    uci_r:set("audit_policy", conf_section[".name"], "http_title_enable", conf_section["http_title"])
    uci_r:set("audit_policy", conf_section[".name"], "http_upload_enable", conf_section["http_upload"])
    uci_r:set("audit_policy", conf_section[".name"], "http_download_enable", conf_section["http_download"])
    uci_r:set("audit_policy", conf_section[".name"], "ftp_cmd_enable", conf_section["ftp_cmd"])
    uci_r:set("audit_policy", conf_section[".name"], "ftp_upload_enable", conf_section["ftp_upload"])
    uci_r:set("audit_policy", conf_section[".name"], "ftp_download_enable", conf_section["ftp_download"])
    uci_r:set("audit_policy", conf_section[".name"], "mail_send_enable", conf_section["mail_send"])
    uci_r:set("audit_policy", conf_section[".name"], "mail_receive_enable", conf_section["mail_receive"])
    uci_r:set("audit_policy", conf_section[".name"], "im_login_enable", conf_section["im_login"])
    uci_r:set("audit_policy", conf_section[".name"], "name", conf_section["name"])
    if nil ~= conf_section["comment"] then
        uci_r:set("audit_policy", conf_section[".name"], "comment", conf_section["comment"])
    end
    uci_r:set("audit_policy", conf_section[".name"], "ref", conf_section["ref"])
    if nil ~= conf_section["http_url_list"] then
        uci_r:set("audit_policy", conf_section[".name"], "http_url_list", conf_section["http_url_list"])
    end
    return 1
end

local function audit_profile_sync()
    local has_changed = 0
	uci_r:load(conf_dir)

	uci_r.foreach("audit_profile", "audit_profile", function(conf_section)
        has_changed = addEnable(conf_section)
	end)

    if has_changed == 1 then
    	uci_r:commit("audit_policy")
    	cfgsync.set_config_changed()
        os.execute("rm /tmp/etc/uc_conf/audit_profile")
    end

	return
end

--[[
根据TDCP修改audit_policy的action为none
]]
local function audit_policy_sync()
    local has_changed = 0

	uci_r:load(conf_dir)
	uci_r.foreach("audit_policy", "audit_policy", function(conf_section)
        if conf_section["action"] == "return" then
            has_changed = 1
            uci_r:set("audit_policy", conf_section[".name"], "action", "none")
        end
	end)
    if has_changed == 1 then
    	uci_r:commit("audit_policy")
    	cfgsync.set_config_changed()
    end
	return
end

--[[
根据TDCP修改behave_audit的log_send为log_send_enable
]]
local function behave_audit_sync()
    local has_changed = 0

	uci_r:load(conf_dir)
	uci_r.foreach("behave_audit", "behave_audit", function(conf_section)
        if nil ~= conf_section["log_send"] then
            has_changed = 1
            uci_r:set("behave_audit", conf_section[".name"], "log_send_enable", conf_section["log_send"])
            uci_r:delete("behave_audit", conf_section[".name"], "log_send")
        end
	end)
    if has_changed == 1 then
    	uci_r:commit("behave_audit")
    	cfgsync.set_config_changed()
    end
	return
end

audit_profile_sync()
audit_policy_sync()
behave_audit_sync()
