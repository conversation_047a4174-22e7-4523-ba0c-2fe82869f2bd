file_name="/tmp/.nat/one_nat_id"

set_up_sub_iface()
{
    local rule_name=$1
    local external_ip=$2
    local device=$3
    local proto=$4
    local netmask="***************"

    case $proto in
    IPv4)
        # get device name
        local free=`awk -F : '{if(NF==2) {print $1}}' ${file_name} | awk -F : 'NR==1 {printf $1}'`
        [ ${#free} -eq 0 ] && {
            free=`wc -l ${file_name} | awk '{print $1+1}'`
            [ ${#free} -eq 0 ] && free=1
            echo "$free:$device:$rule_name" >> ${file_name}
        } || sed "s/^${free}:/$free:$device:$rule_name/" -i ${file_name}

        ifconfig ${device}:${free} ${external_ip} netmask $netmask up
    ;;
    IPv6)
        ip -6 addr add ${external_ip} dev ${device}
    ;;
    esac
}

del_sub_iface()
{
    local rule_name=$1
    local external_ip=$2
    local device=$3
    local proto=$4

    case $proto in
    IPv4)
        local del_item=`grep ":${rule_name}$" /tmp/.nat/one_nat_id | awk -F : '{print $2":"$1}'`
        [ ${#del_item} -ne 0 ] && {
            ifconfig ${del_item} down
            local del_dev=`echo ${del_item} | awk -F : '{print $1}'`
            sed "s/:${del_dev}:${rule_name}$/:/" -i ${file_name}
        }
    ;;
    IPv6)
        ip -6 addr del ${external_ip} dev ${device}
    ;;
    esac
}


onenat_dmz_rule_creat_white()
{
	local name=$1
	local ip_proto=$2
	local dest_ip=$3

	case $ip_proto in
	IPv4)
	# do nothing
	;;
	IPv6)
	local chain_exist=`ip6tables -w -t filter -nvL | grep -w ipv6_onenat_dmz_forward`
	[ -z "$chain_exist" ] && {
		fw add 6 f ipv6_onenat_dmz_forward
		ip6tables -w -t filter -I forwarding_rule -j ipv6_onenat_dmz_forward
	}

	fw del 6 f ipv6_onenat_dmz_forward ACCEPT { -d $dest_ip -m comment --comment $name }
	fw add 6 f ipv6_onenat_dmz_forward ACCEPT { -d $dest_ip -m comment --comment $name }
	;;
	esac

}

onenat_dmz_rule_destroy_white()
{
	local rule_name=$1
	local ip_proto=$2
	local chain_name="ipv6_onenat_dmz_forward"

	case $ip_proto in
	IPv4)
	# do nothing
	;;
	IPv6)
	#find the number of the rule
	local rule_num=`ip6tables -w -t filter -nv --line-number -L ${chain_name} | grep -w "\/\* ${rule_name} \*\/" | sort -r -n | cut -d " " -f 1`

	for nums in $rule_num
	do
		ip6tables -w -t filter -D ${chain_name} ${nums}
	done
	;;
	esac
}