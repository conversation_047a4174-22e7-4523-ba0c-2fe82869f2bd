#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/sys_monitor.pid

START=98

start()
{
    service_start /usr/sbin/sys_monitor
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "/usr/sbin/ip_conflict_detection", "wait_time": 10, "type": "online", "action":"add" }' &> /dev/null
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "/usr/sbin/dhcpd_check_event", "wait_time": 10, "type": "online", "action":"add" }' &> /dev/null
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "killall -9 ip_conflict_detection", "wait_time": 10, "type": "offline", "action":"add" }' &> /dev/null
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "killall -9 dhcpd_check_event", "wait_time": 10, "type": "offline", "action":"add" }' &> /dev/null
}

stop()
{
	service_stop /usr/sbin/sys_monitor
	if [ -f ${SERVICE_PID_FILE} ];then
		read PID < ${SERVICE_PID_FILE} && kill ${PID}  && rm -f ${SERVICE_PID_FILE}
	fi
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "/usr/sbin/ip_conflict_detection", "wait_time": 10, "type": "online", "action":"delete" }' &> /dev/null
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "/usr/sbin/dhcpd_check_event", "wait_time": 10, "type": "online", "action":"delete" }' &> /dev/null
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "killall -9 ip_conflict_detection", "wait_time": 10, "type": "offline", "action":"delete" }' &> /dev/null
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "killall -9 dhcpd_check_event", "wait_time": 10, "type": "offline", "action":"delete" }' &> /dev/null
}

restart()
{
	stop
	start
}

reload()
{
	restart   
}

