#!/bin/sh
#author: wangdechu

. /lib/functions.sh
. /lib/pppox/pppox-default-variables.sh

_check_pppox_type()
{
	[ "${1}" = "${2}" -o  "${1}" = "all" ] && 
		[ ! -f ${pppox_path}/${2}.hasinited ] && 
		{ rm -rf ${4}; mkdir -p ${4}; \
		touch ${4}/${pppox_if_config}; \
		touch ${4}/${pppox_user_secrets}; \
		eval "$3=1"; }
}

_begin_load_user()
{
	_check_pppox_type $1 ${pppox_l2tptype} initl2tp ${pppox_l2tp_path}
	_check_pppox_type $1 ${pppox_pptptype} initpptp ${pppox_pptp_path}
	_check_pppox_type $1 ${pppox_pppoetype} initpppoe ${pppox_pppoe_path}
}

_copy_to_pppox()
{
	if [ ${1} = "all" -o "$2" = "" ]; then
		for dir in `ls ${3} 2>/dev/null`; do
			cp -f ${4}/* ${3}/${dir}/config/
		done
	else
		cp -f ${4}/* ${3}/${2}/config/
	
	fi
}

_end_load_user()
{	
	[ $initl2tp -eq 1 ] && { uci -c ${pppox_l2tp_path} commit ${pppox_if_config}; \
		touch ${pppox_path}/${pppox_l2tptype}.hasinited; }
	[ $initpptp -eq 1 ] && { uci -c ${pppox_pptp_path} commit ${pppox_if_config}; \
		touch ${pppox_path}/${pppox_pptptype}.hasinited; }
	[ $initpppoe -eq 1 ] && { uci -c ${pppox_pppoe_path} commit ${pppox_if_config}; \
		touch ${pppox_path}/${pppox_pppoetype}.hasinited; }
	
	# simple, not use function
	case "${1}" in
		"all")
			_copy_to_pppox $1 "$2" ${pppox_l2tp_server_path} ${pppox_l2tp_path}
			_copy_to_pppox $1 "$2" ${pppox_pptp_server_path} ${pppox_pptp_path}
			_copy_to_pppox $1 "$2" ${pppox_pppoe_server_path} ${pppox_pppoe_path};;
		"${pppox_l2tptype}")
			_copy_to_pppox $1 "$2" ${pppox_l2tp_server_path} ${pppox_l2tp_path};;
		"${pppox_pptptype}")
			_copy_to_pppox $1 "$2" ${pppox_pptp_server_path} ${pppox_pptp_path};;
		"${pppox_pppoetype}")
			_copy_to_pppox $1 "$2" ${pppox_pppoe_server_path} ${pppox_pppoe_path};;
	esac
}

_echo_info()
{ 
	local password1=${password//\\/\\\\}
	password1=${password1//\'/\\\'}
	local username1=${username//\\/\\\\}
	username1=${username1//\'/\\\'}
	username2=`asciimaker -a "${username}"`
	echo "'${username1}' * '${password1}' *" >>${1}/${pppox_user_secrets};
	echo -e "${localip}:\nippool ${ippool}\nms-dns ${dns}" >>${1}/${username2}${pppox_profile}
	echo -e "maxsessions ${maxsessions}" >>${1}/${username2}${pppox_profilex};
	
	uci -c ${1} set ${pppox_if_config}.${username2}=if
	uci -c ${1} set ${pppox_if_config}.${username2}.netmode=${netmode}
	uci -c ${1} set ${pppox_if_config}.${username2}.remotesubnet=${remotesubnet}
#	uci -c ${1} set ${pppox_if_config}.${username}.workmode=${workmode}
}

_load_one_user()
{
	local section=$1
	local ppptype=$2
	local matchl2tp=0
	local matchpptp=0
	local matchpppoe=0
	
	for option in ${pppox_user_options}; do
		eval "config_get ${option} \"${section}\" \"${option}\""
	done
	
	if echo ${type} | grep -iqE "(${pppox_l2tptype}|auto)"; then
		matchl2tp=1
	fi
	if echo ${type} | grep -iqE "(${pppox_pptptype}|auto)"; then
		matchpptp=1
	fi
	if echo ${type} | grep -iqE "(${pppox_pppoetype}|auto)"; then
		matchpppoe=1
	fi
	
	[ ${matchl2tp} -eq 1 -a ${initl2tp} -eq 1 ] && 
		_echo_info ${pppox_l2tp_path}
	[ ${matchpptp} -eq 1 -a ${initpptp} -eq 1 ] && 
		_echo_info ${pppox_pptp_path}
	[ ${matchpppoe} -eq 1 -a ${initpppoe} -eq 1 ] && 
		_echo_info ${pppox_pppoe_path}
}

# should run after load pppox configure 
# pppox_load_user { ({l2tp|pptp|pppoe} [<name>]) | all}
pppox_load_user()
{
	local configname=${pppox_user_configname}
	local initl2tp=0
	local initpptp=0
	local initpppoe=0
	
	_begin_load_user $1
	
	if [ ${initl2tp} -eq 1 -o ${initpptp} -eq 1 -o ${initpppoe} -eq 1 ]; then
		config_load ${configname}
		config_foreach _load_one_user user $1
		#config_foreach config_clear
	fi
	
	#$2: name of config, can be empty
	_end_load_user $1 $2
}

#$1 l2tp pptp pppoe all
pppox_reset_loadflag()
{
	[ "$1" = "${pppox_l2tptype}" -o "$1" = "all" ] && { rm -rf ${pppox_l2tp_path} \
		${pppox_path}/${pppox_l2tptype}.hasinited; }
	[ "$1" = "${pppox_pptptype}" -o "$1" = "all" ] && { rm -rf ${pppox_pptp_path} \
		${pppox_path}/${pppox_pptptype}.hasinited; }
	[ "$1" = "${pppox_pppoetype}" -o "$1" = "all" ] && { rm -rf ${pppox_pppoe_path} \
		${pppox_path}/${pppox_pppoetype}.hasinited; }
}

