#!/bin/sh

. /lib/functions.sh

LICENSE_LOCK="/tmp/license_lock"
LICENSE_PATH="/tmp/license"
LICENSE_INFO="/tmp/license/license_info"
status_change=0

timeout() {
	local waitsec=10
	( "$@" ) & pid=$!
	( sleep $waitsec && kill -9 $pid ) 2>/dev/null & watchdog=$!

	if wait $pid 2>/dev/null; then
		kill -9 $watchdog
		wait $watchdog
	fi
}

_rand() {
	local min max num
	min=$1
	max=$(($2-$min+1))
	num=$(date +%s)
	echo $(($num%$max+$min))
}

set_license_update_timer() {
	local minute hour
	minute=$(_rand 0 59)
	hour=$(_rand 0 23)
	echo "$minute $hour * * * /lib/license/license.sh update" >> /etc/crontabs/root
}

update_related_info() {
	local module="$1"
	case $module in
		ssl)
		# 通知nginx读取用户数
		lua /usr/lib/lua/openvpn/nginx_sslvpn_maxuser_reload.lua
		;;
		ap_manager)
		lua /usr/lib/lua/license/notify_uac_reload.lua
		;;
		portal)
		lua /usr/lib/lua/license/notify_nginx_reload.lua
		;;
		all)
		# 通知nginx修改用户数
		lua /usr/lib/lua/openvpn/nginx_sslvpn_maxuser_reload.lua
		lua /usr/lib/lua/license/notify_uac_reload.lua
		lua /usr/lib/lua/license/notify_nginx_reload.lua
		;;
		*)
		;;
	esac
}

set_module_modified_flag() {
	[ ! -d /tmp/policy_db ] && return
	local module="$1"
	case $module in
		av)
		/lib/fw_policy_manager/fw_policy_db_handler.sh modified av_profile
		;;
		ips)
		/lib/fw_policy_manager/fw_policy_db_handler.sh modified ips_profile
		;;
		url)
		/lib/fw_policy_manager/fw_policy_db_handler.sh modified ips_profile
		;;
		all)
		/lib/fw_policy_manager/fw_policy_db_handler.sh modified av_profile
		/lib/fw_policy_manager/fw_policy_db_handler.sh modified ips_profile
		;;
		*)
		;;
	esac
}

get_module_id() {
	local module="$1"
	case $module in
		ips)
		echo 0
		;;
		av)
		echo 1
		;;
		url)
		echo 2
		;;
		app)
		echo 3
		;;
		*)
		;;
	esac
}

_check_module_expired_time() {
	local module="$1"
	local global_status="$2"
	local cur_time="$3"
	local old_status expired_time
	old_status=$(uci get -c $LICENSE_PATH license_info.$module.status 2>/dev/null)
	expired_time=$(uci get -c $LICENSE_PATH license_info.$module.expired_time)
	[ -z "$old_status" ] && old_status=0
	if [ "$expired_time" -ne "0" ] && [ "$cur_time" -gt "$expired_time" ] || [ "$global_status" -eq "0" ]; then
		uci set -c $LICENSE_PATH license_info.$module.status="0"
		uci commit -c $LICENSE_PATH
		if [ "$old_status" -eq "1" ]; then
			local sys_time module_id
			sys_time=`expr "$(date +%s)" \* 1000`
			module_id=$(get_module_id $module)
			nms_event_report_notify -k 40001009 -t $sys_time -p '{"module":'$module_id'}'

			status_change=1
			set_module_modified_flag $module
			update_related_info $module
		fi
	else
		if [ "$expired_time" -gt "$cur_time" ]; then
			local remain_days old_remain_days
			remain_days=`expr \( $expired_time - $cur_time \) / 1000 / 3600 / 24 + 1`
			old_remain_days=$(uci get -c $LICENSE_PATH license_info.$module.remain_days 2>/dev/null)
			[ -z "$old_remain_days" ] && old_remain_days=0
			if [ "$remain_days" -lt "31" ] && [ "$remain_days" -ne "$old_remain_days" ]; then
				local sys_time module_id
				sys_time=`expr "$(date +%s)" \* 1000`
				module_id=$(get_module_id $module)
				nms_event_report_notify -k 40001004 -t $sys_time -p '{"module":'$module_id',"day":'\"$remain_days\"'}'
				uci set -c $LICENSE_PATH license_info.$module.remain_days="$remain_days"
			fi
		fi
		uci set -c $LICENSE_PATH license_info.$module.status="1"
		uci commit -c $LICENSE_PATH
		if [ "$old_status" -ne "1" ]; then
			status_change=1
			set_module_modified_flag $module
			update_related_info $module
		fi
	fi
}

_reload_sdb() {
	local module="$1"
	if [ "hotsite" == "${module}" ]; then
		# 网站分类库规格限制通过hotsite_sdb_init脚本读取规格，利用vpp接口进行限制
		# 与其他分类库规格限制方法不同(对库进行裁减)
		/etc/init.d/${module}_sdb_init restart
	else
		sec_db load $module
	fi
}

if [ -z "$1" ]; then
	echo "$0: too few args" > /dev/console
	return
fi

case $1 in
	start|update)
	# get cloud time
	timeout cloud_time > /tmp/cloud_time
	cur_time=`cat /tmp/cloud_time`
	if [ -z "$cur_time" ]; then
		# fail to get cloud time, use local sys time
		cur_time=`expr "$(date +%s)" \* 1000`
	fi

{
	flock -xn 299
	# ret not equal 0, can't get license lock, return
	if [ "$?" != "0" ]; then
		return
	fi

	license > /dev/null 2>&1
	if [ ! -e $LICENSE_INFO ]; then
		if [ "start" == "$1" ]; then
			sys_time=`expr "$(date +%s)" \* 1000`
			nms_event_report_notify -k 40001010 -t $sys_time
		fi
		sed -i '/license.sh/d' /etc/crontabs/root
		flock -u 299
		return
	fi

	# check license expired_time
	status=$(uci get -c $LICENSE_PATH license_info.global.status 2>/dev/null)
	expired_time=$(uci get -c $LICENSE_PATH license_info.global.expired_time)
	if [ "$expired_time" -ne "0" ] && [ "$cur_time" -gt "$expired_time" ]; then
		if [ -z "$status" ] || [ "$status" -ne "0" ]; then
			sys_time=`expr "$(date +%s)" \* 1000`
			nms_event_report_notify -k 40001010 -t $sys_time
		fi
		status="0"
		uci set -c $LICENSE_PATH license_info.global.status="0"
		uci commit -c $LICENSE_PATH
	else
		status="1"
		uci set -c $LICENSE_PATH license_info.global.status="1"
		uci commit -c $LICENSE_PATH
	fi
	# check module expired_time
	config_load $LICENSE_INFO
	config_foreach _check_module_expired_time license $status $cur_time

	# commit policy tree if status is changed
	if [ "$status_change" -eq "1" ] && [ "$1" == "update" ]; then
		# sdb spec may be cahnged, reload it, not need to be reload when start because the sdb will reload when they init
		config_foreach _reload_sdb license
		[ -f /tmp/policy_db/db_modified ] && /lib/fw_policy_manager/fw_policy_db_handler.sh submit &
	fi

	sed -i '/license.sh/d' /etc/crontabs/root
	if [ "$status" -eq "1" ]; then
		set_license_update_timer
	fi

	flock -u 299
} 299>$LICENSE_LOCK
	;;
	stop)
	# wait until the lock is available
{
	flock -x 299
	rm $LICENSE_INFO
	set_module_modified_flag all
	update_related_info all
	[ -f /tmp/policy_db/db_modified ] && /lib/fw_policy_manager/fw_policy_db_handler.sh submit &
	sed -i '/license.sh/d' /etc/crontabs/root
	flock -u 299
} 299>$LICENSE_LOCK
	;;
	*)
	;;
esac
