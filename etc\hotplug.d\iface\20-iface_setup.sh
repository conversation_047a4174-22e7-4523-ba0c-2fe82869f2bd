#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.
. /lib/zone/zone_api.sh

[ ! -f /tmp/iface_setup.ready ] && {
    exit 0
}

# ipset list ${INTERFACE}_FACES 2>dev/null || exit 0

case "$ACTION" in
    ifup)
        local userif=`zone_iface_to_userif ${INTERFACE}`
        if [ -n "$userif" ]; then
            ipset create ${userif}_IFACES  hash:name -exist
            local dev=$(zone_get_device_byif ${INTERFACE})
            ipset add ${userif}_IFACES $dev -exist
        fi
    ;;
    ifdown)
        local iface_list=$(uci get zone.NORMAL.iface 2>/dev/null)
        local ret=$(echo "$iface_list" | grep -q "\b${INTERFACE}\b" && echo "1")
        if [ "x$ret" == "x" ];then
            #此处接口集合不能被删除,可能存在被引用情况,故采用FLUSH操作
            #当存在被引用时,destroy后,可能会导致iptables的kernel panic
            local userif=`zone_iface_to_userif ${INTERFACE}`
            if [ -n "$userif" ]; then
                ipset flush ${INTERFACE}_IFACES 2>/dev/null
            fi
        fi
    ;;
    ifupdate)
        # do nothing
    ;;
esac
