#!/usr/bin/lua
-- 为了保持跟旧设备配置兼容，添加该脚本处理network中ipv6相关配置。
-- 该脚本仅设备出厂状态下会修改配置。根据系统配置，添加接口默认的ipv6配置。

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"
local uci = require ("luci.model.uci")
local sys = require("luci.sys")
local zone_api = require("luci.torchlight.zone_api")
local util = require("luci.torchlight.util")

local FILENAME="05_network_cfg_sync.sh"

--生成ipv6接口配置
local function add_ipv6_iface_uci(iface_name, ifname)
    local uci_r = uci.cursor()
    local ifv6_name = iface_name.."6"
    local section = {}
    local IFACE_CONFIG = "network"
    local res = true
    if nil == iface_name or nil == ifname then
        dbg("[ERROR:".. FILENAME .. "] param iface_name or ifname is nil")
        return false
    end

    local keys = {
        "mtu", "mtu6", "t_type",
        "peerdns", "peerdns6", "macaddr",
        "t_vlanid", "t_bindif"
    }

    for _,key in ipairs(keys) do
        section[key] = uci_r:get(IFACE_CONFIG, iface_name, key)
        if nil == section[key] then
            dbg("[ERROR:" .. FILENAME .. "] generate ipv6 iface uci failed, lack:" .. key)
            return false
        end
    end

    section["ifname"] = ifname
    section["t_name"] = ifv6_name
    section["proto"] = "dhcpv6"
    section["untag"] = "0"
    section["ipv6_enable"] = "on"
    section["ip_config"] = "auto"
    section["pd_mode"] = "non_temp"
    section["t6_reference"] = "0"

    uci_r:section(IFACE_CONFIG, "interface", ifv6_name, section)
    uci_r:save(IFACE_CONFIG)

    --添加相应的zone
    local zone6_preifx = util.split_string(ifv6_name, "_")
    if zone6_preifx ~= nil and zone6_preifx[1] ~= nil then
        local add_iface_tbl = {}
        local single_wan = uci_r:get("network", "if_mode", "single_wan")
        local zone_name
        local add_iface = {}
        if single_wan == "1" then
            zone_name = "WAN_inet6"
        else
            zone_name = string.upper(zone6_preifx[1]).."_inet6"
        end
        add_iface.dev = section["ifname"]
        add_iface_tbl[section["t_name"]] = add_iface
        zone_api.zone_update(zone_name, add_iface_tbl, nil, false)
    end

    return true
end

--业务需要默认开启IPv6接口
local function network_ipv6_config()
    local is_save = false
    local uci_r = uci.cursor()
    local has_set_pwd  = uci_r:get("system", "sys", "has_set_pwd")
    local is_factory = uci_r:get("system", "sys", "is_factory")
    --非出厂配置，不处理
    if "0" == tostring(is_factory) or "1" == tostring(has_set_pwd) then
        return true
    end

    --IPV6桥模式，不处理
    local bridge_v6_enable = uci_r:get("network", "bridge_v6", "bridge6_enable")
    if "on" == bridge_v6_enable then
        return true
    end

    --是否需要默认开启IPV6接口
    local default_ipv6_enable = uci_r:get("network", "if_mode", "default_ipv6_enable")
    if "on" ~= default_ipv6_enable then
        return true
    end

    uci_r:foreach("network", "interface", function(section)
        local res = true
        -- 不处理loopback接口
        if "loopback" == section[".name"] then
            return true
        end

        if "dhcp" == section["proto"] and nil == section["ipv6_enable"] then
            uci_r:set("network", section[".name"], "ipv6_enable", "on")
            uci_r:set("network", section[".name"], "pd_mode", "non_temp")
            uci_r:set("network", section[".name"], "ip_config", "auto")
            res = add_ipv6_iface_uci(section[".name"], section["ifname"])
            if not res then
                is_save = false
                return false
            end
            is_save = true
        end

        if "lan" == section[".name"] and nil == section["ipv6_enable"] then
            uci_r:set("network", section[".name"], "ipv6_enable", "on")
            uci_r:set("network", section[".name"], "ip6ifaceid", "manual")
            uci_r:set("network", section[".name"], "ip6addr", "fd00::1/64")
            uci_r:set("network", section[".name"], "prefixlen", "64")
            uci_r:set("network", section[".name"], "ip_mode", "manual")
            is_save = true
        end

        if "static" == section["proto"] and nil == section["ipv6_enable"] then
            --非管理口保持默认配置
            if nil ~= section["ipaddr"] then
                uci_r:set("network", section[".name"], "ipv6_enable", "on")
                uci_r:set("network", section[".name"], "ip6ifaceid", "manual")
                uci_r:set("network", section[".name"], "ip6addr", "fd00::1/64")
                uci_r:set("network", section[".name"], "prefixlen", "64")
                uci_r:set("network", section[".name"], "ip_mode", "manual")
            end
            is_save = true
        end
    end)

    if true == is_save then
        uci_r:commit("network")
        cfgsync.set_config_changed()
    end
end

network_ipv6_config()
