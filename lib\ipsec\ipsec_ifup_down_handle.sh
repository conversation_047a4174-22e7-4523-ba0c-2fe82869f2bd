#!/bin/sh
# handle the user zone(sec_zone) affairs for vpn device ifup/ifdown
#
#${ACTION} ifup or ifdown
#${IFNAME} device of vpn
#${PPPD_TYPE} ipsec
. /lib/iface_group/iface-group-ifup-down-function.sh

ACTION="$1"
IFNAME="$2"
PPPD_TYPE="$3"
bindif="$4"
user_zone="$5"

if [ ! -d ${UCI_VPN_IFACE_DIR} ];then
	mkdir ${UCI_VPN_IFACE_DIR}
fi
if [ ! -e ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE} ];then
	touch ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE}
fi

if [ "${PPPD_TYPE}" != "ipsec" ]; then
	return 1
fi

{
	flock -x 310

	case "${ACTION}" in
		"ifup")
			do_ifup;;
		"ifdown")
			do_ifdown;;
		*)
			return 1;;
	esac
	flock -u 310
} 310<>/tmp/vpn_iface_bindif_310.lock