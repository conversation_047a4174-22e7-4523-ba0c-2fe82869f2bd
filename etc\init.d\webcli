#!/bin/sh /etc/rc.common
# Copyright (C) 2025 TP-Link

START=99
CLI_SERVER_CMD="cli_server -t1 -n"
SOCAT_BIN="/usr/bin/socat"
PID_SAVE_PATH="/tmp/cli_server"
SOCAT_PID_PATH="${PID_SAVE_PATH}/webcli.pid"
WS_BIN="/usr/sbin/ws_cli"
WS_PID_PATH="${PID_SAVE_PATH}/websocket.pid"

start()
{
	[ ! -d ${PID_SAVE_PATH} ] && mkdir -p ${PID_SAVE_PATH}

	if [[ ! -f ${SOCAT_PID_PATH} && -e ${SOCAT_BIN} ]]; then
		local socat_host=$(uci get cli_server.socat.listen_address)
		local socat_port=$(uci get cli_server.socat.listen_port)

		#echo "&-> [socat]: start webcli binding ${socat_host}:${socat_port}" > /dev/console
		${SOCAT_BIN} tcp-listen:${socat_port},bind=${socat_host},fork exec:"${CLI_SERVER_CMD}",pty,raw,echo=0 &
		echo $! > ${SOCAT_PID_PATH}
	fi

	if [[ ! -f ${WS_PID_PATH} && -e ${WS_BIN} ]]; then
		#echo "&-> [websocket]: start webcli server" > /dev/console
		${WS_BIN} &
		echo $! > ${WS_PID_PATH}
		/lib/security_policy/webcli_control.sh on
	fi
}

stop()
{
	[ -f ${SOCAT_PID_PATH} ] && {
		#echo "&-> [socat]: stop webcli binding" > /dev/console
		kill -9 $(cat ${SOCAT_PID_PATH}) 2>/dev/null
		rm ${SOCAT_PID_PATH}
	}

	[ -f ${WS_PID_PATH} ] && {
		/lib/security_policy/webcli_control.sh off
		kill -9 $(cat ${WS_PID_PATH}) 2>/dev/null;
		rm ${WS_PID_PATH}
	}
}
