
# uncomment me to log hotplug events
# DEVPATH is set {
# 	exec logger -s -t hotplug -p daemon.info "name=%DEVNAME%, path=%DEVPATH%"
# }

$include /etc/hotplug2-platform.rules

DEVNAME ~~ (^null$|^full$|^ptmx$|^zero$|^gpio|^hvc) {
	makedev /dev/%DEVNAME% 0666
	next-event
}

DEVNAME == mapper/control {
	makedev /dev/%DEVNAME% 0600
	next-event
}

ACTION == add, DEVPATH is set {
	makedev /dev/%DEVNAME% 0644
}

ACTION == add, DEVPATH is set, DEVNAME ~~ ^tty {
	chmod 0666 /dev/%DEVNAME%
}

ACTION == add, DEVPATH is set, DEVNAME ~~ ^ppp {
	chmod 0600 /dev/%DEVNAME%
}

ACTION == remove, DEVPATH is set, MAJOR is set, MINOR is set {
	remove /dev/%DEVNAME%
}

FIRMWARE is set, ACTION == add {
	exec /sbin/hotplug-call firmware
	load-firmware /lib/firmware
	next-event
}

SUBSYSTEM == platform {
	exec /sbin/hotplug-call %SUBSYSTEM%
}
