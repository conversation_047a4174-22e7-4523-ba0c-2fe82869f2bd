#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

[ "$DEVICE" == "lo" ] && exit 0



case "$ACTION" in
	ifup)
		#fw_acl_event_interface "$INTERFACE" add
		[ $INTERFACE != "lan" -a $INTERFACE != "loopback" ] && {
			
			/lib/freeStrategy/add_delete.sh url &
		}
	;;
	ifdown)
		#fw_acl_event_interface "$INTERFACE" del
		#/lib/freeStrategy/interface.sh "$INTERFACE"
		
	;;
	ifupdate)
		# do nothing
	;;
esac