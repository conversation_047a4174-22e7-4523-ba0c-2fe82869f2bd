local sys = require("luci.sys")
local dbg = require "luci.tools.debug"
local interval = 3
local dev_check_dir="/tmp/health_check"
local dev_check_file = dev_check_dir.."/dev_score"

local MEM_USAGE_MAX =95
local CPU_USAGE_MAX =95
local sys_fail_count =0
local proc_fail_count =0
local FAIL_COUNT_MAX=3
local CPU_MAX = 64
local ubus = require("ubus")

local proc_list ={"uac","uhttpd","wifidog","netifd","monitor","dnsproxyd","cfg_save"}

function dev_check_file_init()
    sys.fork_call("mkdir -p "..dev_check_dir.."; echo 100 100 100 > "..dev_check_file)
end

function dev_check_file_ouput(sys_score,pro_score, ver_score)
    local str = sys_score.." "..pro_score.." "..ver_score
    local file = io.open(dev_check_file, "w")
    assert(file)
    file:write(str)
    file:close()
end

function check_cpu_mem()
    local _ubus= ubus.connect()
    local data = _ubus:call("monitor", "get_cpu_average", {})

    --cpu
    local cpu_avg = 0
    local cpu_sum = 0
    local core = 0
    local cpu_count=0;
    if data then
        for core = 1,CPU_MAX do
            if data["cpu"..tostring(core-1)] ~= nil then
                cpu_sum= cpu_sum +data["cpu"..tostring(core-1)]
                cpu_count = cpu_count+1
            end
        end
    end
    cpu_avg = cpu_sum/cpu_count;

    --mem
    data = _ubus:call("monitor", "get_mem_usage", {})
    _ubus:close()
    local mem_avg = 0
    if data then
        mem_avg = data.mem_usage
    end

    if mem_avg >= MEM_USAGE_MAX or cpu_avg >= CPU_USAGE_MAX then
        return false
    end
    return true
end

function get_sys_score()
    if check_cpu_mem()== false then
        if sys_fail_count < FAIL_COUNT_MAX then
            sys_fail_count = sys_fail_count +1
        end
        if sys_fail_count == FAIL_COUNT_MAX then
            return 0
        end
    else
        sys_fail_count=0
    end
    return 100
end

function check_proc_score()
    local line
    for _,proc in pairs(proc_list) do
        local fp = io.popen('ps | grep -w ' .. proc..' | grep -v grep | wc -l')
        if fp ~= nil then
            line = fp:read('*l')
            if line ~= nil then
                if string.len(line) ~= 0 then
                    if tonumber(line) ==0 then
                        fp:close()
                        return false
                    end
                end
            end
            fp:close()
        end
    end
    return true
end

function get_proc_score()
    if check_proc_score()== false then
        if proc_fail_count < FAIL_COUNT_MAX then
            proc_fail_count = proc_fail_count +1
        end
        if proc_fail_count == FAIL_COUNT_MAX then
            return 0
        end
    else
        proc_fail_count=0
    end
    return 100
end

function get_ver_score()
    return 100
end

function dev_check()
    local sys_score= 100
    local proc_score =100
    local ver_score =100
    dev_check_file_init()

    -- while true do
    --     sys.fork_call("sleep "..interval..";")
    --     sys_score = get_sys_score()
    --     proc_score = get_proc_score()
    --     ver_score = get_ver_score()
    --     dev_check_file_ouput(sys_score,proc_score,ver_score)
    -- end
end

dev_check()
