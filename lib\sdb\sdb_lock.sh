#!/bin/sh

# lock interface for CSE engine
# $1: owner description (text)
# $2: caller PID
sdb_lock_take() {
    local desc=$1
    local pid=$2

    if [ "$CSEDB_LOCKED" != "1" ]; then
        custom_lock -l "security" -f "fw_policy_cse_db_lock" -p "$pid" -t "$pid" -T "1800" -w "1" -F "1"
        export CSEDB_LOCKED=1
        export CSEDB_LOCK_OWNER=${desc}_${pid}
    fi
}

# unlock interface for CSE engine
# $1: owner description (text)
# $2: caller PID
sdb_lock_give() {
    local desc=$1
    local pid=$2

    if [ "$CSEDB_LOCKED" = "1" ] && [ "$CSEDB_LOCK_OWNER" = "${desc}_${pid}" ]; then
        custom_lock -n "security" -f "fw_policy_cse_db_lock" -p "$pid" -t "$pid" -T "1800" -w "1" -F "1"
        export CSEDB_LOCKED=0
        export CSEDB_LOCK_OWNER=""
    fi
}
