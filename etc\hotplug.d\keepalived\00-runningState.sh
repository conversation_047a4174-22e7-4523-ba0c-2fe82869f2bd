#!/bin/sh
#author qiufusheng

case ${ACTION} in
    master)
		local path="/tmp/keepalived"
		if [ ! -d  $path ];then
			mkdir -p $path
		fi
		local file="/tmp/keepalived/${INSTANCE}"
		if [ ! -f $file ];then
			touch $file
		fi
		echo "master" > $file
    ;;
    backup)
		local path="/tmp/keepalived"
		if [ ! -d  $path ];then
			mkdir -p $path
		fi
		local file="/tmp/keepalived/${INSTANCE}"
		if [ ! -f $file ];then
			touch $file
		fi
		echo "backup" > $file
    ;;
    stop)
		rm -rf "/tmp/keepalived/${INSTANCE}"
    ;;
    *)
        # do nothing
    ;;
esac
