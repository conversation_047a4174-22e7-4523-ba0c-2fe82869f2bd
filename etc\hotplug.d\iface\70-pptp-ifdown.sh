#!/bin/sh
#author luopei
#brief dealing with interface up/down events

. /lib/zone/zone_api.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/pptp/pptp-option.sh
. /lib/firewall/fw.sh
. /lib/access_ctl/core_acl.sh
. /lib/pptp/pptp-ifdevice-info.sh

kill_server_pppd(){
	local service_name=$1
	for file in `ls ${pppox_ppppath}/pid 2>/dev/null`; do
		local getisserver=`uci -c ${pppox_ppppath}/pid get ${file}.@info[0].isserver 2>/dev/null`
		getconfigpath=`uci -c ${pppox_ppppath}/pid get ${file}.@info[0].configpath 2>/dev/null`
		
		[ "${getisserver}" != "1" ] && continue	
		[ "$service_name" !=  "$getconfigpath" ] && continue
		[ -f "${getconfigpath}/config/pptp.type" ] && kill -TERM ${file} #/lib/pptp/pptp-tunnel-action.sh '1' $file
	done
}

kill_pppd(){
	local pppfile=${pppox_pptp_client_path}/$1/config/ppp
	local mgrfile=${pppox_pptp_client_path}/$1/config/mgr
	local mgrfather=${pppox_pptp_client_path}/$1/config/mgrfather

	[ -e $mgrfather ] && kill -9 $(cat $mgrfather)
	[ -e $pppfile ] && kill -TERM $(cat $pppfile)
	[ -e $mgrfile ] && kill -9 $(cat $mgrfile)
	[ -e $mgrfather ] && rm -f $mgrfather
	[ -e $pppfile ] && rm -f $pppfile
	[ -e $mgrfile ] && rm -f $mgrfile
}

deal_with_pptp_server(){
	local path=${pppox_pptp_server_path}/$1/config
	local zone=$2
	local enable=$3
	local local_service_chain=local_service_rule
	case $ACTION in
	"ifup")
	  [ -n "${usingdev}" ] && fw del 4 f $local_service_chain ACCEPT $ { -i ${usingdev} -p tcp --dport 1723 }
	  kill_server_pppd $1
	  #need to tell pptpd that a new interface is up,notice that script in htoplug.d/firewall will add the rules
	  #local devices=$(zone_get_effect_devices $2)
	  local updevice=$(zone_get_device_byif $INTERFACE)
	  local str="m$(basename ${1})#$updevice"
	  pptp_set_ifdevice_info "${INTERFACE}"  "$(basename ${1})"
	  #echo "upevent,$str" >>/tmp/pptp-server-debug
	  fifo_write "$str"
	  #send to pptpd
	  
	  #local updevice=$(zone_get_device_byif $INTERFACE)
	  fw_check "filter" "local_service_rule" "-i ${updevice} -p tcp -m tcp --dport 1723 -j ACCEPT"
	  [ x$? == x0 ] && fw add 4 f $local_service_chain ACCEPT $ { -i ${updevice} -p tcp --dport 1723 }
	;;
	
	"ifdown")
	  local downdevice=$(zone_get_device_byif $INTERFACE)
	  downdevice=${downdevice:-${usingdev}}
	  #echo "down event,interface=$INTERFACE,downdevice=$downdevice" >>/tmp/pptp-server-debug
	  fw del 4 f $local_service_chain ACCEPT $ { -i ${downdevice} -p tcp --dport 1723 }
	  kill_server_pppd $1
	  pptp_set_ifdevice_info "${INTERFACE}"  "$(basename ${1})"
	;;
	esac
}


deal_with_pptp_client(){
	case ${ACTION} in
	"ifup")
		#kill pppd & start it again if neccessary
		kill_pppd $1
		#start the new pppd?
		local path=${pppox_pptp_client_path}/$1/config
		local username=$(uci -c $path get ${pppox_configname}.@pnc[0].username)
		local password=$(uci -c $path get ${pppox_configname}.@pnc[0].password)
		local pns=$(uci -c $path get ${pppox_configname}.@pnc[0].pns)
		local enable=$(uci -c $path get ${pppox_configname}.@pnc[0].enable)
		local device=$(zone_get_device_byif $INTERFACE)
		local outif=$device
		pptp_set_ifdevice_info "${INTERFACE}"  "${1}"
		client_start_pppd $username $password $pns $outif $path
	;;
	
	"ifdown")
		#kill the old pppd if there is any
		kill_pppd $1
		pptp_set_ifdevice_info "${INTERFACE}"  "${1}"
	;;
	esac
}

#dir is the same with uniqname
check_pptp(){
	local checkpath=$1
	local checktype=$2
	local runfunc=$3
	
	for dir in $(ls ${checkpath} 2>/dev/null);do
		[ ! -e ${checkpath}/${dir}/config ] && continue
		local path=${checkpath}/${dir}
		if [ -d $path ];then
			local option=""
			local interfaces=""
			local enable=""
			local usingiff=`uci -c ${pppox_pptp_main_path} get ${pptp_ifdevice_info}.${dir}.iff`
			usingdev=`uci -c ${pppox_pptp_main_path} get ${pptp_ifdevice_info}.${dir}.dev`

			[ $2 = "client" ] && {
				option=$(uci -c ${path}/config get ${pppox_configname}.@pnc[0].outif)
				enable=$(uci -c ${path}/config get ${pppox_configname}.@pnc[0].enable)
				[ $enable = "0" -o $enable = 'off' ] && continue
				#interfaces=$(zone_get_ifaces $option)
				#echo "$interfaces" | grep -q "$INTERFACE" && $runfunc $dir 
				optiondev=`zone_get_effect_devices ${option}`
				#echo "$option" | grep -q "$INTERFACE" && $runfunc $dir
				if [ "${ACTION}" = "ifup" ]; then
					[ "${indev}" = "${optiondev}" ] && $runfunc $dir
				else
					if [ -n "${INTERFACE}" -a "${INTERFACE}" = "${usingiff}" ] ||
						[ -n "${indev}" -a "${indev}" = "${usingdev}" ]; then
						$runfunc $dir
					fi
				fi
			}
			[ $2 = "server" ] && {
				option=$(uci -c ${path}/config get ${pppox_configname}.@pns[0].bindif)
				#interfaces=$(zone_get_ifaces $option)
				enable=$(uci -c ${path}/config get ${pppox_configname}.@pns[0].enable)
				[ $enable = "0" -o $enable = 'off' ] && continue
				#echo "$interfaces" | grep -q "$INTERFACE" && $runfunc $dir $option $enable 
				optiondev=`zone_get_effect_devices ${option}`
				#echo "$option" | grep -q "$INTERFACE" && $runfunc $path $option $enable 
				if [ "${ACTION}" = "ifup" ]; then
					[ "${indev}" = "${optiondev}" ] && $runfunc $path $option $enable 
				else
					if [ -n "${INTERFACE}" -a "${INTERFACE}" = "${usingiff}" ] ||
						[ -n "${indev}" -a "${indev}" = "${usingdev}" ]; then
						$runfunc $path $option $enable
					fi
				fi
			}
		fi
	done
}

usingdev=""
if [ -n "${INTERFACE}" ]; then
	indev=`zone_get_effect_devices ${INTERFACE}`
elif [ -n "${DEVICE}" ]; then
	indev=${DEVICE}
fi

check_pptp $pppox_pptp_client_path "client" deal_with_pptp_client
check_pptp $pppox_pptp_server_path "server" deal_with_pptp_server
