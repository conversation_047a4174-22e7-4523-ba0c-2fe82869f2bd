#!/bin/sh

_delete_rule() {
    local cfg="$1"
    local iface="$2"
    local out_if

    out_if=$(uci_get remote_mngt "$cfg" out_if)
    echo "cfg=$cfg,iface=$iface,out_if=$out_if"  >> /tmp/remote_mngt_vpnhook.log
    [ "$out_if" == "$iface" ] && {
        uci delete remote_mngt.$cfg
        uci_commit dnsproxy
    }
}

case ${ACTION} in
    DELETE)
        [ -n "${interfaces}" ] && {
            echo "interfaces=$interfaces" >> /tmp/remote_mngt_vpnhook.log
            interfaces=${interfaces//,/ }
            for element in $interfaces
            do
                echo "element=$element"  >> /tmp/remote_mngt_vpnhook.log
                config_load remote_mngt
                config_foreach _delete_rule rule $element
            done
        }
    ;;
    ADD)
    ;;
    *)
    ;;
esac

