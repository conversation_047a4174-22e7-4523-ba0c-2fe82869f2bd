#!/bin/sh

_delete_rule() {                                        
    local cfg="$1"                                     
    local iface="$2"                                                                                       
    local interface   

    interface=$(uci_get cmxddns "$cfg" interface)
    echo "cfg=$cfg,iface=$iface,interface=$interface"  >> /tmp/cmxddns_vpnhook.log    	
    [ "$interface" == "$iface" ] && {      
        uci delete cmxddns.$cfg    
        uci_commit cmxddns  		
    }                                                         
}

case ${ACTION} in
	DELETE)  
		[ -n "${interfaces}" ] && {
		    echo "interfaces=$interfaces" >> /tmp/cmxddns_vpnhook.log    
		    interfaces=${interfaces//,/ } 
			for element in $interfaces   
			do  
				echo "element=$element"  >> /tmp/cmxddns_vpnhook.log    
				config_load cmxddns
				config_foreach _delete_rule cmxddns $element
			done  
		}
	;;
	ADD)
	;;
	*)
	;;
esac