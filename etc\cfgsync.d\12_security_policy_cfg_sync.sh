#!/usr/bin/lua

local dbg     = require "luci.torchlight.debug"
local uci     = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"

local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()

local DEFAULT_POLICY = "security_policy_default"
local LOCALOUT_POLICY = "security_policy_localout"

local function security_policy_sync()
    uci_r:load(conf_dir)
    local is_changed = false

    uci_r.foreach("security_policy", "sec_policy", function(conf_section)
        -- 修改action字段，默认策略的action是可设置的，也需要处理
        if "drop" == conf_section["action"] then
            -- "drop"替换成"block"
            uci_r:set("security_policy", conf_section[".name"], "action", "block")
            is_changed = true
        end
        -- 跳过默认策略
        if conf_section[".name"] ~= DEFAULT_POLICY and conf_section[".name"] ~= LOCALOUT_POLICY then
            -- 删除policy_group字段
            if nil ~= conf_section["policy_group"] then
                uci_r:delete("security_policy", conf_section[".name"], "policy_group")
                is_changed = true
            end
            -- record_log替换成sp_hit_log_enable
            if nil ~= conf_section["record_log"] then
                if "yes" == conf_section["record_log"] then
                    -- "yes"替换成"on"
                    uci_r:set("security_policy", conf_section[".name"], "sp_hit_log_enable", "on")
                else
                    -- "no"替换成"off"
                    uci_r:set("security_policy", conf_section[".name"], "sp_hit_log_enable", "off")
                end
                uci_r:delete("security_policy", conf_section[".name"], "record_log")
                is_changed = true
            end
        end
    end)

    if is_changed == true then
        uci_r:commit("security_policy")
        cfgsync.set_config_changed()
    end
end

security_policy_sync()
