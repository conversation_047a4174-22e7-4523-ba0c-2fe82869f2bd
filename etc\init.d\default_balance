#!/bin/sh /etc/rc.common

START=96

IPT="iptables -t mangle -w"

start()
{
	. /lib/balance/api.sh

	state=`get_balance_global_state`
	[ "$state" == "off" ] && return

	. /usr/sbin/default_balance

	local ifaces count=0 e_iface if_state on_ifaces counter=0

	ifaces=`get_wan_interfaces`
	if [ -n "$ifaces" ];then
		for iface in $ifaces;do
			e_iface=`zone_get_effect_ifaces $iface`
			while [ -z "$e_iface" -a "$counter" -lt 10 ]; do
				sleep 1
				e_iface=`zone_get_effect_ifaces $iface`
				let counter++
				if [ "$counter" -ge 10 ]; then
					logger -t default_balance -p warn "Could not get ifaces in start, iface=[$iface]."
					break
				fi
			done

			[ -n "$e_iface" ] || continue

			if_state=`zone_get_iface_state $e_iface`
			[ "$if_state" != "UP" ] && continue

			state=`balance_get_state $iface`

			if [ "$state" == "on" ];then
				append on_ifaces $iface
				let count++
			fi
		done
	fi

	if [ "$count" -gt 0 ];then
		local weight_str=$(default_balance_compose_weight_string "$on_ifaces")
		# param: iface_num|weight_str
		# e.g. 3|10,10,7 for GE1:GE2:GE3=10:10:7
		echo "$count|$weight_str" > /proc/balance_route

		default_balance "$on_ifaces"
	fi

	touch /tmp/default_balance.ready
}

stop()
{
	#delete ip rule
	for rule in $(ip rule list | egrep '^[9][0-9]{3}\:' | cut -d ':' -f 1); do
		ip rule del pref $rule &> /dev/null
	done
	#notice: pref from 10001
	for rule in $(ip rule list | egrep '^[1][0-9]{4}\:' | cut -d ':' -f 1); do
		ip rule del pref $rule &> /dev/null
	done

	#delete default_balance
	$IPT -D PREROUTING -j default_balance &>/dev/null
	$IPT -F default_balance  &> /dev/null
	$IPT -X default_balance &> /dev/null

	echo "0|0" > /proc/balance_route
}

restart() {
	( flock -x 68
		stop
		start
	  flock -u 68
	) 68<>/tmp/.default_balance_lock
}
