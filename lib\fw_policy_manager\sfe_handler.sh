#!/bin/sh

. /lib/functions.sh

# purpose: 统计需要用到安全引擎的策略
# return:
#   n - 当前有n个策略用到安全引擎
count_sec() {
    local section=$1
    local security_options="app_groups av_profile ips_conf app_control_filter_conf file_content_filter_conf content_filter_conf url_filter_conf email_filter_conf industrial_control_conf"

    config_get enable $section enable
    config_get time $section time

    if [ $enable"x" == "offx" ]; then
        return 0
    fi

    # 软件加速后，后续包不会经过策略模块，当超出时间范围后，只要流不老化，包依旧能够正常转发。
    if ! [ $time"x" == "anyx" ]; then
        let cnt++
        return 0
    fi

    for opt in $security_options;
    do
        config_get item $section $opt
        if [ -n "$item" ]; then
            let cnt++
            break
        fi
    done
}

check_audit() {
	local section=$1

	config_get action $section action

	if [ $action"x" == "customx" ]; then
		status="off"
	fi
}

update_sfe_switch() {
	if [ -x "$(which sfe_switch)" ]; then
		local cnt=0
		local status="off"

		# Check security_policy
		config_load security_policy
		config_foreach count_sec sec_policy

		local http_resume_enable=$(uci get security_global_config.global_config.http_resume_enable 2>/dev/null)
		if [ "$http_resume_enable" = "on" ]; then
			let cnt++
		fi

		local ftp_resume_enable=$(uci get security_global_config.global_config.ftp_resume_enable 2>/dev/null)
		if [ "$ftp_resume_enable" = "on" ]; then
			let cnt++
		fi

		local smart_mode_enable=$(uci get app_library.global.smart_mode_enable 2>/dev/null)
		if [ "$smart_mode_enable" = "off" ]; then
			let cnt++
		fi

		if [ $cnt -eq 0 ]; then
			status="on"
		else
			status="off"
		fi
		sfe_switch update security_policy $status

		# Check audit_policy
		local action="none"
                status="on"

		config_load audit_policy
		config_foreach check_audit audit_policy

		sfe_switch update audit_policy $status
	fi
}

action=$1

if [ -x "$(which sfe_switch)" ]; then
        case $action in
        register)
                sfe_switch register security_policy off
                sfe_switch register audit_policy on
        ;;
        update)
                update_sfe_switch
        ;;
        esac
fi
