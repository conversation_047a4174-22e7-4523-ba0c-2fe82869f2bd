#!/bin/sh
notify_cloud_cfg_to_sync()
{
	echo `date` >> /tmp/cfg_save/tmp_log_for_check
	echo "notify_cloud_cfg_to_sync trigger reset config_id $1 pid=$$">>/tmp/cfg_save/tmp_log_for_check
	local config_id=`uci get cloud_sync.config_id.config_id`
	if [ -n "$1" ]&&[ "1" == "$1" ]&&[ ! "0" == "$config_id" ];then
		uci set cloud_sync.config_id.config_id='0'
		uci commit cloud_sync
	fi
}
#if cfg wasn't save complete or cfg was damage, will reset config_id to notify cloud to sync cfg again
check_cloud_cfg()
{
	#由于save_done这个标记配置比较特殊，在运行时不存在的，故并不是全路径
	#若是其他配置，比如无线服务的数据，就需要全路径，命令为cfgSave -c file -O "/etc/nouci_config/dbs/wserv.db"
	cfgSave -c file -O "save_done"
	if [ ! "$?" == "0" ];then
		notify_cloud_cfg_to_sync "1"
		echo `date` >> /tmp/cfg_save/tmp_log_for_check
		echo "save done is not ok to trigger reset config_id">>/tmp/cfg_save/tmp_log_for_check
	fi
}