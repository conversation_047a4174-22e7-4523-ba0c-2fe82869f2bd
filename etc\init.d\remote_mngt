#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1
START=89

RMC_LIBDIR=/lib/remote_mngt

rmc(){
	. $RMC_LIBDIR/remote_mngt.sh
	rmc_$1
}

start(){
		rmc start
		[ ! -f /tmp/remote_mngt.ready ] && touch /tmp/remote_mngt.ready
}

stop(){
	rmc stop
}

restart(){
	echo "rmc restart"
	{
		flock 5
	rmc restart
	} 5<>/tmp/remote_mngt.lock
}

reload(){
	echo "rmc reload"
        {
	       flock 5
	rmc reload
        } 5<>/tmp/remote_mngt.lock
}
