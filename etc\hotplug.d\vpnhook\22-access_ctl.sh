#!/bin/sh

. /lib/functions.sh

delinfs=

_acl_delete_rule() {                                        
    local section=${1}
	config_get effect_zone ${section} zone
	
    for element in $interfaces; do  
		[ "${element}" = "${effect_zone}" ] && {
			uci delete access_ctl.${section}
		}
	done
}

_acl_add_rule() {
	local zone=${1}
	local flag="1"
	local policy="DROP"
	local src="IPGROUP_LAN"
	local time="ANY"
	local dest="IPGROUP_LAN"
	local service="ALL"
	local num="acl"

	uci add access_ctl rule_acl_inner
	uci set access_ctl.@rule_acl_inner[-1].zone=${zone}
	uci set access_ctl.@rule_acl_inner[-1].flag=${flag}
	uci set access_ctl.@rule_acl_inner[-1].policy=${policy}
	uci set access_ctl.@rule_acl_inner[-1].src=${src}
	uci set access_ctl.@rule_acl_inner[-1].time=${time}
	uci set access_ctl.@rule_acl_inner[-1].dest=${dest}
	uci set access_ctl.@rule_acl_inner[-1].service=${service}
	uci set access_ctl.@rule_acl_inner[-1].num=${num}
	uci commit access_ctl

	src="IPGROUP_ANY"
	dest="ME"

	uci add access_ctl rule_acl_inner
	uci set access_ctl.@rule_acl_inner[-1].zone=${zone}
	uci set access_ctl.@rule_acl_inner[-1].flag=${flag}
	uci set access_ctl.@rule_acl_inner[-1].policy=${policy}
	uci set access_ctl.@rule_acl_inner[-1].src=${src}
	uci set access_ctl.@rule_acl_inner[-1].time=${time}
	uci set access_ctl.@rule_acl_inner[-1].dest=${dest}
	uci set access_ctl.@rule_acl_inner[-1].service=${service}
	uci set access_ctl.@rule_acl_inner[-1].num=${num}
	uci commit access_ctl
}


case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {   
		    echo "interfaces=$interfaces" >> /tmp/access_ctl_wanhook.log    
		    interfaces=${interfaces//,/ } 
			#config_load access_ctl
			#config_foreach _acl_delete_rule rule_acl_inner
			#uci_commit access_ctl

			#/etc/init.d/access_ctl reload
			lua /lib/access_ctl/acl_wanhook.lua del "${interfaces}"
			/etc/init.d/access_ctl reload
		}
	;;
	ADD)
		[ -n "${interfaces}" ] && {
			echo "interfaces=$interfaces" >> /tmp/access_ctl_wanhook.log    
		    interfaces=${interfaces//,/ } 
		    #for element in ${interfaces}; do 
		    #	_acl_add_rule ${element}
		    #done

		    #/etc/init.d/access_ctl reload
		}
	;;
	*)
	;;
esac