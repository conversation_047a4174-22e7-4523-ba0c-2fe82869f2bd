#!/bin/sh

# 拔出存储设备时检查是否umount。

SDX1_MOUNT_POINT=/mnt/cfg
SDX2_MOUNT_POINT=/mnt/data
MOUNTS_PATH=/proc/mounts
USB_DISK_INFO_FILE=/tmp/usb_storage_disk.info

[ "$SUBSYSTEM" = block ] || exit0

[ "$DEVTYPE" = partition -a "$ACTION" = remove ] && {
    if [ -f $USB_DISK_INFO_FILE ]; then
        rm $USB_DISK_INFO_FILE
    fi
    cat $MOUNTS_PATH |grep -q $SDX1_MOUNT_POINT
    local sdx1_exist=$?
    cat $MOUNTS_PATH |grep -q $SDX2_MOUNT_POINT
    local sdx2_exist=$?
    if [ 0 -eq $sdx1_exist -o 0 -eq $sdx2_exist ]
    then
        POSSIBLE_DEVICES="mmcblk0p1 mmcblk0p2 sda1 sda2"
        if list_contains POSSIBLE_DEVICES $DEVICENAME; then
            /etc/init.d/loggerd stop
            echo "kill loggerd" > /dev/console
            sleep 4
            umount -f ${SDX1_MOUNT_POINT}
            local umntCfg=$?
            sleep 2
            umount -f ${SDX2_MOUNT_POINT}
            local umntData=$?
            sleep 2
            if [[ ${umntCfg} == 0 && ${umntData} == 0 ]]
            then
                sleep 2
                /etc/init.d/loggerd start
                echo "start loggerd" > /dev/console
                return 0
            else
                /etc/init.d/loggerd start
                echo "disk umount fail" > /dev/console
                echo "start loggerd" > /dev/console
                return 1
            fi
        fi
    fi

}