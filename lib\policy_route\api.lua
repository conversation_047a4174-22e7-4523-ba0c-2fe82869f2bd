#!/usr/bin/lua

-- arg[1]: 调用的函数名
-- arg[2]: 函数的参数

local cjson = require("cjson")
local sys = require("luci.sys")
local uci = require("luci.model.uci")
local dbg = require "luci.torchlight.debug"
local socket = require "socket"
local policy_route_api = require("policy_route.policy_route_api")

---------------------------------------------------------------------------------------------------------------------------
-------------------------------------------------- 全局变量定义区 -------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------------
local cmd = arg[1]
local para = table.concat(arg, " ", 2, #arg)

function policy_log(log_fmt, ...)
    if nil == log_fmt then
        return ;
    end

    local prefix = string.format("[policy_route] {{cmd: %s; arg: %s}}: ", cmd or "", para or "")

    log_fmt = prefix..log_fmt

    if 1 == POLICTY_DBG then
        dbg(string.format(log_fmt, ...))
    end
end

---------------------------------------------------------------------------------------------------------------------------
------------------------------------------------- 处理流程区 --------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------------
local API = {
    ["add_policy_rule"] = policy_route_api.add_policy_rule,
    ["del_policy_rule"] = policy_route_api.del_policy_rule,
    ["handle_balance_event"] = policy_route_api.handle_balance_event,
    ["update_policy_rule"] = policy_route_api.update_policy_rule,
    ["handle_time_event"] = policy_route_api.handle_time_event,
    ["append_policy_rule"] = policy_route_api.append_policy_rule,
    ["handle_ipsec_iface_event"] = policy_route_api.handle_ipsec_iface_event,
    ["handle_iface_event"] = policy_route_api.handle_iface_event,
    ["start_policy_route"] = policy_route_api.start_policy_route,
}

local start_time = socket.gettime()

policy_log("coming in case, now time is %d", os.time())

if not cmd then
    return
end

local func = API[cmd]

if not func then
    return
end

if "handle_balance_event" == cmd or "handle_time_event" == cmd or "handle_ipsec_iface_event" == cmd then
    func(arg[2], arg[3])
else
    func(arg[2])
end

local end_time = socket.gettime()

policy_log("complete case, now time is %d", os.time())
policy_log("this case cost time %s seconds", tostring(end_time-start_time))

