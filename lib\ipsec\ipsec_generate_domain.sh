#!/bin/sh
#/etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file	  ipsec_generate_domain.sh
# brief	  generate uci file : /etc/config/ipsec_check_dns from /etc/config/vpn
#			which be checked by ipsec_check_domain.sh
# author   <PERSON>
# version  1.0.0
# date	   08June15
# histry   arg 1.0.0, 16July15, <PERSON>, Create the file.


. /lib/functions.sh
. /lib/functions/network.sh
. /lib/zone/zone_api.sh
include /lib/network

UCI_DEFAULT_SEARCH_DIR=/etc/config
UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE=ipsec_check_dns
UCI_IPSEC_CONFIG_FILE=vpn
IPSEC_SECTION=ipsec_connection
#该section名字是ipsec_check_dns文件的config 字段的名字.
CHECKED_DOMAIN_SECTION=domain

#check is ipaddr. 1 for false, 0 for true
CheckIPAddr()
{
	echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null;
	#IP地址必须为全数字
	if [ $? -ne 0 ]
	then
		return 1
	fi

	ipaddr=$1
	a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值
	b=`echo $ipaddr|awk -F . '{print $2}'`
	c=`echo $ipaddr|awk -F . '{print $3}'`
	d=`echo $ipaddr|awk -F . '{print $4}'`
	for num in $a $b $c $d
	do
		if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间
		then
			return 1
		fi
	done
	return 0
}

##
#virtulif_to_user_if(){
###	${0}: shell name
##	${1}: section name
#	${2}: input file
#	${3}: save tmpfile
#	${4}: the virtualif name which only generated from.
#	config_get name $1 name
#	test $4
#	if [ $? -ne 1 ]
#	then
#		if [ ${name} != $4 ]
#		then
#			#echo "virtualif name is not equal.\"${name}\" !=	\"$4\""
#			return
#		fi
#	fi
#	local userif=""
#	config_get userif $1 bindif
#	echo $userif > ${3}
#	echo "********virtulif_to_user_if() test 1*********" > /dev/console
#	echo "userif: ${userif}" > /dev/console
#	cat ${3} > /dev/console
#}
#

generate_from_uci_ipsec(){
	#${0}: shell name
	#${1}: section name
	#${2}: input file
	#${3}: output file
	#${4}: the connection name which only generated from.
	let ignore_dns_resovle=0
	config_get name $1 name
	#echo "Checking ${IPSEC_SECTION} section: ${name}."
	test $4
	if [ $? -ne 1 ]
	then
		if [ ${name} != $4 ]
		then
			#echo "connection name is not equal.\"${name}\" !=	\"$4\""
			return
		fi
		let ignore_dns_resovle=1
	#else
	#	echo "\$4 doesn't exit"
	fi

	#echo "bindif is a domain name ,add to the file."
	config_get bindif $1 bindif
	echo "" >> ${3}
	echo "config domain" >> ${3}
	echo "	option domain_name '${bindif}'" >> ${3}
	echo "	#1: DOMAIN_NORMAL,2: DOMAIN_SPECIAL" >> ${3}

	local old_ipaddr=""
	echo "	option domain_type '2'" >> ${3}

#	config_get ipsec_vpn_mode $1 ipsec_vpn_mode
#	local tmpfile="/tmp/varible"
#	if [ ${ipsec_vpn_mode} == "ROUTE_MODE" ]
#	then
#		config_load virtualif
#		config_foreach virtulif_to_user_if virtualif virtualif ${tmpfile} ${bindif}
#	fi
#	bindif=$(cat ${tmpfile})
#	rm ${tmpfile}

	local effect_iface=$(zone_userif_to_effective_iface ${bindif})

	network_get_ipaddr old_ipaddr ${effect_iface}

#	echo "binding_item/name: ${name}" > /dev/console

	echo "	option binding_file '${2}'" >> ${3}
	echo "	option binding_section_type '${IPSEC_SECTION}'" >> ${3}
	echo "	option binding_item	 '${name}'" >> ${3}
	echo "	option binding_field  'bindif'" >> ${3}
	#因为dnsq查询的时候需要指定从那个接口发送出去，即：该域名所属的隧道条目的本地绑定的接口名称，因此需要多加一个参数，其它地方没有用到，一下同�?
	echo "	option tunnel_local_binding	 '${bindif}'" >> ${3}
#	echo "old_ipaddr: ${old_ipaddr}" > /dev/console
	test "${old_ipaddr}"
	if [ $? -eq 1 -o ${old_ipaddr} = "0.0.0.0" ]
	then
		echo "	option old_ipaddr	'xx.xx.xx.xx'" >> ${3}
	else
		echo "	option old_ipaddr	'${old_ipaddr}'" >> ${3}
	fi

	config_get remote_peer $1 remote_peer
	CheckIPAddr $remote_peer
	#Incase of reponder mode, the remote_peer have to be 0.0.0.0
	#Skip valid ip
	if [ $? -ne 0 ]
	then
		config_get bindif $1 bindif
		echo "" >> ${3}
		echo "config domain" >> ${3}
		echo "	option domain_name '${remote_peer}'" >> ${3}
		echo "	#1: DOMAIN_NORMAL,2: DOMAIN_SPECIAL" >> ${3}
		local old_ipaddr=""
		echo "	option domain_type '1'" >> ${3}
		if [ ${ignore_dns_resovle} -eq 0 ]
		then
			old_ipaddr=$(dnsq ${local_binding} ${remote_peer} )
		else
			old_ipaddr="xx.xx.xx.xx"
		fi
		echo "	option binding_file '${2}'" >> ${3}
		echo "	option binding_section_type '${IPSEC_SECTION}'" >> ${3}
		echo "	option binding_item	 '${name}'" >> ${3}
		echo "	option binding_field  'remote_peer'" >> ${3}
		echo "	option tunnel_local_binding	 '${bindif}'" >> ${3}
		test "${old_ipaddr}"
		#Domain's IP should not be 0.0.0.0
		#dnsq will return 0.0.0.0 in a failure
		if [ $? -eq 1  -o ${old_ipaddr} = "0.0.0.0" ]
		then
			echo "	option old_ipaddr	'xx.xx.xx.xx'" >> ${3}
		else
			echo "	option old_ipaddr	'${old_ipaddr}'" >> ${3}
		fi
	fi
}

delete_from_checked_file(){
	#${0}: shell name
	#${1}: section name
	#${2}: the connection name which will be deleted.
	#${3}: count
	config_get binding_item $1 binding_item

	test $2
	if [ $? -eq 1 ]
	then
		#echo "\"$2\" doesn't exit."
		return
	elif [ ${binding_item} != $2 ]
	then
		#echo "\"$2\" vs ${binding_item} let it go ."
		return
	else
		#echo "\"$2\" vs ${binding_item} delete it=$1."
		uci delete ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}."${1}"
	fi
}

USAGE(){
	echo "Uage: ipsec_generate_domain.sh <commands> [connection_name]"
	echo ""
	echo "Commands:"
	echo "	renew ---- rebuild all the domain to \"ipsec_check_dns\" from scratch, and it will ignore the [connection_name]."
	echo "	 add  ---- only add the domain of connection [connection_name] to the \"ipsec_check_dns\". "
	echo "	 del  ---- only del the domain of connection [connection_name] from the \"ipsec_check_dns\". "
	echo "	 lock ---- lock the whole \"ipsec_check_dns\" file."
	echo "	 unlock ---- unlock the whole \"ipsec_check_dns\" file."
	echo "For example: "
	echo "	./ipsec_generate_domain.sh renew "
	echo "	./ipsec_generate_domain.sh add u_s_t_c "
	echo "	./ipsec_generate_domain.sh del tunnel_1 "

}


if [ $# -le 0 ]
then
	USAGE
	exit 0
else
	if [ $1 = "renew" ]
	then
		test "${2}"
		if [ $? -ne 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi

	elif [ $1 = "add" ]
	then
		test "${2}"
		if [ $? -eq 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi
		test "${3}"
		if [ $? -ne 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi

	elif [ $1 = "del" ]
	then
		test "${2}"
		if [ $? -eq 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi
		test "${3}"
		if [ $? -ne 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi
	elif [ $1 = "lock" ]
	then
		test "${2}"
		if [ $? -ne 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi
	elif [ $1 = "unlock" ]
	then
		test "${2}"
		if [ $? -ne 1 ]
		then
			echo "Invalid command."
			USAGE
			exit 0
		fi
	else
		echo "command \"$1\" not surported."
		USAGE
		exit 0
	fi
fi


if [ $1 = "renew" ]
then
	{
		flock -x 30
		[ $? -eq 1 ] && {  echo "ipsec_generate_domain.sh get lock(/var/lock/ipsec_dnsq) fail.";  exit 1; }

		rm -rf ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}

		lua /usr/lib/lua/ipsec/ipsec_generate_domain.lua renew

		flock -u 30
} 30>>/var/lock/ipsec_dnsq
elif [ $1 = "add" ]
then
	{
		flock -x 30
		[ $? -eq 1 ] && {  echo "ipsec_generate_domain.sh get lock(/var/lock/ipsec_dnsq) fail.";  exit 1; }

		#step1: delete the bind_item
		lua /usr/lib/lua/ipsec/ipsec_generate_domain.lua delete $2

		#step2: generate from /etc/config/vpn
		lua /usr/lib/lua/ipsec/ipsec_generate_domain.lua add $2

		flock -u 30
	} 30>>/var/lock/ipsec_dnsq
elif [ $1 = "del" ]
then
	{
		flock -x 30
		[ $? -eq 1 ] && {  echo "ipsec_generate_domain.sh get lock(/var/lock/ipsec_dnsq) fail.";  exit 1; }

		#delete from /etc/config/vpn
		lua /usr/lib/lua/ipsec/ipsec_generate_domain.lua delete $2

		flock -u 30
	} 30>>/var/lock/ipsec_dnsq
elif [ $1 = "lock" ]
then
	{
		flock -x 37
		echo "lock" > /tmp/lock/ipsec_web
		flock -u 37
	} 37>>/var/lock/ipsec_web_lock
elif [ $1 = "unlock" ]
then
	{
		flock -x 37
		echo "unlock" > /tmp/lock/ipsec_web
		flock -u 37
	} 37>>/var/lock/ipsec_web_lock
else
	echo "command \"$1\" not surported."
	USAGE
fi


