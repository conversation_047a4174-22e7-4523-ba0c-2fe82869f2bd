#!/bin/sh /etc/rc.common

START=99

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1

start() {
	service_start /usr/sbin/sdwan_report &
	/lib/sdwan/sdwan_monitor.sh &
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "health_info_cloud start", "wait_time": 10, "type": "online", "action":"add" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "health_info_cloud stop", "wait_time": 10, "type": "offline", "action":"add" }'	
}

stop() {
	service_stop /usr/sbin/sdwan_report
	PID=`ps -w| grep sdwan_monitor.sh | grep -v grep | awk '{print $1}'`
    kill -9 $PID
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "health_info_cloud start", "wait_time": 10, "type": "online", "action":"delete" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "health_info_cloud stop", "wait_time": 10, "type": "offline", "action":"delete" }'	
}

restart()
{
	stop
	start
}

reload()
{
	restart
}