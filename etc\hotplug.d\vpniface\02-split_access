#!/bin/sh
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     
# brief    
# author   <PERSON>
# version  1.0.0
# date     23Dec15
# histry   arg 1.0.0, 23Dec15, <PERSON>, Create the file.

[ "$DEVICE" == "lo" ] && exit 0
[ "$INTERFACE" == "loopback" ] && exit 0

. /lib/functions.sh
. /lib/zone/zone_api.sh
local route_table_id_min=41
local route_table_id_max=310
local route_table_id_220=220
local route_priority_base=20000

ROUTE_TABLE_ID="/tmp/route.id"
SPLIT_ACCESS_INIT_FILE="/tmp/split_access.init"
	
get_effect_devices(){
    local interface="$1"
	local count=0
	
	local device=`zone_get_effect_devices "$interface"`
	while [ -z "$device" -a "$count" -lt 20 ] ;do
		sleep 1
		device=`zone_get_effect_devices "$interface"`
		[ -n "$device" ] && break
		let count++

		if [ "$count" -ge 20 ];then
			break
		fi		
	done
	
	echo "$device"
}	

policy_table_alloc(){
    local iface=$2
	local cur_id
	
	[ ! -f $ROUTE_TABLE_ID ] && {
	    touch $ROUTE_TABLE_ID
		echo $route_table_id_min > $ROUTE_TABLE_ID
	}
	
	echo "iface=$iface" >> /tmp/split_access.log
	local cnt=`cat /etc/iproute2/rt_tables |grep -w $iface`
	#local tmp="awk '/\s$iface$/{print}' /etc/iproute2/rt_tables"
	#local cnt=`eval $tmp`
	echo "cnt=$cnt" >> /tmp/split_access.log
	if [ -z $cnt ]; then 
	    cur_id=`cat $ROUTE_TABLE_ID`
	    ori_id=$cur_id
		flag="success"
		cur_id=$((cur_id+1)) 
		if [ $cur_id -gt $route_table_id_max ]; then
			       cur_id=$route_table_id_min
	    fi
		echo "cur_id=$cur_id"
		while [ 1 -eq 1 ]
		do
			# 轮询了一圈，没有空闲的id可供分配，分配失败
			if [ $cur_id -eq $ori_id ]; then
				flag="fail"
				break;
			fi
			if [ $cur_id -eq $route_table_id_220 ]; then
				cur_id=$((cur_id+1))
			fi
		    #cur_id=$((cur_id+1)) 
			local id=`cat /etc/iproute2/rt_tables |grep -w $cur_id`
			if [ -z $id  ]; then
			   break;
			else 
			   cur_id=$((cur_id+1))
			   if [ $cur_id -gt $route_table_id_max ]; then
			       cur_id=$route_table_id_min
			   fi
			fi
		done
		if [ $flag == "success" ]; then
			echo $cur_id > /tmp/route.id
		
			echo $cur_id $iface >> /etc/iproute2/rt_tables
			export "$1=${cur_id}"
		else
			cur_id="full"
			export "$1=${cur_id}"
		fi
	else
	    #cur_id=$(echo $cnt | cut -d' ' -f1)
		cur_id="duplication"
		export "$1=${cur_id}"
	fi
}

policy_table_free(){
    local iface=$1

    #sed -i "/\s${iface}$/d" /etc/iproute2/rt_tables
	 sed -i "/\<${iface}\>/d" /etc/iproute2/rt_tables
	#ip route flush table $iface &> /dev/null #need?
}

init_network_env() {
    #uci_revert_state splitaccess
	uci_set_state splitaccess core "" splitaccess_state
	uci_set_state splitaccess env "" splitaccess_env_state
}
	
generage_ifaces_env() {
	local if_list=""
    useriflist=$(zone_get_useriflist)
    for userif in ${useriflist};do
		[ "$(echo $userif | grep "sdwan_")" != "" ] && continue
        ifaces=$(zone_userif_to_effective_iface "${userif}")
        for iface in ${ifaces};do
            [ -n "$iface" -a "$iface" != "loopback" ] && {
                if_list="$if_list $iface"
            }
        done
    done

	uci_toggle_state splitaccess env if_list "$if_list"
}

add_network_env() {
    local if=$1
    [ -n "$if" -a "$if" != "loopback" ] && {
		network_get_ipaddr if_ipaddr "$if"
		uci_toggle_state splitaccess env ${if}_ip "$if_ipaddr"
		network_get_subnet if_mask "$if"
		if_mask="${if_mask#*/}"
		uci_toggle_state splitaccess env ${if}_mask "$if_mask" 
		network_get_gateway if_gateway "$if"
		uci_toggle_state splitaccess env ${if}_gateway "$if_gateway" 
	        [ -n "$if_ipaddr" ] && {
			network_get_protocol if_proto "$if"
			uci_toggle_state splitaccess env ${if}_proto "$if_proto" 
		}
	}
}

del_network_env() {
    local if=$1
    [ -n "$if" -a "$if" != "loopback" ] && {
		uci_revert_state splitaccess env ${if}_ip 
		uci_revert_state splitaccess env ${if}_mask 
		uci_revert_state splitaccess env ${if}_gateway
		uci_revert_state splitaccess env ${if}_proto
	}
}

intranet_route_add() {
	local in_if=$1
	
	local if_list=$(uci_get_state splitaccess env if_list)
	local in_if_proto=$(uci_get_state splitaccess env ${in_if}_proto)
	local in_if_gateway=$(uci_get_state splitaccess env ${in_if}_gateway)
	
        [ -z $in_if_proto ] && {                                                          
            echo "intranet_route_add: in_if_proto is null" >> /tmp/split_access.log
            in_if_proto="default"
            
        }   
	
	echo "intranet_route_add: in_if:$in_if, in_if_proto:$in_if_proto,in_if_gateway:$in_if_gateway " >> /tmp/split_access.log
	if [ $in_if_proto != "static" ] || [ $in_if_proto == "static" -a $in_if_gateway != "" ]; then
	    echo "intranet_route_add: test" >> /tmp/split_access.log
		for tmpif in ${if_list}; do
			local if_proto=$(uci_get_state splitaccess env ${tmpif}_proto)
			[ $if_proto == "static" ] && {
				local if_gateway=$(uci_get_state splitaccess env ${tmpif}_gateway)
				[ -z $if_gateway ] && {
					local lan_device=$(get_effect_devices $tmpif)
					local lan_ipaddr=$(uci_get_state splitaccess env ${tmpif}_ip)
					local lan_mask=$(uci_get_state splitaccess env ${tmpif}_mask)
					
					[ -z $lan_ipaddr ] && {
						echo "intranet_route_add: lan_ipaddr is invalid" >> /tmp/split_access.log
						break;
					}
					if [ -z $lan_mask ]; then
						echo "intranet_route_add: lan_mask is invalid" >> /tmp/split_access.log
						break;
					fi
					
					local lan_subnet=$(ipcalc -n ${lan_ipaddr}/${lan_mask})
					lan_subnet=${lan_subnet#*=}
					ip route add ${lan_subnet}/${lan_mask} dev $lan_device src $lan_ipaddr table $in_if
				}
		}
        done
	else
		local lan_device=$(get_effect_devices $in_if)
		local lan_ipaddr=$(uci_get_state splitaccess env ${in_if}_ip)
		local lan_mask=$(uci_get_state splitaccess env ${in_if}_mask)
		
		echo "intranet_route_add iface:$in_if,ipaddr:$lan_ipaddr",mask:$lan_mask >> /tmp/split_access.log
		[ -z $lan_ipaddr ] && {
			echo "intranet_route_add: lan_ipaddr is invalid" >> /tmp/split_access.log
			return;
		}
		if [ -z $lan_mask ]; then
			echo "intranet_route_add: lan_mask is invalid" >> /tmp/split_access.log
			return;
		fi
		
		local lan_subnet=$(ipcalc -n ${lan_ipaddr}/${lan_mask})
		lan_subnet=${lan_subnet#*=}
		echo "iface:$in_if,ipaddr:$lan_ipaddr",mask:$lan_mask >> /tmp/split_access.log
		
		for tmpif in ${if_list};do
			local if_proto=$(uci_get_state splitaccess env ${tmpif}_proto)
			local if_gateway=$(uci_get_state splitaccess env ${tmpif}_gateway)
			if [ $if_proto != "static" ] || [ $if_proto == "static" -a $if_gateway != "" ]; then
				ip route add ${lan_subnet}/${lan_mask} dev $lan_device src $lan_ipaddr table $tmpif
			fi
		done
	fi
}

intranet_route_del() {
	local in_if=$1
	
	local if_list=$(uci_get_state splitaccess env if_list)
	local in_if_proto=$(uci_get_state splitaccess env ${in_if}_proto)
        [ -z $in_if_proto ] && {
            echo "intranet_route_del: in_if_proto is null" >> /tmp/split_access.log
            in_if_proto="default"          
                             
        }
	if [ $in_if_proto == "static" ]; then
		local in_if_gateway=$(uci_get_state splitaccess env ${in_if}_gateway)
		[ -z $in_if_gateway ] && {
			local lan_device=$(get_effect_devices $in_if)
			local lan_ipaddr=$(uci_get_state splitaccess env ${in_if}_ip)
			local lan_mask=$(uci_get_state splitaccess env ${in_if}_mask)
			
			[ -z $lan_ipaddr ] && {
				echo "intranet_route_add: lan_ipaddr is invalid" >> /tmp/split_access.log
				break;
			}
			if [ -z $lan_mask ]; then
				echo "intranet_route_add: lan_mask is invalid" >> /tmp/split_access.log
				break;
			fi
			
			local lan_subnet=$(ipcalc -n ${lan_ipaddr}/${lan_mask})
			lan_subnet=${lan_subnet#*=}
			
			for tmpif in ${if_list};do
				local if_proto=$(uci_get_state splitaccess env ${tmpif}_proto)
				local if_gateway=$(uci_get_state splitaccess env ${tmpif}_gateway)
				if [ $if_proto != "static" ] || [ $if_proto == "static" -a $if_gateway != ""]; then
					ip route del ${lan_subnet}/${lan_mask} dev $lan_device src $lan_ipaddr table $tmpif
				fi
			done
		}
	fi
}

lan_route_add() {
	local lanif="LAN"
	
	local lan_device=$(get_effect_devices $lanif)
	local lan_ipaddr=$(uci_get_state splitaccess env ${lanif}_ip)
	local lan_mask=$(uci_get_state splitaccess env ${lanif}_mask)
	
	[ -z $lan_ipaddr ] && {
	    echo "lan_route_add: lan_ipaddr is invalid" >> /tmp/split_access.log
	    return;
	}
	if [ -z $lan_mask ]; then
	    echo "lan_route_add: lan_mask is invalid" >> /tmp/split_access.log
	    return;
	fi
	
	local lan_subnet=$(ipcalc -n ${lan_ipaddr}/${lan_mask})
	lan_subnet=${lan_subnet#*=}
	
	ip route add ${lan_subnet}/${lan_mask} dev $lan_device src $lan_ipaddr table $1
}

lan_route_del() {
	local lanif="LAN"
	
	local lan_device=$(get_effect_devices $lanif)
	local lan_ipaddr=$(uci_get_state splitaccess env ${lanif}_ip)
	local lan_mask=$(uci_get_state splitaccess env ${lanif}_mask)
	
	[ -z $lan_ipaddr ] && {
	    echo "lan_route_del: lan_ipaddr is invalid" >> /tmp/split_access.log
	    return;
	}
	if [ -z $lan_mask ]; then
	    echo "lan_route_del: lan_mask is invalid" >> /tmp/split_access.log
	    return;
	fi
	
	local lan_subnet=$(ipcalc -n ${lan_ipaddr}/${lan_mask})
	lan_subnet=${lan_subnet#*=}
	
	ip route del ${lan_subnet}/${lan_mask} dev $lan_device src $lan_ipaddr table $1
}

up_event_handle() {
	local interface=$1

	[ ! -f $SPLIT_ACCESS_INIT_FILE ] && {
		touch $SPLIT_ACCESS_INIT_FILE
		init_network_env
	}

	generage_ifaces_env
	add_network_env $interface

	local ipaddr=$(uci_get_state splitaccess env ${interface}_ip)
	[ -z $ipaddr ] && {
		echo "up_event_handle: ipaddr is invalid" >> /tmp/split_access.log
		return
	}
	local mask=$(uci_get_state splitaccess env ${interface}_mask)
	if [ -z $mask ]; then
		echo "up_event_handle: mask is invalid" >> /tmp/split_access.log
		return
	fi

	policy_table_alloc table_id $interface
	echo "table_id=$table_id" >> /tmp/split_access.log
	
	[ $table_id == "duplication" ] && {
		echo "duplication up event!!! Don't in this!!!" >> /tmp/split_access.log
		down_event_handle $interface
		policy_table_alloc table_id $interface
		add_network_env $interface
		echo "table_id2=$table_id" >> /tmp/split_access.log
	}
	
	local gateway=$(uci_get_state splitaccess env ${interface}_gateway)
	local device=`get_effect_devices "$interface"`
	echo "interface=$interface,device=$device,ipaddr=$ipaddr,mask=$mask,gateway=$gateway" >> /tmp/split_access.log
	
	local subnet=$(ipcalc -n ${ipaddr}/${mask})
	subnet=${subnet#*=}
	echo "subnet=$subnet" >> /tmp/split_access.log
	
	ip route add ${subnet}/${mask} dev $device src $ipaddr table $interface
	ip route add ${subnet}/${mask} dev $device  table 20
	[ -n "$gateway" ] && {
		ip route add default via $gateway dev $device table $interface
	}
	
	intranet_route_add $interface
	
	local pri=$(($table_id+$route_priority_base))
	echo "pri=$pri" >> /tmp/split_access.log
	ip rule add pref $pri from $ipaddr table $interface
	ip route flush cache
}

down_event_handle() {
	local interface=$1
	
	
	local ipaddr=$(uci_get_state splitaccess env ${interface}_ip)
	[ -z $ipaddr ] && {
		echo "down_event_handle:ipaddr is invalid" >> /tmp/split_access.log
		policy_table_free $interface
		return
	}

	local mask=$(uci_get_state splitaccess env ${interface}_mask)
	if [ -z $mask ]; then
		echo "down_event_handle:mask is invalid" >> /tmp/split_access.log
		policy_table_free $interface
		return
	fi
	local gateway=$(uci_get_state splitaccess env ${interface}_gateway)
	local device=$(get_effect_devices $interface)
	echo "down_event_handle:device=$device,ipaddr=$ipaddr,mask=$mask,gateway=$gateway" >> /tmp/split_access.log
	
	local subnet=$(ipcalc -n ${ipaddr}/${mask})
	subnet=${subnet#*=}
	echo "subnet=$subnet"
	
	ip route del ${subnet}/${mask} dev $device src $ipaddr table $interface
	ip route del ${subnet}/${mask} dev $device  table 20
	[ -n "$gateway" ] && {
		ip route del default via $gateway dev $device table $interface
	}
	
	intranet_route_del $interface
	
	ip route flush table $interface
	ip rule del from $ipaddr table $interface
	
	policy_table_free $interface
	
	ip route flush cache
	
	generage_ifaces_env
	del_network_env $interface
}

reload_event_handle() {
	 	local if_list=$(uci_get_state splitaccess env if_list)
		
		for tmpif in ${if_list};do
			[ $tmpif != "LAN" ] && {
				lan_route_del $tmpif
			}
        done
		
		generage_ifaces_env
		add_network_env LAN
		for tmpif2 in ${if_list};do
			[ $tmpif2 != "LAN" ] && {
				lan_route_add $tmpif2
			}
        done
}

update_event_handle() {
	down_event_handle $1
	up_event_handle $1
}

{
	flock -x 55
if [ -z $INTERFACE ]; then
    [ -z $DEVICE ] && {
		flock -u 55
		exit 0
	}
	INTERFACE=$(zone_get_iface_bydev $DEVICE)
fi

[ -z $INTERFACE ] && {
	flock -u 55
	exit 0
}

echo "ACTION=$ACTION,INTERFACE=$INTERFACE" >> /tmp/split_access.log

ret=`ip rule list |grep 20:`
if [ -z "$ret" ]; then
	ip rule add pref 20 table 20
fi

#if [ $INTERFACE == "LAN" ]; then
#	if [ $ACTION == "ifup" ]; then
#		reload_event_handle
#	else
#		echo "Skip the event!" >> /tmp/split_access.log
#	fi
#	
#	flock -u 55
#	exit 0
#fi

case "$ACTION" in
	ifup)
		up_event_handle $INTERFACE
	;;
	ifdown)
		down_event_handle $INTERFACE
	;;
	ifupdate)
		#update_event_handle $INTERFACE
		echo "update event,do nothing" >> /tmp/split_access.log
	;;
esac
    flock -u 55
} 55<>/tmp/split_access.lock