stroke {

    # Analyze addresses/hostnames in left|right to detect which side is local
    # and swap configuration options if necessary. If disabled left is always
    # local.
    # allow_swap = yes

    # Treat certificates in ipsec.d/cacerts and ipsec.conf ca sections as CA
    # certificates even if they don't contain a CA basic constraint.
    # ignore_missing_ca_basic_constraint = no

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Maximum number of stroke messages handled concurrently.
    # max_concurrent = 4

    # If enabled log level changes via stroke socket are not allowed.
    # prevent_loglevel_changes = no

    # Location of the ipsec.secrets file
    # secrets_file = ${sysconfdir}/ipsec.secrets

    # Socket provided by the stroke plugin.
    # socket = unix://${piddir}/charon.ctl

    # Timeout in ms for any stroke command. Use 0 to disable the timeout.
    # timeout = 0

}

