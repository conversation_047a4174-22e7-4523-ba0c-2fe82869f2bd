#!/bin/bash

. /lib/sdb/sdb_lock.sh
. /lib/sdb/sdb_config.sh

reload_av()
{
    local dbname="av"
    dbg_info "reload av"
    get_sdb_use_flag $dbname
    if [ "$?" -eq 1 ]; then
        dbg_info "sdb[$dbname] is in use"
        # 确保CSE启动,避免从CSE中读取标志位出错
        waitcse_status 0 6,9 120
        get_sdb_load_flag $dbname
        if [ "$?" -eq 0 ]; then
            dbg_info "sdb[$dbname] unload, load it"
            sec_db load $dbname
        fi
    else
        dbg_info "sdb[$dbname] not in use"
        sec_db unload $dbname
    fi
}

reload_commit()
{
    local SUBSHELL_PID
    SUBSHELL_PID=$(sh -c 'echo $PPID')
    sdb_lock_take reload_commit $SUBSHELL_PID
    csedb_commit
    sdb_lock_give reload_commit $SUBSHELL_PID
}
