#!/bin/sh /etc/rc.common

START=96
CONFIG_NAME="network"
ROUTE_NUM=30

ERROR_RET="RTNETLINK answers: No such process"
EXIST_RET="RTNETLINK answers: File exists"

ROUTE_STATE_FILE="/tmp/.route"
ROUTE_STATE_FILE_IPV6="/tmp/.route6"

handle_ipsec_route()
{
	config_get isIPsec $1 isIPsec
	# 本函数只维护VPN静态路由，用于隧道重建时维护VPN接口的静态路由
	if [ $isIPsec == "false" ];then
		#echo "not vpn route" > /dev/console
		return 0
	fi

	config_get enable $1 enable
	if [ "$enable" == "on" ];then
		config_get key $1 key
		config_get target $1 target
		config_get gateway $1 gateway
		config_get netmask $1 netmask
		config_get interface $1 if
		config_get metric $1 metric

		# Cache results of zone_get_effect_devices
		# to accelerate booting procedure by 70%
		[ -n "$(eval echo \$__cache_effect_dev_$interface)" ] || eval "__cache_effect_dev_$interface=`zone_get_effect_devices $interface`"
		dev=$(eval echo \$__cache_effect_dev_$interface)
		[ -n "$dev" ] || return 0

		netmask=`ipcalc -p 0.0.0.0 $netmask`
		netmask=`echo $netmask |cut -d "=" -f 2`

		if [ "$gateway" == "--" ]; then
			ret=`ip route add $target/$netmask dev $dev metric $metric table $ROUTE_NUM 2>&1`
		else
			ret=`ip route add $target/$netmask via $gateway dev $dev metric $metric table $ROUTE_NUM 2>&1`
		fi
	
		if [ -z "$ret" -o "$ret" == "$EXIST_RET" ];then
			route_set_state $key "on"
			lan_route=1
		else
			route_set_state $key "off"
		fi

		let ipsec_global++
	fi
}

handle_static_route()
{
	config_get isIPsec $1 isIPsec
	# 本函数只维护常规静态路由
	if [ $isIPsec == "true" ];then
		#echo "not static route" > /dev/console
		return 0
	fi

	config_get enable $1 enable
	if [ "$enable" == "on" ];then
		config_get key $1 key
		config_get target $1 target
		config_get gateway $1 gateway
		config_get netmask $1 netmask
		config_get interface $1 if
		config_get metric $1 metric

		# Cache results of zone_get_effect_devices
		# to accelerate booting procedure by 70%
		[ -n "$(eval echo \$__cache_effect_dev_$interface)" ] || eval "__cache_effect_dev_$interface=`zone_get_effect_devices $interface`"
		dev=$(eval echo \$__cache_effect_dev_$interface)
		[ -n "$dev" ] || return 0

		netmask=`ipcalc -p 0.0.0.0 $netmask`
		netmask=`echo $netmask |cut -d "=" -f 2`

		if [ "$gateway" == "--" ]; then
			ret=`ip route add $target/$netmask dev $dev metric $metric table $ROUTE_NUM 2>&1`
		else
			ret=`ip route add $target/$netmask via $gateway dev $dev metric $metric table $ROUTE_NUM 2>&1`
		fi

		if [ -z "$ret" -o "$ret" == "$EXIST_RET" ];then
			route_set_state $key "on"
			lan_route=1
		else
			route_set_state $key "off"
		fi

		let global++
	fi
}

handle_static_route_ipv6()
{
	config_get enable $1 enable

	if [ "$enable" == "on" ];then
		config_get key $1 key
		config_get target $1 target
		config_get gateway $1 gateway
		config_get netmask $1 netmask
		config_get interface $1 if
		config_get metric $1 metric

		# Cache results of zone_get_effect_devices
		# to accelerate booting procedure by 70%
		[ -n "$(eval echo \$__cache_effect_dev_$interface)" ] || eval "__cache_effect_dev_$interface=`zone_get_effect_devices $interface`"
		dev=$(eval echo \$__cache_effect_dev_$interface)
		[ -n "$dev" ] || return 0
		# IPv6 pppoe不复用IPv4链路拨号dev需要加后缀6
		local proto=`uci get network.$interface.proto`
		local share=`uci get network.$interface.pppoe_sharev4`
		if [ "$proto" == "pppoe" ] && [ "$share" == "off" ]; then
			dev="${dev}6"
		fi

		ret=`ip -6 route add $target/$netmask via $gateway dev $dev metric $metric table $ROUTE_NUM 2>&1`

		if [ -z "$ret" -o "$ret" == "$EXIST_RET" ];then
			route_set_state_ipv6 $key "on"
			lan_route_ipv6=1
		else
			route_set_state_ipv6 $key "off"
		fi

		let global_ipv6++
	fi
}

ipsec_route_start()
{
	. /lib/functions.sh
	. /lib/zone/zone_api.sh
	. /lib/route/route_api.sh

	local ipsec_global=0
	config_load $CONFIG_NAME
	config_foreach handle_ipsec_route user_route

	if [ "$ipsec_global" != 0 ]; then
		ret=`ip rule list |grep "^$ROUTE_NUM:"`
		if [ -z "$ret" ]; then
			ip rule add pref $ROUTE_NUM table $ROUTE_NUM
		fi
	fi

	touch /tmp/ipsec_route.ready
}

start()
{
	. /lib/functions.sh
	. /lib/zone/zone_api.sh
	. /lib/route/route_api.sh

	local global=0 lan_route=0
	local global_ipv6=0 lan_route_ipv6=0

	config_load $CONFIG_NAME

	#IPv4 static route
	config_foreach handle_static_route user_route

	if [ "$global" != 0 ]; then
		ret=`ip rule list |grep "^$ROUTE_NUM:"`
		if [ -z "$ret" ]; then
			ip rule add pref $ROUTE_NUM table $ROUTE_NUM

			#add lan interface default route rule
			[ $lan_route -eq 1 ] && add_lan_default_route
		fi
	fi

	#IPv6 static route
	config_foreach handle_static_route_ipv6 user_route_ipv6

	if [ "$global_ipv6" != 0 ]; then
		ret=`ip -6 rule list |grep "^$ROUTE_NUM:"`
		if [ -z "$ret" ]; then
			ip -6 rule add pref $ROUTE_NUM table $ROUTE_NUM

			#add lan interface default route rule
			[ $lan_route_ipv6 -eq 1 ] && add_lan_default_route_ipv6
		fi
	fi

	touch /tmp/static_route.ready

	ipsec_route_start
}


stop()
{
	#delete route table 
	#ip route flush table $IPSEC_ROUTE_NUM  &> /dev/null
	ip route flush table $ROUTE_NUM  &> /dev/null
	ip -6 route flush table $ROUTE_NUM  &> /dev/null

	#del ip rule if not route info
	#ret=`ip route list table $IPSEC_ROUTE_NUM`
	#if [ -z "$ret" ]; then
	#	ip rule del pref $IPSEC_ROUTE_NUM table $IPSEC_ROUTE_NUM
	#fi

	#del ip rule if not route info
	ret=`ip rule list |grep "^$ROUTE_NUM:"`
	if [ -n "$ret" ]; then
		ip rule del pref $ROUTE_NUM table $ROUTE_NUM
	fi

	#del ipv6 rule if not route info
	ret=`ip -6 rule list |grep "^$ROUTE_NUM:"`
	if [ -n "$ret" ]; then
		ip -6 rule del pref $ROUTE_NUM table $ROUTE_NUM
	fi

	#rm $IPSEC_ROUTE_STATE_FILE -f
	rm $ROUTE_STATE_FILE -f
	rm $ROUTE_STATE_FILE_IPV6 -f
}

restart() {
	stop
	start
}
