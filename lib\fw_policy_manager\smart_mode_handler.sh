#! /bin/sh
# smart_mode_handler.sh

. /lib/functions/dbg.sh

smart_mode_switch()
{
    local enable="$1"

    if [ "$enable" = "on" ]; then
        if [ -f /proc/policy/isfullmode ]; then
            echo 0 > /proc/policy/isfullmode;
        elif [ -f /usr/bin/vppctl ]; then
            vppctl set policytarget fullmode off
        fi
    elif [ "$enable" = "off" ]; then
        if [ -f /proc/policy/isfullmode ]; then
            echo 1 > /proc/policy/isfullmode;
        elif [ -f /usr/bin/vppctl ]; then
            vppctl set policytarget fullmode on
        fi
    else
        dbg_error "unknown input for smart_mode_handler"
        return 1
    fi

    . /lib/fw_policy_manager/sfe_handler.sh update

    return 0
}

smart_mode_switch "$1"
