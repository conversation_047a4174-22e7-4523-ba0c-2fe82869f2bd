#!/bin/sh

local dnsserver=""

get_pptp_dns()
{
	. /lib/zone/zone_api.sh
	. /lib/pppox/pppox-default-variables.sh

	/lib/pptp/pptp-get-tuunel-info.sh -p
	local i=0
	while true; do
		local bb=`uci -c $pppox_pptp_path get pptp-tunnel-info.@info["${i}"].ifname`
		if [ $bb != "" ]; then
			if [ $bb == $1 ]; then
				break
			fi
		else
			break
		fi
		let "i++"
	done

	dnsserver=`uci -c $pppox_pptp_path get pptp-tunnel-info.@info["${i}"].dns`
}

get_l2tp_dns()
{
	. /lib/zone/zone_api.sh
	. /lib/pppox/pppox-default-variables.sh

	/lib/l2tp/l2tp-get-tunnel-info.sh -p
	local i=0
	while true; do
		local tmp=`uci -c $pppox_l2tp_main_path get l2tp-tunnel-info.@lacinfo["${i}"].ifname`
		if [ $tmp != "" ]; then
			if [ $tmp == $1 ]; then
					break
			fi
		else
			break
		fi
		let "i++"
	done

	dnsserver=`uci -c $pppox_l2tp_main_path get l2tp-tunnel-info.@lacinfo["${i}"].dns`
}

#$1:iface_name
#$2:dns1
#$3:dns2
update_dns()
{
	[ -n "$1" ] || return

	. /lib/functions.sh
	. /lib/zone/zone_api.sh
	. /lib/functions/network.sh

	for cnt in $(seq 1 300); do
		dev=`zone_get_effect_devices $1`
		if [ -n "$dev" ]; then
			break
		fi	
		sleep 1
	done

	[ -n "$dev" ] || return

	local dns1=$2
	local dns2=$3

	if [ "$dns1" == "0.0.0.0" ] && [ "$dns2" == "0.0.0.0" ]; then		
		local dnsserver	
		
		#make sure pppoe dnsserver is rebuilded
		sleep 10
		
		iface_name=`zone_get_effect_ifaces $1`
		if [ -n "$iface_name" ]; then
			network_get_dnsserver4 dnsserver $iface_name

			dns1=`echo $dnsserver | cut -d ' ' -f 1`
			dns2=`echo $dnsserver | cut -d ' ' -f 2`
		else
			dns1="0.0.0.0"
			dns2="0.0.0.0"
		fi
	fi

	[ -n "$dns1" ] || dns1="0.0.0.0"
	[ -n "$dns2" ] || dns2="0.0.0.0"

	ubus call online_check update_dns "{\"name\":\"$1\", \"dev\":\"$dev\", \"dns1\":\"$dns1\", \"dns2\":\"$dns2\"}"
}

update_info()
{
	[ -n "$1" ] || return

	. /lib/functions.sh
	. /lib/zone/zone_api.sh
	. /lib/functions/network.sh

	for cnt in $(seq 1 300); do
		dev=`zone_get_effect_devices $1`
		if [ -n "$dev" ]; then
			break
		fi	
		sleep 1
	done

	[ -n "$dev" ] || return
	
	local vpn=`echo $dev | cut -d '-' -f 1`

	if [ "$vpn" == "lt" ] || [ "$vpn" == "pt" ]; then		
		get_pptp_dns $dev
		[ "${dnsserver}" == "" ] && get_l2tp_dns $dev
		
		dns1=`echo $dnsserver | cut -d ' ' -f 1`
		dns2=`echo $dnsserver | cut -d ' ' -f 2`

		[ "$dns1" == "" ] && dns1="0.0.0.0"
		[ "$dns2" == "" ] && dns2="0.0.0.0"				
		 
	else
		iface_name=`zone_get_effect_ifaces $1`
		if [ -n "$iface_name" ]; then
			network_get_dnsserver4 dnsserver $iface_name

			dns1=`echo $dnsserver | cut -d ' ' -f 1`
			dns2=`echo $dnsserver | cut -d ' ' -f 2`
		else
			dns1="0.0.0.0"
			dns2="0.0.0.0"
		fi
	fi

	[ -n "$dns1" ] || dns1="0.0.0.0"
	[ -n "$dns2" ] || dns2="0.0.0.0"
    
	echo "${1} ${dev} ${dns1} ${dns2}" >>/tmp/online/init_info
	#ubus call online_check update_dns "{\"name\":\"$1\", \"dev\":\"$dev\", \"dns1\":\"$dns1\", \"dns2\":\"$dns2\"}"
}

init_update_iface_info()
{
	. /lib/functions.sh
	. /lib/zone/zone_api.sh
	rm /tmp/online/init_info &>/dev/null
	config_load online_check
	config_foreach update_info if
}
