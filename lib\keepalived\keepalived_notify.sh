#!/bin/sh
# make the keepalived hotplug  event
case $1 in
	master)
		echo "$1 $2 $3 $4" > /dev/console
		env -i ACTION=$1 INTERFACE=$2 VIP=$3 INSTANCE=$4 /sbin/hotplug-call keepalived
	;;
	backup)
		echo "$1 $2 $3 $4" > /dev/console
		env -i ACTION=$1 INTERFACE=$2 VIP=$3 INSTANCE=$4 /sbin/hotplug-call keepalived
	;;
	stop)
		echo "$1 $2 $3 $4" > /dev/console
		env -i ACTION=$1 INTERFACE=$2 VIP=$3 INSTANCE=$4 /sbin/hotplug-call keepalived
	;;
	fault)
		echo "$1 $2 $3 $4" > /dev/console
		env -i ACTION=$1 INTERFACE=$2 VIP=$3 INSTANCE=$4 /sbin/hotplug-call keepalived
	;;
	*)
		echo "Usage: $1 {master|backup}" > /dev/console
	;;
esac
