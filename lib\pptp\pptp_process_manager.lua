--[[

File:		pptp_process_manager.lua
Details:	check whether the pptp related process need to exist
Author:		Chen <PERSON>
Version:	0.1
Date:		15 May, 2017

]]--

local uci    = require "luci.model.uci"
local dbg    = require "luci.tools.debug"
local sys    = require "luci.sys"
local socket = require "socket"
local util = require "luci.util"
local err = require "luci.torchlight.error"
local uci_r = uci.cursor()

local PROCESS_FILTER = "pptp"
local PROCESS_NAME = "  pptpd"
local PID_NAME = "pptpd"

function check_pptp_enable()

    local pns_list = {}
    local pptp_state = "off"
    ----------------------------get all connection section ----------------------------
    uci_r:foreach("vpn", "pns",
        function(section)
            pns_list[#pns_list + 1] = uci_r:get_all("vpn", section[".name"])        
            if not pns_list[#pns_list] then
                return false,err.EENTRYNOTEXIST
            end        
        end     
    )

    for k, pns in ipairs(pns_list) do   
        if nil ~= pns.enable and "on" == pns.enable then 
            pptp_state = "on"
            --dbg("there is pptp server that enable")
            break
        end
    end

    return pptp_state
end

local function found_pid(my_cmd)

    local ps_info = io.popen("ps | grep %q" % my_cmd)
    local pid = false
    local tbl

    if ps_info then
        while true do
            local line = ps_info:read("*l")
            if not line then 
                break 
            end
            line = util.trim(line)
            tbl  = util.split(line, " +", 99, true)
            local cmd = tbl[5]
            --dbg(cmd)
            if cmd and cmd:match("^" .. my_cmd .. "$") then
                pid = tbl[1]
                --dbg(pid)
                break
            end
        end
        ps_info:close()
    end
    return pid
end

local function check_process()

    local ps_info = io.popen("ps | grep %q" % PROCESS_FILTER)
    local process_exist = false

    if ps_info then
        while true do
            local line = ps_info:read("*l")
            if not line then 
                break 
            end
            --dbg(line)
            local i,j = string.find(tostring(line),PROCESS_NAME)
    		if i and j then
    			process_exist = true
    			--dbg("find process exist")
    			break
    		end  	
        end
        ps_info:close()
    end
    return process_exist
end


local function manage_process(pptp_state,process_state)
	if nil == pptp_state or nil == process_state then
		return false
	end

	if "on" == pptp_state and false == process_state then
        dbg("PPTP_PROCESS_MANAGER: the pptp is not enable but the process is not on, so start pptpd")
        cmd=string.format("pptpd")              
        sys.fork_call(cmd)

    --[[elseif "off" == pptp_state and true == process_state then
        dbg("PPTP_PROCESS_MANAGER: the pptp is not enable but the process is not on, so kill pptpd")
        cmd=string.format("/etc/init.d/pptpd stop")              
        sys.fork_call(cmd)
    ]]--
    end

	return true
end

--dbg("pptp process manager begin")
local t1 = socket.gettime()

local pptp_state = check_pptp_enable()
local process_state = check_process()

manage_process(pptp_state,process_state)

local t2 = socket.gettime()

dbg("  pptp_check_process.lua time=[", t2-t1, "]")






