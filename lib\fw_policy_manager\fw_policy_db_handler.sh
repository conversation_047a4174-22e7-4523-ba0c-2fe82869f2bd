#!/bin/sh

. /lib/sdb/sdb_config.sh

set_policy_mode() {
	smart_mode="$(uci -q get app_library.global.smart_mode_enable)"
	current_is_fullmode="$(cat /proc/policy/isfullmode)"
	if [ "$smart_mode" == "on" ]; then
		if [ "$current_is_fullmode" == "1" ]; then
			echo 0 > /proc/policy/isfullmode
		fi
	else
		if [ "$current_is_fullmode" == "0" ]; then
			echo 1 > /proc/policy/isfullmode
		fi
	fi
}

set_policy_stat_enable() {
	stat_enable="$(uci -q get security_policy.global.stat_enable)"
	if [ "$stat_enable" == "on" ]; then
		echo 1 > /proc/security_policy/tx_rx_stat
	fi
}

modify_lock() {
	custom_lock -l "security" -f "db_modified_lock" -p "$$" -t "$$" -T "1800" -w "1" -F "1"
}

modify_unlock() {
	custom_lock -n "security" -f "db_modified_lock" -p "$$" -t "$$" -T "1800" -w "1" -F "1"
}

submit_lock() {
	if [ "$CSEDB_LOCKED" != "1" ]; then
		custom_lock -l "security" -f "fw_policy_cse_db_lock" -p "$$" -t "$$" -T "1800" -w "1" -F "1"
		export CSEDB_LOCKED=1
		export CSEDB_LOCK_OWNER=fw_policy_$$
	fi
}

submit_unlock_and_exit() {
	if [ "$CSEDB_LOCKED" = "1" -a "$CSEDB_LOCK_OWNER" = "fw_policy_$$" ]; then
		custom_lock -n "security" -f "fw_policy_cse_db_lock" -p "$$" -t "$$" -T "1800" -w "1" -F "1"
		export CSEDB_LOCKED=0
		export CSEDB_LOCK_OWNER=""
	fi

	exit
}

{
	if [ -z "$1" ]; then
		echo "$0: too few args" > /dev/console
		exit
	fi
	# $1==>action
	action_type=$1
	case $action_type in
		submit)
		submit_lock
		# read policy submit counter from /proc/policy/stat
		submit_cnt=$(cat /proc/policy/stat | grep submit_cnt | sed 's/^submit_cnt:[ ]*//g')
		# if it is first time to submit, create all db
		if [ -z "$submit_cnt" ] || [ "$submit_cnt" = "0" ]; then
			mkdir -p /tmp/policy_db
			touch /tmp/policy_db/db_submitting
			# security_policy
			# 获取设备支持哪些sdb，只对支持的才执行db_creator
			for dbname in $(get_sdb_array);
			do
				case $dbname in
					app)
					lua /lib/security_policy/app_db_creator.lua create
					lua /lib/security_policy/app_id_creator.lua create
					;;
					extapp)
					# already done in app
					;;
					av)
					lua /lib/security_policy/av_creator.lua create
					;;
					ips)
					lua /lib/security_policy/ips_db_creator.lua create
					lua /lib/security_policy/ips_ip_credit_handler.lua init
					;;
					url)
					lua /lib/security_policy/urlfilter_db_creator.lua create
					;;
					asset | hotsite)
					# do nothing
					;;
					fpiapp)
					# do nothing
					;;
					*)
					echo "Unknown sdb: $dbname" > /dev/console
					;;
				esac
			done
			lua /lib/security_policy/time_creator.lua create
			lua /lib/security_policy/global_conf_creator.lua create
			lua /lib/security_policy/industrial_control_global_conf_creator.lua

			# audit_policy
			lua /usr/lib/lua/audit_policy/audit_creator.lua create

			#enc_detect_policy
			lua /lib/enc_detect_policy/enc_detect_policy_db_creator.lua

			#industrial_control
			lua /lib/security_policy/industrial_control_db_creator.lua create
		else
			[ -e /tmp/policy_db/db_modified ] || submit_unlock_and_exit
			touch /tmp/policy_db/db_submitting
			modify_lock
			while read mod
			do
				case $mod in
					timeobj)
					lua /lib/security_policy/time_creator.lua destroy
					lua /lib/security_policy/time_creator.lua create
					;;
					app_profile)
					lua /lib/security_policy/app_db_creator.lua destroy
					lua /lib/security_policy/app_db_creator.lua create
					;;
					av_profile)
					lua /lib/security_policy/av_creator.lua destroy
					lua /lib/security_policy/av_creator.lua create
					;;
					industrial_control_profile)
					lua /lib/security_policy/industrial_control_db_creator.lua destroy
					lua /lib/security_policy/industrial_control_db_creator.lua create
					;;
					industrial_control_global_config)
					lua /lib/security_policy/industrial_control_global_conf_creator.lua
					;;
					ips_profile)
					lua /lib/security_policy/ips_db_creator.lua destroy
					lua /lib/security_policy/ips_db_creator.lua create
					lua /lib/security_policy/ips_ip_credit_handler.lua init
					;;
					audit_profile)
					lua /usr/lib/lua/audit_policy/audit_creator.lua destroy
					lua /usr/lib/lua/audit_policy/audit_creator.lua create
					;;
					urlfilter_profile)
					lua /lib/security_policy/urlfilter_db_creator.lua destroy
					lua /lib/security_policy/urlfilter_db_creator.lua create
					;;
					websort_list)
					lua /lib/security_policy/urlfilter_db_creator.lua destroy
					lua /lib/security_policy/urlfilter_db_creator.lua create
					lua /usr/lib/lua/audit_policy/audit_creator.lua destroy
					lua /usr/lib/lua/audit_policy/audit_creator.lua create
					;;
					security_global_config)
					lua /lib/security_policy/global_conf_creator.lua destroy
					lua /lib/security_policy/global_conf_creator.lua create
					;;
					sec_app_ids)
					lua /lib/security_policy/app_id_creator.lua destroy
					lua /lib/security_policy/app_id_creator.lua create
					;;
					enc_detect_policy)
					# update the sslsplit.conf
					lua /lib/enc_detect_policy/create_sslsplit_conf.lua
					/etc/init.d/sslsplit reload
					lua /lib/enc_detect_policy/enc_detect_policy_db_creator.lua
					;;
					appid_escape_limit)
					echo "modify appid_escape_limit config" > /dev/console
				esac
			done < /tmp/policy_db/db_modified
			rm -f /tmp/policy_db/db_modified
			modify_unlock
		fi

		# appid_escape_limit
		lua /usr/lib/lua/fw_policy_manager/appid_escape_limit_api.lua init
		lua /usr/lib/lua/fw_policy_manager/appid_escape_limit_api.lua create

		lua /usr/lib/lua/fw_policy_manager/submit_api.lua init
		# security_policy
		lua /lib/security_policy/policy_db_creator.lua
		# audit_policy
		lua /usr/lib/lua/audit_policy/audit_policy_db_creator.lua

		lua /usr/lib/lua/fw_policy_manager/submit_api.lua create
		policy_tree
		# set the full mode&smart mode
		set_policy_mode
		# set the security policy stat enable
		set_policy_stat_enable
		# check if the tuple is not use
		. /lib/security_policy/check_tuple_in_use.sh
		# update SFE accorddingly
		. /lib/fw_policy_manager/sfe_handler.sh update
		# commit the csedb
		csedb_commit
		ret=$?

		rm -f /tmp/policy_db/db_submitting
		if [ "$ret" != "0" ]; then
			echo "csedb_commit failed!!!" > /dev/console
			modify_lock
			if [ ! -e /tmp/policy_db/db_modified ]; then
				touch /tmp/policy_db/db_modified
			fi
			modify_unlock
		fi
		# restart fpi after policy commit
		# in app_only mode or enabled policy_route that does specify a particular application
		# fpi will replace cse dpi for application recognition
		# other dpi detections will not take effect (the configuration does not configure other detections)
		# /proc/policy/cse_disable -> 1 and insmod k_fpi
		/etc/init.d/fpi restart
		submit_unlock_and_exit
		;;
		modified)
		if [ -z "$2" ]; then
			echo "$0 $1: too few args" > /dev/console
			exit
		fi
		modify_lock
		modified_mod=$2
		if [ -e /tmp/policy_db/db_modified ]; then
			while read mod
			do
				[ $mod == $modified_mod ] && modify_unlock && exit
			done < /tmp/policy_db/db_modified
		fi
		echo $modified_mod >> /tmp/policy_db/db_modified
		modify_unlock
		;;
		*)
		;;
	esac
}
