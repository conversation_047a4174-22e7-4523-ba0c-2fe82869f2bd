#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat_napt.sh
# brief
# author   <PERSON><PERSON>
# version  1.0.0
# date     23Apr15
# histry   arg 1.0.0, 23Apr15, <PERSON><PERSON>, Create the file.

. /lib/zone/zone_api.sh
. /lib/firewall/fw.sh

napt_postrouting_chain="postrouting_rule_multi_nat"
nat_napt_log="/tmp/.nat/nat_napt.log"
NAT_ZONE_LAN="LAN"
IPT="iptables -w -t nat "

get_napt_rule_dev_by_name()
{
	local rule_napt_sym=$1
	local napt_rule_dev=`$IPT -nvL ${napt_postrouting_chain} | grep -w "\/\* ${rule_napt_sym} \*\/" | awk '{print $7}'`

	#echo "rule_napt_sym is $rule_napt_sym,napt_rule_dev is $napt_rule_dev " > /dev/console
	echo $napt_rule_dev
}

del_napt_rule()
{
	local rule_napt_sym=$1
	local napt_rule_num=`$IPT -nv --line-number -L ${napt_postrouting_chain} | grep -w "\/\* ${rule_napt_sym} \*\/" | cut -d " " -f 1`
	#echo "rule_napt_sym is $rule_napt_sym,napt_rule_num is $napt_rule_num " > /dev/console
	if [ -n "$napt_rule_num" ];then
		$IPT -D ${napt_postrouting_chain} $napt_rule_num
	fi
}

add_napt_rule()
{
	local op=$1
	local rule_napt_name=$2
	local rule_napt_interface=$3
	local rule_napt_ipaddr=$4
	local rule_napt_mask=$5
	local rule_napt_enable=$6

	#echo " op=$1 rule_napt_name=$2 rule_napt_interface=$3 rule_napt_ipaddr=$4 rule_napt_mask=$5 rule_napt_enable=$6" > /dev/console

	[ -z "$rule_napt_interface" ] && {
		echo "interface is not set " >> /dev/console
		return
	}

	#[ -z "$rule_napt_ipaddr" -o "$rule_napt_ipaddr" == "0.0.0.0" ] && {
		#echo "Host ipaddr is not set" >> /dev/console
		#return
	#}

	if [ "$rule_napt_enable" == "on" ]; then
		ifaces=$(zone_get_effect_ifaces "${rule_napt_interface}")
		for iface in $ifaces;do
			if [ "$iface" = "lan" ]; then
				# use -m iface_group to match LAN_IFACES including br-lan and other pppx dev
				local device="iface_group --dev_set LAN_IFACES"
				[ -n "$iface" -a -n "$device" ] && {
					echo "$IPT -A ${napt_postrouting_chain} -m $device -s $rule_napt_ipaddr/$rule_napt_mask -j MASQUERADE -m comment --comment ${rule_napt_name}">>   /dev/console
					$IPT -A ${napt_postrouting_chain} -m $device -s $rule_napt_ipaddr/$rule_napt_mask -j MASQUERADE -m comment --comment ${rule_napt_name}
				}
			else
				#network_get_device device "$iface"
				local device=$(zone_get_effect_devices "${iface}")

				[ -n "$iface" -a -n "$device" ] && {
					$IPT -A ${napt_postrouting_chain} -o $device -s $rule_napt_ipaddr/$rule_napt_mask -j MASQUERADE -m comment --comment ${rule_napt_name}
					 echo "$IPT -A ${napt_postrouting_chain} -o $device -s $rule_napt_ipaddr/$rule_napt_mask -j MASQUERADE -m comment --comment ${rule_napt_name}">>   /dev/console
				}
			fi
		done
	fi
}

napt_rule_operation()
{
	local op=$1
	local rule_napt_name=$2
	local rule_napt_interface=$3

	flock -x 90

	case $op in
	add)
		add_napt_rule $@
	;;
	del)
		del_napt_rule ${rule_napt_name}
	;;
	update)
		del_napt_rule ${rule_napt_name}

		add_napt_rule $@
	;;
	esac

	flock -u 90
} 90>/tmp/nat_napt.lock

nat_load_rule_napt()
{
	config_get name $1 name
	config_get interface $1 if
	config_get ip $1 ip
	config_get mask $1 mask
	config_get enable $1 enable
	local ipaddr=${ip%/*}
	local mask=${ip#*/}

	local ops=$2

	#echo "$ops nat_load_rule_napt name=$name interface=$interface ipaddr=$ipaddr mask=$mask enable=$enable" > /dev/console
	napt_rule_operation ${ops} ${name} ${interface} ${ipaddr} ${mask} ${enable}
}

nat_rule_napt_operation()
{

	fw flush 4 n $napt_postrouting_chain

	config_load nat
	config_foreach nat_load_rule_napt rule_napt add

	#conntrack -F >/dev/null 2>&1

	env -i CLEAN=on MODULE=NAPT /sbin/hotplug-call nat
}

update_napt_rule()
{
	config_get interface $1 if
	config_get enable $1 enable
	config_get name $1 name
	INTERFACE=$2
	if [ -n "${interface}" -a "${enable}" == "on" -a "${interface}" == "${INTERFACE}" ];then
		local rule_napt_dev=$(get_napt_rule_dev_by_name "${name}")
		local ifaces=$(zone_get_effect_ifaces "${interface}")
		for iface in $ifaces;do
			#network_get_device device "$iface"
			local device=$(zone_get_effect_devices "${iface}")
			[ -n "$iface" -a "$device" != "$rule_napt_dev" ] && {
				echo "rule_napt_dev is ${rule_napt_dev},device is ${device}, need to set iptables rules " > /dev/console
				nat_load_rule_napt $1 update
			}
		done
	fi
}

nat_rule_napt_hotplug()
{
	config_load nat
	config_foreach update_napt_rule rule_napt $1
}


