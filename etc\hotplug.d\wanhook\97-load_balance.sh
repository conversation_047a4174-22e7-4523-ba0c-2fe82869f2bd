#!/bin/sh

. /lib/functions.sh

_delete_rule()
{
	infaces=`uci get load_balance.basic.use_if`
	for iface in $infaces
	do
		if [ $iface == "$1" ];then
			uci del_list load_balance.basic.use_if=$iface
			uci_commit load_balance
			break
		fi
	done
}

_add_rule()
{
	func_en=`uci get load_balance.basic.balance_state`
	if [ "$func_en" != "on" ]; then
		return
	fi

	balance_ifs=`uci get load_balance.basic.use_if`
	if ! list_contains balance_ifs "$1"; then
		uci add_list load_balance.basic.use_if=$1
		uci_commit load_balance
	fi
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _delete_rule $element
			done
		}
	;;
	ADD)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _add_rule $element
			done
		}
	;;
	*)
	;;
esac