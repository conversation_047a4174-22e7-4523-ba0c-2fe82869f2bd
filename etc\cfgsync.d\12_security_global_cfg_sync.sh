#!/usr/bin/lua

local dbg     = require "luci.torchlight.debug"
local uci     = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"

local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()

local function security_global_sync()
    uci_r:load(conf_dir)
    local is_changed = false

    local global_config = uci_r:get_all("security_global_config", "global_config")
    -- http_resume替换成http_resume_enable
    if nil ~= global_config["http_resume"] then
        uci_r:set("security_global_config", "global_config", "http_resume_enable", global_config["http_resume"])
        uci_r:delete("security_global_config", "global_config", "http_resume")
        is_changed = true
    end
    -- ftp_resume替换成ftp_resume_enable
    if nil ~= global_config["ftp_resume"] then
        uci_r:set("security_global_config", "global_config", "ftp_resume_enable", global_config["ftp_resume"])
        uci_r:delete("security_global_config", "global_config", "ftp_resume")
        is_changed = true
    end

    if is_changed == true then
        uci_r:commit("security_global_config")
        cfgsync.set_config_changed()
    end
end

security_global_sync()
