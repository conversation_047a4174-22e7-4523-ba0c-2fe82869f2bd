#!/bin/sh /etc/rc.common
# Copyright (C) 2006-2011 OpenWrt.org

START=50

has_root_pwd() {
	local pwd=$([ -f "$1" ] && cat "$1")
	      pwd="${pwd#*root:}"
	      pwd="${pwd%%:*}"

	test -n "${pwd#[\!x]}"
}

get_root_home() {
	local homedir=$([ -f "$1" ] && cat "$1")
	homedir="${homedir#*:*:0:0:*:}"

	echo "${homedir%%:*}"
}

has_ssh_pubkey() {
	( /etc/init.d/dropbear enabled 2> /dev/null && grep -qs "^ssh-" /etc/dropbear/authorized_keys ) || \
	( /etc/init.d/sshd enabled 2> /dev/null && grep -qs "^ssh-" "$(get_root_home /etc/passwd)"/.ssh/authorized_keys )
}

start() {
	if ( ! has_ssh_pubkey && \
	     ! has_root_pwd /etc/passwd && ! has_root_pwd /etc/shadow ) || \
	   ( ! /etc/init.d/dropbear enabled 2> /dev/null && ! /etc/init.d/sshd enabled 2> /dev/null );
	then
		#service_start /usr/sbin/telnetd -l /bin/login.sh
		service_start /usr/sbin/telnetd
	fi
}

stop() {
	service_stop /usr/sbin/telnetd
}
