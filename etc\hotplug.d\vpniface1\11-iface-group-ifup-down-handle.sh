#!/bin/sh
# handle the user zone(sec_zone) affairs for vpn device ifup/ifdown
#
#${ACTION} ifup or ifdown
#${IFNAME} device of vpn
#${PPPD_USERNAME}
#${PPPD_PID}
#${PPPD_TYPE} server or client
#${PPPD_CONFIGPATH}
#${IPREMOTE}
#${IPLOCAL}
#

. /lib/iface_group/iface-group-ifup-down-function.sh
if [ ! -d ${UCI_VPN_IFACE_DIR} ];then
	mkdir ${UCI_VPN_IFACE_DIR}
fi
if [ ! -e ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE} ];then
	touch ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE}
fi

if [ "${PPPD_TYPE}" = "server" -o "${PPPD_TYPE}" = "client" ]; then
	path=${PPPD_CONFIGPATH}/config
	UCI_CONFIG_DIR=${path}
	#echo "UCI_CONFIG_DIR is ${path}" > /dev/console
	#echo "configname ${pppox_configname}" > /dev/console
	configname=${pppox_configname}
	config_load ${configname}
	config_foreach get_param
	config_foreach config_clear
else
	return 1
fi
{
flock -x 310
case "${ACTION}" in
	"ifup")
		do_ifup;;
	"ifdown")
		do_ifdown;;
	*)
		return 1;;
esac
flock -u 310
} 310<>/tmp/vpn_iface_bindif_310.lock

