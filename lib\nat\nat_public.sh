#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat.init
# brief    
# author   <PERSON> chen
# version  1.0.0
# date     20Apr15
# histry   arg 1.0.0, 20Apr15, <PERSON> chen, Create the file. 

[ "$INC_NAT_COM" != "" ] && return
export INC_NAT_COM="INC"

rm -rf /tmp/.nat/nat.log


NAT_LIBDIR=${NAT_LIBDIR:-/lib/nat}

export NAT_TMP_DIR="/tmp/.nat"
export NAT_LOG="$NAT_TMP_DIR/nat.log"


mkdir -p $NAT_TMP_DIR
[ ! -d $NAT_TMP_DIR ] && { mkdir $NAT_TMP_DIR; mkdir -p ${NAT_TMP_DIR}/log; }
[ ! -f $NAT_LOG ] && echo -e "time\t\t\tlog_info" > "$NAT_LOG" && echo -e " create nat.log file"
[ ! -f ${NAT_LIBDIR}/log ] && ln -sf $NAT_LOG ${NAT_LIBDIR}/log


NAT_PRINT()
{
	echo -e "$1"
}

NAT_WRITE_FILE()
{
	echo -e "$1" >> "$2"
}

NAT_WRITE_LOG()
{
	local log="$1"
#return
	local file=$NAT_LOG
	local time=`date +%Y%m%d-%H.%M.%S`
	local item="${time}    ${log}"
	# if log size > 150K, save and recreate
	[ `ls -l "$file" | awk '{print $5}'` -gt 100000 ] && { 
		# keep count of logfile below 10
		[ `ls ${NAT_TMP_DIR}/log/ | wc -l` -ge 10 ] && {
			rm ${NAT_TMP_DIR}/log/`ls ${NAT_TMP_DIR}/log | awk 'NR==1 {print $1}'`
		}
		mv "$file" "${NAT_TMP_DIR}/log/${time}_nat.log"
		NAT_WRITE_FILE "time\t\t\tlog_info" "$file"
	}
	echo -e "$item" >> "$file"
}

NAT_LOG_PRINT()
{
	local item="$1"
#return
	NAT_WRITE_LOG "$item"
	echo -e "$item"
}


export IPT_F_ADD="iptables -t filter -A"
export IPT_N_ADD="iptables -t nat -A"


#unset PRODUCT_WVR450
PRODUCT_ER7520="true"


