#!/bin/sh
TMP_FILE_PATH=/tmp/pushmsg-id
LOCK_PATH=/var/run/inc-pushmsg-id
MSG_SOURCE_DUT="3"
MAX=65536
MIN=1

if [ -e $TMP_FILE_PATH ];then
    lock -w $LOCK_PATH
    num=`cat $TMP_FILE_PATH`
    num=`expr $num + 1`
    [ $num == $MAX ] && num=$MIN
else
    num=$MIN
fi

mac=`echo $(uci get network.lan.macaddr) | awk '{gsub(/:/, ""); print}'`
time=`date +%s`
id=${MSG_SOURCE_DUT}_${time}_${mac}_${num}

echo $id

lock $LOCK_PATH
echo $num > $TMP_FILE_PATH
lock -u $LOCK_PATH
