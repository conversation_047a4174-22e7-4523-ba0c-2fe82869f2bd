#!/bin/sh

. /lib/zone/zone_api.sh

local userif=$(zone_iface_to_userif $INTERFACE)

case ${ACTION} in
    ifup)
        # 像fw5600这种物理接口执行ifdown之后会将接口删除的设备，ifdown后再进行配置会导致配置失败
        # 在ifup处添加对应服务的init，保证接口起来时服务能正常加载
        local ipv6_enable=$(uci get network.bridge_v6.bridge6_enable)
        local ipv6_wan=$(uci get network.bridge_v6.ipv6_WAN)
        local ipv6_lan=$(uci get network.bridge_v6.ipv6_LAN)
        [ "$ipv6_enable" = "on" ] && [ "$userif" = "$ipv6_wan" -o "$userif" = "$ipv6_lan" ] && {
            /etc/init.d/ipv6-pass-through restart
        }
    ;;
esac