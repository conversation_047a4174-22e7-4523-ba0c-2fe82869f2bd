#!/bin/sh /etc/rc.common
# Copyright (c) 2014-2015 TP-LINK Technologies CO.,LTD.
# Copyright (c) 2013 Qualcomm Atheros, Inc.
# Copyright (C) 2006-2011 OpenWrt.org

START=07

system_config() {
	local cfg="$1"

	local hostname conloglevel timezone

	config_get hostname "$cfg" hostname 'SLP'
	echo "$hostname" > /proc/sys/kernel/hostname

	config_get conloglevel "$cfg" conloglevel
	config_get buffersize "$cfg" buffersize
	[ -z "$conloglevel" -a -z "$buffersize" ] || dmesg ${conloglevel:+-n $conloglevel} ${buffersize:+-s $buffersize}

	config_get timezone "$cfg" timezone 'UTC'
	echo "$timezone" > /tmp/TZ

	# apply timezone to kernel
	date -k

}

cloud_config() {
	[ -e /tmp/.update_firmware ] && [ -e /etc/config/cloud_config ] && {
		uci set cloud_config.upgrade_info.type=""
		uci set cloud_config.upgrade_info.version=""
		uci set cloud_config.upgrade_info.release_date=""
		uci set cloud_config.upgrade_info.download_url=""
		uci set cloud_config.upgrade_info.location=""
		uci set cloud_config.upgrade_info.release_log_url=""
		uci set cloud_config.upgrade_info.release_log=""

		uci set cloud_config.new_firmware.fw_update_type="1"
		uci set cloud_config.new_firmware.not_show="0"
		uci set cloud_config.new_firmware.fw_new_notify="0"
		uci commit cloud_config
		cfgSave -s
	}
}

start() {
	[ -f /proc/mounts ] || /sbin/mount_root
	[ -f /proc/jffs2_bbc ] && echo "S" > /proc/jffs2_bbc
	[ -f /proc/net/vlan/config ] && vconfig set_name_type DEV_PLUS_VID_NO_PAD

	mkdir -p /var/run
	mkdir -p /var/log
	mkdir -p /var/lock
	mkdir -p /tmp/usbdisk
	touch /var/log/wtmp
	touch /var/log/lastlog
	touch /tmp/resolv.conf.auto
	ln -sf /tmp/resolv.conf.auto /tmp/resolv.conf
	grep -q debugfs /proc/filesystems && mount -t debugfs debugfs /sys/kernel/debug
	[ "$FAILSAFE" = "true" ] && touch /tmp/.failsafe

	load_modules /etc/modules.d/*

	# init firmware notify infomation
	cloud_config

	# config dn_login add by liyi
	config_dn_login
	
	config_load system
	config_foreach system_config system

	killall -q hotplug2
	[ -x /sbin/hotplug2 ] && /sbin/hotplug2 --override --persistent \
			--set-rules-file /etc/hotplug2.rules \
			--set-coldplug-cmd /sbin/udevtrigger \
			--max-children 1 >/dev/null 2>&1 &

	# create /dev/root if it doesn't exist
	[ -e /dev/root -o -h /dev/root ] || {
		rootdev=$(awk 'BEGIN { RS=" "; FS="="; } $1 == "root" { print $2 }' < /proc/cmdline)
		[ -n "$rootdev" ] && ln -s "$rootdev" /dev/root
	}
        
}

# config dn_login add by liyi
config_dn_login() {
	local enable=0
	local lan_ip
	local domain_name
	local login_way=1
	local is_factory
	local br_type=0
	
	enable=$(uci_get device_info info enable_dns)
	if [ $enable == 1 ]; then 
		is_factory=$(uci_get system sys is_factory)
		[ "$is_factory" == "1" ] && login_way=0
		domain_name=$(uci_get device_info info domain_name)
		lan_ip=$(uci_get network lan ipaddr)
		insmod dn_login ipv4_addr=$lan_ip domain_name=$domain_name login_way=$login_way init_type=$br_type
	else 
		return 
	fi
}


