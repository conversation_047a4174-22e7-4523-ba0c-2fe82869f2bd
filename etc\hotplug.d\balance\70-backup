#!/bin/sh

[ "$DEVICE" == "loopback" ] && exit 0


get_master_inface()
{
	[ $flag -eq 1 ] && return

	master_iface=`uci get line_backup.$1.master_if 2>/dev/null`
	if [ "$INTERFACE" == "$master_iface" ]; then
		flag=1
	fi
}

check_inface_in_rule()
{
	. /lib/functions.sh

	config_load line_backup
	config_foreach get_master_inface rule
}

. /lib/balance/api.sh

state=`get_balance_global_state`
[ "$state" == "off" ] && return

case "$ACTION" in
		ifup)
			local flag=0

			check_inface_in_rule

			if [ $flag -eq 1 ]; then
				logger -t backup -p notice "The backup fault for interface:[$INTERFACE], ACTION:[on]."
				/usr/sbin/line_backup -t fault -i "$INTERFACE" -o "on"
			fi
		;;
		ifdown)
			local flag=0

			check_inface_in_rule

			if [ $flag -eq 1 ];then
				logger -t backup -p notice "The backup fault for interface:[$INTERFACE], ACTION[off]."
				/usr/sbin/line_backup -t fault -i "$INTERFACE" -o "off"
			fi
		;;
esac



