# Section defining IKE connection configurations.
# connections {

    # Section for an IKE connection named <conn>.
    # <conn> {

        # IKE major version to use for connection.
        # version = 0

        # Local address(es) to use for IKE communication, comma separated.
        # local_addrs = %any

        # Remote address(es) to use for IKE communication, comma separated.
        # remote_addrs = %any

        # Local UDP port for IKE communication.
        # local_port = 500

        # Remote UDP port for IKE communication.
        # remote_port = 500

        # Comma separated proposals to accept for IKE.
        # proposals = default

        # Virtual IPs to request in configuration payload / Mode Config.
        # vips =

        # Use Aggressive Mode in IKEv1.
        # aggressive = no

        # Set the Mode Config mode to use.
        # pull = yes

        # Differentiated Services Field Codepoint to set on outgoing IKE packets
        # (six binary digits).
        # dscp = 000000

        # Enforce UDP encapsulation by faking NAT-D payloads.
        # encap = no

        # Enables MOBIKE on IKEv2 connections.
        # mobike = yes

        # Interval of liveness checks (DPD).
        # dpd_delay = 0s

        # Timeout for DPD checks (IKEV1 only).
        # dpd_timeout = 0s

        # Use IKE UDP datagram fragmentation (yes, accept, no or force).
        # fragmentation = yes

        # Use childless IKE_SA initiation (allow, force or never).
        # childless = allow

        # Send certificate requests payloads (yes or no).
        # send_certreq = yes

        # Send certificate payloads (always, never or ifasked).
        # send_cert = ifasked

        # String identifying the Postquantum Preshared Key (PPK) to be used.
        # ppk_id =

        # Whether a Postquantum Preshared Key (PPK) is required for this
        # connection.
        # ppk_required = no

        # Number of retransmission sequences to perform during initial connect.
        # keyingtries = 1

        # Connection uniqueness policy (never, no, keep or replace).
        # unique = no

        # Time to schedule IKE reauthentication.
        # reauth_time = 0s

        # Time to schedule IKE rekeying.
        # rekey_time = 4h

        # Hard IKE_SA lifetime if rekey/reauth does not complete, as time.
        # over_time = 10% of rekey_time/reauth_time

        # Range of random time to subtract from rekey/reauth times.
        # rand_time = over_time

        # Comma separated list of named IP pools.
        # pools =

        # Default inbound XFRM interface ID for children.
        # if_id_in = 0

        # Default outbound XFRM interface ID for children.
        # if_id_out = 0

        # Whether this connection is a mediation connection.
        # mediation = no

        # The name of the connection to mediate this connection through.
        # mediated_by =

        # Identity under which the peer is registered at the mediation server.
        # mediation_peer =

        # Section for a local authentication round.
        # local<suffix> {

            # Optional numeric identifier by which authentication rounds are
            # sorted.  If not specified rounds are ordered by their position in
            # the config file/VICI message.
            # round = 0

            # Comma separated list of certificate candidates to use for
            # authentication.
            # certs =

            # Section for a certificate candidate to use for authentication.
            # cert<suffix> =

            # Comma separated list of raw public key candidates to use for
            # authentication.
            # pubkeys =

            # Authentication to perform locally (pubkey, psk, xauth[-backend] or
            # eap[-method]).
            # auth = pubkey

            # IKE identity to use for authentication round.
            # id =

            # Client EAP-Identity to use in EAP-Identity exchange and the EAP
            # method.
            # eap_id = id

            # Server side EAP-Identity to expect in the EAP method.
            # aaa_id = remote-id

            # Client XAuth username used in the XAuth exchange.
            # xauth_id = id

            # cert<suffix> {

                # Absolute path to the certificate to load.
                # file =

                # Hex-encoded CKA_ID of the certificate on a token.
                # handle =

                # Optional slot number of the token that stores the certificate.
                # slot =

                # Optional PKCS#11 module name.
                # module =

            # }

        # }

        # Section for a remote authentication round.
        # remote<suffix> {

            # Optional numeric identifier by which authentication rounds are
            # sorted.  If not specified rounds are ordered by their position in
            # the config file/VICI message.
            # round = 0

            # IKE identity to expect for authentication round.
            # id = %any

            # Identity to use as peer identity during EAP authentication.
            # eap_id = id

            # Authorization group memberships to require.
            # groups =

            # Certificate policy OIDs the peer's certificate must have.
            # cert_policy =

            # Comma separated list of certificate to accept for authentication.
            # certs =

            # Section for a certificate to accept for authentication.
            # cert<suffix> =

            # Comma separated list of CA certificates to accept for
            # authentication.
            # cacerts =

            # Section for a CA certificate to accept for authentication.
            # cacert<suffix> =

            # Identity in CA certificate to accept for authentication.
            # ca_id =

            # Comma separated list of raw public keys to accept for
            # authentication.
            # pubkeys =

            # Certificate revocation policy, (strict, ifuri or relaxed).
            # revocation = relaxed

            # Authentication to expect from remote (pubkey, psk, xauth[-backend]
            # or eap[-method]).
            # auth = pubkey

            # cert<suffix> {

                # Absolute path to the certificate to load.
                # file =

                # Hex-encoded CKA_ID of the certificate on a token.
                # handle =

                # Optional slot number of the token that stores the certificate.
                # slot =

                # Optional PKCS#11 module name.
                # module =

            # }

            # cacert<suffix> {

                # Absolute path to the certificate to load.
                # file =

                # Hex-encoded CKA_ID of the CA certificate on a token.
                # handle =

                # Optional slot number of the token that stores the CA
                # certificate.
                # slot =

                # Optional PKCS#11 module name.
                # module =

            # }

        # }

        # children {

            # CHILD_SA configuration sub-section.
            # <child> {

                # AH proposals to offer for the CHILD_SA.
                # ah_proposals =

                # ESP proposals to offer for the CHILD_SA.
                # esp_proposals = default

                # Use incorrect 96-bit truncation for HMAC-SHA-256.
                # sha256_96 = no

                # Local traffic selectors to include in CHILD_SA.
                # local_ts = dynamic

                # Remote selectors to include in CHILD_SA.
                # remote_ts = dynamic

                # Time to schedule CHILD_SA rekeying.
                # rekey_time = 1h

                # Maximum lifetime before CHILD_SA gets closed, as time.
                # life_time = rekey_time + 10%

                # Range of random time to subtract from rekey_time.
                # rand_time = life_time - rekey_time

                # Number of bytes processed before initiating CHILD_SA rekeying.
                # rekey_bytes = 0

                # Maximum bytes processed before CHILD_SA gets closed.
                # life_bytes = rekey_bytes + 10%

                # Range of random bytes to subtract from rekey_bytes.
                # rand_bytes = life_bytes - rekey_bytes

                # Number of packets processed before initiating CHILD_SA
                # rekeying.
                # rekey_packets = 0

                # Maximum number of packets processed before CHILD_SA gets
                # closed.
                # life_packets = rekey_packets + 10%

                # Range of random packets to subtract from packets_bytes.
                # rand_packets = life_packets - rekey_packets

                # Updown script to invoke on CHILD_SA up and down events.
                # updown =

                # Hostaccess variable to pass to updown script.
                # hostaccess = no

                # IPsec Mode to establish (tunnel, transport, transport_proxy,
                # beet, pass or drop).
                # mode = tunnel

                # Whether to install IPsec policies or not.
                # policies = yes

                # Whether to install outbound FWD IPsec policies or not.
                # policies_fwd_out = no

                # Action to perform on DPD timeout (clear, trap or restart).
                # dpd_action = clear

                # Enable IPComp compression before encryption.
                # ipcomp = no

                # Timeout before closing CHILD_SA after inactivity.
                # inactivity = 0s

                # Fixed reqid to use for this CHILD_SA.
                # reqid = 0

                # Optional fixed priority for IPsec policies.
                # priority = 0

                # Optional interface name to restrict IPsec policies.
                # interface =

                # Netfilter mark and mask for input traffic.
                # mark_in = 0/0x00000000

                # Whether to set *mark_in* on the inbound SA.
                # mark_in_sa = no

                # Netfilter mark and mask for output traffic.
                # mark_out = 0/0x00000000

                # Netfilter mark applied to packets after the inbound IPsec SA
                # processed them.
                # set_mark_in = 0/0x00000000

                # Netfilter mark applied to packets after the outbound IPsec SA
                # processed them.
                # set_mark_out = 0/0x00000000

                # Inbound XFRM interface ID.
                # if_id_in = 0

                # Outbound XFRM interface ID.
                # if_id_out = 0

                # Traffic Flow Confidentiality padding.
                # tfc_padding = 0

                # IPsec replay window to configure for this CHILD_SA.
                # replay_window = 32

                # Enable hardware offload for this CHILD_SA, if supported by the
                # IPsec implementation.
                # hw_offload = no

                # Whether to copy the DF bit to the outer IPv4 header in tunnel
                # mode.
                # copy_df = yes

                # Whether to copy the ECN header field to/from the outer IP
                # header in tunnel mode.
                # copy_ecn = yes

                # Whether to copy the DSCP header field to/from the outer IP
                # header in tunnel mode.
                # copy_dscp = out

                # Action to perform after loading the configuration (none, trap,
                # start).
                # start_action = none

                # Action to perform after a CHILD_SA gets closed (none, trap,
                # start).
                # close_action = none

            # }

        # }

    # }

# }

# Section defining secrets for IKE/EAP/XAuth authentication and private key
# decryption.
# secrets {

    # EAP secret section for a specific secret.
    # eap<suffix> {

        # Value of the EAP/XAuth secret.
        # secret =

        # Identity the EAP/XAuth secret belongs to.
        # id<suffix> =

    # }

    # XAuth secret section for a specific secret.
    # xauth<suffix> {

    # }

    # NTLM secret section for a specific secret.
    # ntlm<suffix> {

        # Value of the NTLM secret.
        # secret =

        # Identity the NTLM secret belongs to.
        # id<suffix> =

    # }

    # IKE preshared secret section for a specific secret.
    # ike<suffix> {

        # Value of the IKE preshared secret.
        # secret =

        # IKE identity the IKE preshared secret belongs to.
        # id<suffix> =

    # }

    # Postquantum Preshared Key (PPK) section for a specific secret.
    # ppk<suffix> {

        # Value of the PPK.
        # secret =

        # PPK identity the PPK belongs to.
        # id<suffix> =

    # }

    # Private key decryption passphrase for a key in the private folder.
    # private<suffix> {

        # File name in the private folder for which this passphrase should be
        # used.
        # file =

        # Value of decryption passphrase for private key.
        # secret =

    # }

    # Private key decryption passphrase for a key in the rsa folder.
    # rsa<suffix> {

        # File name in the rsa folder for which this passphrase should be used.
        # file =

        # Value of decryption passphrase for RSA key.
        # secret =

    # }

    # Private key decryption passphrase for a key in the ecdsa folder.
    # ecdsa<suffix> {

        # File name in the ecdsa folder for which this passphrase should be
        # used.
        # file =

        # Value of decryption passphrase for ECDSA key.
        # secret =

    # }

    # Private key decryption passphrase for a key in the pkcs8 folder.
    # pkcs8<suffix> {

        # File name in the pkcs8 folder for which this passphrase should be
        # used.
        # file =

        # Value of decryption passphrase for PKCS#8 key.
        # secret =

    # }

    # PKCS#12 decryption passphrase for a container in the pkcs12 folder.
    # pkcs12<suffix> {

        # File name in the pkcs12 folder for which this passphrase should be
        # used.
        # file =

        # Value of decryption passphrase for PKCS#12 container.
        # secret =

    # }

    # Definition for a private key that's stored on a token/smartcard.
    # token<suffix> {

        # Hex-encoded CKA_ID of the private key on the token.
        # handle =

        # Optional slot number to access the token.
        # slot =

        # Optional PKCS#11 module name to access the token.
        # module =

        # Optional PIN required to access the key on the token. If none is
        # provided the user is prompted during an interactive --load-creds call.
        # pin =

    # }

# }

# Section defining named pools.
# pools {

    # Section defining a single pool with a unique name.
    # <name> {

        # Addresses allocated in pool.
        # addrs =

        # Comma separated list of additional attributes from type <attr>.
        # <attr> =

    # }

# }

# Section defining attributes of certification authorities.
# authorities {

    # Section defining a certification authority with a unique name.
    # <name> {

        # CA certificate belonging to the certification authority.
        # cacert =

        # Absolute path to the certificate to load.
        # file =

        # Hex-encoded CKA_ID of the CA certificate on a token.
        # handle =

        # Optional slot number of the token that stores the CA certificate.
        # slot =

        # Optional PKCS#11 module name.
        # module =

        # Comma-separated list of CRL distribution points.
        # crl_uris =

        # Comma-separated list of OCSP URIs.
        # ocsp_uris =

        # Defines the base URI for the Hash and URL feature supported by IKEv2.
        # cert_uri_base =

    # }

# }

# Include config snippets
include conf.d/*.conf

