#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.
. /lib/zone/zone_api.sh

[ ! -f /tmp/iface_setup.ready ] && {
    exit 0
}

case "$ACTION" in
    ifup)
        lua /usr/lib/lua/ipgroup/sdwan_subnet_init.lua update_local &
    ;;
    ifdown)
        # do nothing
    ;;
    ifupdate)
        # do nothing
    ;;
esac
