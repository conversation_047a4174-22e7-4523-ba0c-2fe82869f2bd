#!/bin/sh

cmd=$1
rule_name="$2"

shift
case $cmd in
    *ACTIVE)  
        . /lib/policy_route/time.sh

        echo "[policy_route] rule $rule_name is active!" > /dev/console
        policy_route_handle_timeobj $rule_name "active"
    ;;
    
    *EXPIRE)        
        . /lib/policy_route/time.sh
    
        echo "[policy_route] rule $rule_name is expire!" > /dev/console
        policy_route_handle_timeobj $rule_name "expire"
    ;;
    
    *RESET)
        echo "reset evnet!"
    ;;   
      
esac


