#!/bin/sh

notify_update_serviceids()
{
	local default_serviceids=`uci get cloud_config.serviceids.default_serviceids`
	local had_not_id="1"
	if [ -n "$default_serviceids" ]&&[ "1" == "$had_not_id" ];then
		for one_id in $default_serviceids
		do
			if [ -n "$one_id" ]&&[ "$one_id" == "$1" ];then
				had_not_id="0"
				break;
			fi
			
		done
	fi
	local serviceids=`uci get cloud_config.serviceids.serviceids`
	if [ -n "$default_serviceids" ]&&[ "1" == "$had_not_id" ];then
		for one_id in $serviceids
		do
			if [ -n "$one_id" ]&&[ "$one_id" == "$1" ];then
				had_not_id="0"
				break;
			fi
			
		done
	fi
	if [ "1" == "$had_not_id" ];then
		uci add_list cloud_config.serviceids.serviceids=$1
		uci commit cloud_config
		cfgSave
	fi
	ubus call cloudclient notify_update_new_token
}

if [ -n "$1" ];then
	notify_update_serviceids $1
fi
