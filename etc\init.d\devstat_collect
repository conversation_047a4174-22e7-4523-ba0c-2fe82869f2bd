#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=60
DEV_COLLECT_PID_FILE=/var/run/collect_devStat.pid

stop()
{
    ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "devstat-cloud start", "wait_time": 10, "type": "online", "action":"delete" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "devstat-cloud stop", "wait_time": 10, "type": "offline", "action":"delete" }'
}

start()
{
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "devstat-cloud start", "wait_time": 10, "type": "online", "action":"add" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "devstat-cloud stop", "wait_time": 10, "type": "offline", "action":"add" }'
	ubus call nms_report_cache statistics_parms_reg '{"type":"devStats", "get_cb":"lua /usr/lib/lua/monitor/report_devStat.lua report", "success_cb":"lua /usr/lib/lua/monitor/report_devStat.lua success_cb"}'
	return 0
}

restart()
{
        stop
		start
}

reload()
{
	restart
}