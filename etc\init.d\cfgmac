#!/bin/sh /etc/rc.common
# Copyright (C) 2014-2014 www.tp-link.com

START=17

FAC_MACADDR=
FAC_MAC_NUM=0
FAC_FOLLOW_MAC_NUM=0

#cfg 1st mac to default lan
cfg_fac_mac0() {
	local t_issys
	local t_useconfig
	local ipaddr
	local t_factory_mac
	local t_mac_follow_if
	config_get t_issys $1 t_issys "unknown"
	config_get t_useconfig $1 t_useconfig "unknown"
	config_get ipaddr $1 ipaddr "unknown"
	config_get t_factory_mac $1 t_factory_mac "unknown"
	config_get t_mac_follow_if $1 t_mac_follow_if "unknown"
	if [ "$t_issys" == "1" -a "$t_useconfig" == "unknown" -a "$t_factory_mac" == "unknown" -a "$ipaddr" != "unknown" -a "$t_mac_follow_if" == "unknown" ]; then
		config set $1 "t_factory_mac" "$FAC_MACADDR"
		uci set network.$1.t_factory_mac="$FAC_MACADDR"
		uci set network.$1.macaddr="$FAC_MACADDR"
		let FAC_MAC_NUM=$FAC_MAC_NUM+1
		echo "[$FAC_MAC_NUM]CFG facmac $FAC_MACADDR to $1" > /dev/console
		acc_mac_addr FAC_MACADDR "$FAC_MACADDR"
	fi
}

#cfg other t_issys port
#ER7/ER8上，用户配的桥的issys也等于，所以增加t_useconfig的判断，用户配置的网桥t_useconfig==1
cfg_fac_mac() {
	local t_issys
	local t_useconfig
	local t_factory_mac
	local t_mac_follow_if
	config_get t_issys $1 t_issys "unknown"
	config_get t_useconfig $1 t_useconfig "unknown"
	config_get t_factory_mac $1 t_factory_mac "unknown"
	config_get t_mac_follow_if $1 t_mac_follow_if "unknown"
	if [ "$t_issys" == "1" -a "$t_useconfig" == "unknown" -a "$t_factory_mac" == "unknown" -a "$t_mac_follow_if" == "unknown" ]; then
		uci set network.$1.t_factory_mac="$FAC_MACADDR"
		uci set network.$1.macaddr="$FAC_MACADDR"
		let FAC_MAC_NUM=$FAC_MAC_NUM+1
		echo "[$FAC_MAC_NUM]CFG facmac $FAC_MACADDR to $1" > /dev/console
		acc_mac_addr FAC_MACADDR "$FAC_MACADDR"
	fi
}
cfg_fac_follow_mac() {
	local t_issys
	local t_useconfig
	local macaddr
	local t_mac_follow_if
	local follow_mac
	config_get t_issys $1 t_issys "unknown"
	config_get t_useconfig $1 t_useconfig "unknown"
	config_get macaddr $1 macaddr "unknown"
	config_get t_mac_follow_if $1 t_mac_follow_if "unknown"
	if [ "$t_issys" == "1" -a "$t_useconfig" == "unknown"  -a "$t_mac_follow_if" != "unknown" ]; then
		config_get follow_mac $t_mac_follow_if macaddr "unknown"
		if [ "$follow_mac" != "unknown" -a "$macaddr" != "$follow_mac" ]; then
			uci set network.$1.t_factory_mac="$follow_mac"
			uci set network.$1.macaddr="$follow_mac"
			let FAC_FOLLOW_MAC_NUM=$FAC_FOLLOW_MAC_NUM+1
			echo "[$FAC_FOLLOW_MAC_NUM]CFG facmac $follow_mac to $1" > /dev/console
		fi
	fi
}
start() {
	read_mac_from_tddp
	if [ -z $FAC_MACADDR ]; then
		echo "================Warning===================="
		echo "==== MAC was not written to flash yet! Use default. ===="
		echo "================Warning===================="
		return 0
	fi

	config_load network
	config_foreach cfg_fac_mac0 interface
	config_foreach cfg_fac_mac interface

	if [ $FAC_MAC_NUM -gt 0 ]; then
		uci commit network
	fi
	
	config_load network
	config_foreach cfg_fac_follow_mac interface
	
	if [ $FAC_FOLLOW_MAC_NUM -gt 0 ]; then
		uci commit network
	fi
	
	if [ $FAC_MAC_NUM -gt 0 -o $FAC_FOLLOW_MAC_NUM -gt 0 ]; then
		cfgSave -s all
	fi
}

read_mac_from_tddp() {
	. /usr/share/libubox/jshn.sh
	local MACADDRSTR=
	local READTIME=0

	MACADDRSTR=$(ubus call tddpServer getInfo '{"infoMask":1, "sep":":"}')
	while [ -z "$MACADDRSTR" -a "$READTIME" -le "50" ]; do
		#sleep 1s
		MACADDRSTR=$(ubus call tddpServer getInfo '{"infoMask":1, "sep":":"}')
		READTIME=$(expr $READTIME + 1)
	done

	json_init
	json_load "$MACADDRSTR"
	json_get_var FAC_MACADDR mac
}

mac_to_num() {
	local mac
	
	mac=0x${1//:/}

	export "$2=$mac"
}

num_to_mac() {
	local mac
	local subNum
	local num

	num="$2"
	local i=0
	while [ $i -lt 6 ];do 
		subNum=${num:0:2}
		num=${num:2}
		mac="$mac:$subNum"
		let "i += 1"
	done

	mac=${mac:1}

	eval "export -- \"$1=$mac\""
}

dec_mac_addr() {
	local __out="$1"
	local __in="$2"
	
	mac_to_num "$__in" macNum
	let "macNum -= 1"
	
	macNum=$(printf %012X $macNum)
	num_to_mac "$__out" "$macNum"
}

acc_mac_addr() {
	local __out="$1"
	local __in="$2"

	mac_to_num "$__in" macNum
	let "macNum += 1"
	
	macNum=$(printf %012X $macNum)
	num_to_mac "$__out" "$macNum"
}

write_mac_to_iface() {
	local ifname=$1
	local cfgstr=
	
	# invalid device, return
	ifconfig "$ifname" > /dev/null 2>&1 || return 1
	
	# if MAC has been configured, return
	cfgstr=$(ifconfig "$ifname" | grep "HWaddr" | grep "$2")
	[ -n "$cfgstr" ] && return

	ifconfig "$ifname" down
	ifconfig "$ifname" hw ether "$2"
	#ifconfig "$ifname" up
	
	return 0
}
