#!/bin/sh
#########################################################
# Copyright (C) 2015 TP-LINK Technologies CO.,LTD.
#
# FILE NAME  :   guest_onload
# VERSION    :   1.0
# DESCRIPTION:   Onload the connection in the guest network.
#				  Use for Shortcut_FE.
# AUTHOR     :   lin<PERSON><PERSON> <<EMAIL>>
#
#########################################################
. /lib/functions/parse_uci_config.sh

GUEST_2G_CONNMARK=0x22222222
GUEST_5G_CONNMARK=0x55555555
SFE_CMD=sfe_onload
SFE_EXIST=/sys/fast_classifier
GUEST_NETWORK_UCI=guest_network

gn_onload() {
	local switch=0

	[ -d "$SFE_EXIST" ] || return 1

	switch=$(cat $SFE_EXIST/sfe_switch)
	[ "$switch" -ne 1 ] && return 1

	load_uci_config || return 1

	local sections=$(get_sections_name $GUEST_NETWORK_UCI)
	for section in $sections
	do
		case $section in
			guest_2g)
				ubus call netlinkd send "{ \"cmd\": \"$SFE_CMD\", \"data\": \"c$GUEST_2G_CONNMARK\" }"
				;;
			guest_5g)
				ubus call netlinkd send "{ \"cmd\": \"$SFE_CMD\", \"data\": \"c$GUEST_5G_CONNMARK\" }"
				;;
			*)
				echo "[ guest_onload ] other wireless feq"
				;;
		esac
	done

	return 0
}

gn_onload
