#!/bin/sh /etc/rc.common

START=96

IPT="ip6tables -t filter -w"
RULE="-m conntrack ! --ctstate RELATED,ESTABLISHED -o"

start()
{
    . /lib/zone/zone_api.sh

    local state=`uci get ipv6_firewall.basic.ipv6_firewall_state 2>/dev/null`
    [ "$state" == "off" ] && return

    local ifaces=`uci get ipv6_firewall.basic.use_if 2>/dev/null`
    [ -n "$ifaces" ] || return

    local chain_exist=`$IPT -nvL | grep -w ipv6_firewall_forward`
    [ -z "$chain_exist" ] && fw add 6 f ipv6_firewall_forward

    local e_iface e_device
    for iface in $ifaces;do
        e_iface=`zone_get_effect_ifaces $iface`
        e_device=`zone_get_effect_devices $e_iface`
        if [ -n "$e_device" ]; then
            fw add 6 f ipv6_firewall_forward DROP { $RULE $e_device }
        fi
    done

    fw add 6 f forwarding_rule ipv6_firewall_forward

}

stop()
{

    #delete ipv6_firewall_forward
    fw del 6 f forwarding_rule ipv6_firewall_forward
    fw flush 6 f ipv6_firewall_forward
    fw del 6 f ipv6_firewall_forward

}

restart() {
    stop
    start
}
