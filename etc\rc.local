# Put your custom commands here that should be executed once
# the system init finished. By default this file does nothing.

#用于octeon机型，绑定网口和串口中断到不同CPU
irq_balance()
{
    local irqnum=`cat /proc/interrupts | awk -F: '/ eth/ {print $1}' | sed 's/[^0-9]//g'`
    local core_num=`cat /proc/cpuinfo | grep -w processor | awk 'END{print NR}'`
    local cnt=0

    if [ -n "$irqnum" ]; then
        for irq in $irqnum; do
            local mask=$((1<<$cnt))
            echo $mask > /proc/irq/${irq}/smp_affinity
            echo "eth:Bind $mask to $irq" > /dev/console

            #翻转
            let cnt=cnt+1
            if [ $cnt -ge $core_num ]; then
                cnt=0
            fi
        done
    fi

    local shift_len=0
    let shift_len=core_num-1
    irqnum=`cat /proc/interrupts | awk -F: '/serial/ {print $1}' | sed 's/[^0-9]//g'`
    local mask=$((1<<$shift_len))
    if [ -n "$irqnum" ]; then
        for irq in $irqnum; do
            echo $mask > /proc/irq/${irq}/smp_affinity
            echo "serial:Bind $mask to $irq" > /dev/console
        done
    fi
}
irq_balance


exit 0
