#!/bin/sh
[ ! -f "/etc/keepalived/keepalived.conf" ] && exit 1

. /lib/functions.sh
. /lib/functions/network.sh


# if  vrrp_interface physical link status down exit 1
get_link_status()
{
    local phy_state=`ubus call network.interface.$1  status| jsonfilter -e "$.up"`
    if [ "$phy_state" == "false" ]; then
        exit 1
    fi
}
# if have one iface up, up_flag =1
get_all_online_status()
{
    local ifaces_state=`ubus call online_check get_iface | grep  -E "balance_state" | tr '"' ' '`
    for  line in ${ifaces_state}
    do
        local tmp_state=${line#*":"}
        if [ "$tmp_state" == "up" ]; then
            exit 0
        fi
    done
    exit 1
}

#get_online_status $1

get_link_status $1
get_all_online_status


