#!/bin/sh
#author: wangdechu
#brief: common functions used for dealing with l2tp server and client

. /lib/functions.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/zone/zone_api.sh
. /lib/pppox/pppox-header.sh

l2tp_global_options="l2tphellointerval lcpechointerval"
l2tp_lns_options="bindif ipsecenc presharekey enable"
l2tp_lac_options="tunnelname username password server_ip outif ipsecenc presharekey workmode remotesubnet enable"

l2tp_server_configname=vpn
l2tp_client_configname=vpn

l2tp_global4server_path=/tmp/l2tp/global4server
l2tp_global4client_path=/tmp/l2tp/global4client

l2tp_ifdevice_info=l2tp-ifdevice-info
l2tp_started=l2tp-started
l2tp_ipsec_prefix=X_l2tp_
l2tp_dnsfile=/tmp/l2tp/client-dns.willdo

LOGGER_MODULE_ID_L2TP=80
LOGGER_INFO_ID_IPSECNOTSUCC=14010
LOGGER_INFO_ID_IPSECSUCC=14011

_load_global()
{
	local section=$1
	local subpath=$2
	local configname=${pppox_configname}
	local configtype=global
	local path

	path=${subpath}/config
	mkdir -p ${path}
	touch ${path}/${configname}
	uci -c ${path} set ${configname}.${section}=${configtype}

	for option in ${l2tp_global_options}; do
		config_get myoptionvalue ${section} ${option}
		[ "${myoptionvalue}" = "" ] && continue
		uci -c ${path} set ${configname}.${section}.${option}=${myoptionvalue}
	done
	uci -c ${path} commit ${configname}
}


_load_one_client_or_server()
{
	local section=$1
	local subpath=$2
	local configname=$3
	local options=$4
	local configtype=$5
	local wishsection=$6
	local path

	[ "${wishsection}" != "${section}" -a "${wishsection}" != "all" ] && return 0

	path=${subpath}/${section}/config
	mkdir -p ${path}
	oldcn=${configname}
	configname=${configname}_${section}
	touch ${path}/${configname}
	uci -c ${path} set ${configname}.${section}=${configtype}

	for option in ${options}; do
		config_get myoptionvalue ${section} ${option}
		[ "${myoptionvalue}" = "" ] && continue
		uci -c ${path} set ${configname}.${section}.${option}=${myoptionvalue}
	done


	uci -c ${path} commit ${configname}
	mv -f ${path}/${configname} ${path}/${oldcn}
	touch ${path}/${pppox_l2tptype}.type #used for user manager checking the type of pppox

}

##
#l2tp_load_client_or_server {lns|lac} {all|global|<section>}
l2tp_load_client_or_server()
{
	local configtype=$1
	local wishsection=$2
	local configname
	local globalpath
	local subpath
	local options
	local globalsection

	[ "${configtype}" != "lac" -a "${configtype}" != "lns" ] && return 0

	[ "${configtype}" = "lns" ] && mkdir -p ${pppox_l2tp_server_path}
	[ "${configtype}" = "lac" ] && mkdir -p ${pppox_l2tp_client_path}

	[ "${configtype}" = "lac" ] && configname=${l2tp_client_configname} && subpath=${pppox_l2tp_client_path} &&
		options=${l2tp_lac_options} && globalpath=${l2tp_global4client_path} && globalsection="l2tp_client"
	[ "${configtype}" = "lns" ] && configname=${l2tp_server_configname} && subpath=${pppox_l2tp_server_path} &&
		options=${l2tp_lns_options} && globalpath=${l2tp_global4server_path} && globalsection="l2tp_server"

	config_load ${configname}
	[ "${wishsection}" = "global" -o "${wishsection}" = "all" ] && config_foreach _load_global ${globalsection} ${globalpath}
	config_foreach _load_one_client_or_server ${configtype} ${subpath} ${pppox_configname} "${options}" ${configtype} ${wishsection}
	#config_foreach config_clear
}


_l2tp_pppoptions_for_server()
{
	echo "auth" >$1
	[ "${2}" != "0" ] && { echo "lcp-echo-interval ${2}" >>$1;
		echo "lcp-echo-failure 5" >>$1; }
	echo "plugin ${pppox_ppp4tplinkso}" >>$1
	echo "path ${3}" >>$1
	echo "plugin ${pppox_ippoolso}" >>$1
	echo "require-mschap-v2" >>$1
	echo "refuse-pap" >>$1
	echo "refuse-chap" >>$1
	echo "refuse-mschap" >>$1
	echo "refuse-eap" >>$1
	echo "noccp" >>$1
	echo "***********:" >>$1
	echo "proxyarp" >>$1
}

_l2tp_pppoptions_for_client()
{
	echo "user '${2}'" >$1
	echo "password '${3}'" >>$1
	[ "${4}" != "0" ] && { echo "lcp-echo-interval ${4}" >>$1;
		echo "lcp-echo-failure 5" >>$1; }
	echo "noipdefault" >>$1
	echo "usepeerdns" >>$1
	echo "noauth" >>$1
	echo "persist" >>$1
	echo "maxfail 5" >>$1
	echo "plugin ${pppox_ppp4tplinkso}" >>$1
	echo "path ${5}" >>$1
	echo "ifname ${l2tp_header}${6}" >>$1 #interface name like "l2tp-<tunnelname>"
	echo "noccp" >>$1
	echo "refuse-eap" >>$1
}

#Easy to get device from interface by get_devname_byif, so we don't cache the devicename
l2tp_save_ifdevice_info()
{
	local mif=$1
	local name=$2
	local option=$3

	[ ! -f ${pppox_l2tp_main_path}/${l2tp_ifdevice_info} ] && touch ${pppox_l2tp_main_path}/${l2tp_ifdevice_info}

	local ifsection
	ifsection=if_${mif}
	ifsection=${ifsection//./_}
	#ifsection=${ifsection//-/_}
	uci -c ${pppox_l2tp_main_path} set ${l2tp_ifdevice_info}.${ifsection}=info
	uci -c ${pppox_l2tp_main_path} add_list ${l2tp_ifdevice_info}.${ifsection}.${option}=${name}
	uci -c ${pppox_l2tp_main_path} commit ${l2tp_ifdevice_info}
}

##
#_l2tp_distribute_server <servername>
_l2tp_distribute_server()
{
	local name=$1
	local path=${pppox_l2tp_server_path}/${name}/config

	local enable=`uci -c ${path} get ${pppox_configname}.${name}.enable 2>/dev/null`
	[ "${enable}" != "on" ] && return 0

	local bindif=`uci -c ${path} get ${pppox_configname}.${name}.bindif 2>/dev/null`
	local ipsecenc=`uci -c ${path} get ${pppox_configname}.${name}.ipsecenc 2>/dev/null`
	local presharekey=`uci -c ${path} get ${pppox_configname}.${name}.presharekey 2>/dev/null`
	local lcpechointerval=`uci -c ${l2tp_global4server_path}/config get ${pppox_configname}.l2tp_server_global.lcpechointerval 2>/dev/null`

	local pppoptfile
	pppoptfile=${pppox_l2tp_server_path}/${name}/config/ppp-options
	_l2tp_pppoptions_for_server "${pppoptfile}" "${lcpechointerval}" "${path%\/config}"

	#interface to device
	local mif=`zone_get_effect_ifaces ${bindif}`
	[ -z "${mif}" ] && { echo ""; return 0; }
	local device=`zone_get_device_byif ${mif}`
	device=${device%% *}
	[ -z "${device}" ] && return 0

	#save interface
	l2tp_save_ifdevice_info "${bindif}" "${name}" "server"

	#distribute l2tp config to xl2tpd process
	xl2tpd-control add-lns ${name} bind if = ${device} pppoptfile = ${pppoptfile} hidden bit = yes length bit = yes assign ip = no
}

##
#_l2tp_distribute_client <clientname>
_l2tp_distribute_client()
{
	local name=$1
	local path=${pppox_l2tp_client_path}/${name}/config
	local username=`uci -c ${path} get ${pppox_configname}.${name}.username 2>/dev/null`
	local password=`uci -c ${path} get ${pppox_configname}.${name}.password 2>/dev/null`
	local lns=`uci -c ${path} get ${pppox_configname}.${name}.server_ip 2>/dev/null`
	local outif=`uci -c ${path} get ${pppox_configname}.${name}.outif 2>/dev/null`
	local ipsecenc=`uci -c ${path} get ${pppox_configname}.${name}.ipsecenc 2>/dev/null`
	local presharekey=`uci -c ${path} get ${pppox_configname}.${name}.presharekey 2>/dev/null`
	local enable=`uci -c ${path} get ${pppox_configname}.${name}.enable 2>/dev/null`
	local tunnelname=`uci -c ${path} get ${pppox_configname}.${name}.tunnelname 2>/dev/null`
	local lcpechointerval=`uci -c ${l2tp_global4client_path}/config get ${pppox_configname}.l2tp_client_global.lcpechointerval 2>/dev/null`

	[ "${enable}" != "on" ] && return 0

	local pppoptfile
	password=${password//\\/\\\\}
	password=${password//\'/\\\'}
	username=${username//\\/\\\\}
	username=${username//\'/\\\'}
	pppoptfile=${pppox_l2tp_client_path}/${name}/config/ppp-options
	_l2tp_pppoptions_for_client "${pppoptfile}" "${username}" "${password}" "${lcpechointerval}" "${path%\/config}" "${tunnelname}"

	echo "${tunnelname}" >${pppox_l2tp_client_path}/${name}/tunnelname

	#interface to device
	local mif=`zone_get_effect_ifaces ${outif}`
	[ -z "${mif}" ] && { echo ""; return 0; }
	local device=`zone_get_device_byif ${mif}`
	device=${device%% *}
	[ -z "${device}" ] && return 0

	#save interface
	l2tp_save_ifdevice_info "${outif}" "${name}" "client"

	#distribute l2tp config to xl2tpd process
	xl2tpd-control add-lac ${name} lns = ${lns} out if = ${device} pppoptfile = ${pppoptfile} autodial = no redial = yes redial timeout = 30

	[ ! -f "${l2tp_dnsfile}" ] && touch ${l2tp_dnsfile}
	if ! grep -q "^${lns}#${outif}" ${l2tp_dnsfile}; then
		if ! echo ${lns}|grep -q "^[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}$"; then
			echo "${lns}#${outif}" >>${l2tp_dnsfile}
		fi
	fi

	#if ipsec tunnel has been installed, then connect the client directly
	local ipsecdone=`uci -c ${pppox_l2tp_main_path} get l2tpclient-ipsec-info.${name} 2>/dev/null`
	if [ "${ipsecenc}" = "yes" ]; then
		if [ "${ipsecdone}" != "info" ]; then
			logger_reg ${LOGGER_MODULE_ID_L2TP} ${LOGGER_INFO_ID_IPSECNOTSUCC} "${tunnelname}"
		else
			logger_reg ${LOGGER_MODULE_ID_L2TP} ${LOGGER_INFO_ID_IPSECSUCC} "${tunnelname}"
		fi
	fi
	[ "${ipsecdone}" = "info" -o "${ipsecenc}" != "yes" ] && xl2tpd-control connect ${name}
}

##
#distribute the global config of l2tp server
_l2tp_distribute_global4server()
{
	local path=${l2tp_global4server_path}/config
	local hellodelay=`uci -c ${path} get ${pppox_configname}.l2tp_server_global.l2tphellointerval 2>/dev/null`
	xl2tpd-control add-global global hello delay server = ${hellodelay}
}

##
#distribute the global config of l2tp client
_l2tp_distribute_global4client()
{
	local path=${l2tp_global4client_path}/config
	local hellodelay=`uci -c ${path} get ${pppox_configname}.l2tp_client_global.l2tphellointerval 2>/dev/null`
	xl2tpd-control add-global global hello delay client = ${hellodelay}
}

l2tp_distribute()
{
	[ $# -ne 2 ] && return 1

	if [ "$1" = "lns" ]; then
		[ "$2" != "global" ] && _l2tp_distribute_server $2
		[ "$2" = "global" ] && _l2tp_distribute_global4server
	elif [ "$1" = "lac" ]; then
		[ "$2" != "global" ] && _l2tp_distribute_client $2
		[ "$2" = "global" ] && _l2tp_distribute_global4client
	else
		return 1 #error
	fi
}

_l2tp_cdistribute()
{
	local ltype=$1
	local lname=$2
	local option

	[ "${ltype}" == "lns" ] && option=server
	[ "${ltype}" == "lac" ] && option=client

	xl2tpd-control remove-${ltype} ${lname}

	if [ "${option}" = "client" ]; then
		local path=${pppox_l2tp_client_path}/${lname}/config
		local lns=`uci -c ${path} get ${pppox_configname}.${lname}.server_ip 2>/dev/null`
		local outif=`uci -c ${path} get ${pppox_configname}.${lname}.outif 2>/dev/null`
		sed -ri "/^${lns}#${outif}/d" ${l2tp_dnsfile}
	fi

	## wvr1300 can not deal regular expression: \s, use space
	sed -ri "/list +${option} +'${lname}'/d" ${pppox_l2tp_main_path}/${l2tp_ifdevice_info}
}

#contrary to l2tp_distribute
l2tp_cdistribute()
{
	[ $# -ne 2 ] && return 1

	if [ "$1" = "lns" ]; then
		[ "$2" != "global" ] && _l2tp_cdistribute lns $2
	elif [ "$1" = "lac" ]; then
		[ "$2" != "global" ] && _l2tp_cdistribute lac $2
	else
		return 1 #error
	fi
}

