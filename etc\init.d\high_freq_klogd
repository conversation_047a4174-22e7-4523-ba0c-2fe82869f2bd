#!/bin/sh /etc/rc.common

# Should start after loggerd is inited. Since high_freq_klogd serves loggerd.
START=16

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1
SERVICE_BIN=/usr/sbin/high_freq_klogd
SERVICE_PID_FILE=/var/run/high_freq_klogd.pid

start() {
    if [ ! -f ${SERVICE_PID_FILE} ];then
        service_start ${SERVICE_BIN} &
    fi
}

stop(){
    service_stop ${SERVICE_BIN}
    if [ -f ${SERVICE_PID_FILE} ];then
        rm -f ${SERVICE_PID_FILE}
    fi
}

restart() {
    echo "restarting high_freq_klogd service" > /dev/console
    stop
    start
}

reload() {
    restart
}
