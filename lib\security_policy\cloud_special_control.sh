#!/bin/sh
# purpose: 控制云相关（含商云和TUMS）连接的特殊处理
#          主要实现方式:
#          iptables新增cloud_special_input/cloud_special_output链，并添加到sec_policy_excep_input/sec_policy_excep_output中
# attention:
#           正常情况下，如果要放行特殊的连接通过配置安全策略来实现，但是在某些特殊的场景（比如策略提交场景，接口down up 场景），不希望策略模块对特定连接产生干扰
#           因此在放到sec_policy_excep_input/sec_policy_excep_output中进行处理

# OP： on 打开云管理 | off 关闭云管理
# CLOUD_TYPE： user_define 本地NUMS管理平台 |  public 商用网络云平台
OP=$1
CLOUD_TYPE=$2
PUBLIC_LONG_LINK_PORT="50443,60443"
USER_DEFINE_LONG_LINK_PORT="60442"
IPT_EXECUTOR="/lib/fw_policy_manager/ipt_rule_executor.sh"

case $OP in
    on)
        #add iptables chain
        $IPT_EXECUTOR add_subchain mangle cloud_special_input
        $IPT_EXECUTOR add_subchain mangle cloud_special_output

        #avoid add multiple rules
        $IPT_EXECUTOR get_policy_position mangle sec_policy_excep_input cloud_special_input
        if [ "$?" == "0" ]; then
            $IPT_EXECUTOR add_rule mangle sec_policy_excep_input " " cloud_special_input 1
        fi

        $IPT_EXECUTOR get_policy_position mangle sec_policy_excep_output cloud_special_output
        if [ "$?" == "0" ]; then
            $IPT_EXECUTOR add_rule mangle sec_policy_excep_output " " cloud_special_output 1
        fi

        #flush iptables and add rules
        $IPT_EXECUTOR flush_subchain mangle cloud_special_input
        $IPT_EXECUTOR flush_subchain mangle cloud_special_output

        if [ "public" == "$CLOUD_TYPE" ]; then
            # 云类型为商用云平台
            $IPT_EXECUTOR add_rule mangle cloud_special_input "-p tcp -m multiport --sport $PUBLIC_LONG_LINK_PORT" "ACCEPT" 1
            $IPT_EXECUTOR add_rule mangle cloud_special_output "-p tcp -m multiport --dport $PUBLIC_LONG_LINK_PORT" "ACCEPT" 1
        elif [ "user_define" == "$CLOUD_TYPE" ]; then
            # 云类型为本地NMS管理平台
            $IPT_EXECUTOR add_rule mangle cloud_special_input "-p tcp -m multiport --sport $USER_DEFINE_LONG_LINK_PORT" "ACCEPT" 1
            $IPT_EXECUTOR add_rule mangle cloud_special_output "-p tcp -m multiport --dport $USER_DEFINE_LONG_LINK_PORT" "ACCEPT" 1
        fi
    ;;
    off)
        #flush iptables
        $IPT_EXECUTOR flush_subchain mangle cloud_special_input
        $IPT_EXECUTOR flush_subchain mangle cloud_special_output
    ;;
    *)
        echo "ERROR: error arg in cloud_specical_control" > /dev/console
        return
    ;;
esac