#!/bin/sh
#author ChenXing
#Usage: 	
#		ipsec_handle_iptables.sh <operation> TARGET_CHAIN
#Chain:
#       ipsec_pre_rule: mark the enc packet that receive
#       ipsec_input_rule: transform the packet to NFQUEUE


IPT="iptables -t mangle -w"

operation=$1

if [ "$operation" = "add" -o "$operation" = "del" ]; then
	target_chain=$2
fi

try_add_pre_chain()
{
	if ! $IPT -S PREROUTING | grep ipsec_pre_rule &> /dev/null; then
		$IPT -I PREROUTING -j ipsec_pre_rule
	fi
}

try_add_input_chain()
{
	if ! $IPT -S INPUT | grep ipsec_input_rule &> /dev/null; then
		$IPT -A INPUT -p udp -j ipsec_input_rule
	fi
}

try_del_pre_chain()
{
	if $IPT -S PREROUTING | grep ipsec_pre_rule &> /dev/null; then
		$IPT -D PREROUTING -j ipsec_pre_rule
	fi
}

#init_ipsec_chain()
# {
#	$IPT  -N ipsec_pre_rule
#	$IPT  -N ipsec_input_rule
#	$IPT -I INPUT -p udp -j ipsec_input_rule
#	$IPT -A ipsec_pre_rule -p udp -j CONNMARK --dport 4500 --restore-mark --nfmask 0xfe000000 --ctmask 0xfe000000
#	$IPT -A ipsec_pre_rule -p udp -m mark ! --mark 0x0/0xfe000000 -j RETURN
#	$IPT -A ipsec_pre_rule -p udp -j CONNMARK --save-mark --nfmask 0xfe000000 --ctmask 0xfe000000
#	$IPT -A ipsec_input_rule -p udp -m l2tp --type control --dport 1701 -m policy --strict --dir in --pol ipsec --proto esp -j NFQUEUE --queue-num 0 --queue-bypass
#}

init_ipsec_chain()
{
	$IPT  -N ipsec_pre_rule
	$IPT  -N ipsec_input_rule
	try_add_input_chain
	$IPT -A ipsec_input_rule -p udp -m l2tp --type control --dport 1701 -m policy --strict --dir in --pol ipsec --proto esp -j NFQUEUE --queue-num 0 --queue-bypass
}

init_mangle_ipsec_mss_chain()
{
	if ! $IPT -S FORWARD | grep zone_MSSFIX &> /dev/null; then
		$IPT -N zone_MSSFIX
		$IPT -A FORWARD -p tcp -j zone_MSSFIX
	fi
	if ! $IPT -S zone_MSSFIX | grep IPSEC_IFACES &> /dev/null; then
		$IPT -A zone_MSSFIX -m iface_group --dev_set IPSEC_IFACES --iface_in 0 -p tcp -m tcp --tcp-flags SYN,RST SYN -m comment --comment "[ipsec]" -j TCPMSS --set-mss 1340
		$IPT -A zone_MSSFIX -m iface_group --dev_set IPSEC_IFACES --iface_in 0 -p tcp -m tcp --tcp-flags SYN,RST SYN -m comment --comment "[ipsec]" -j TCPMSS --clamp-mss-to-pmtu
	fi
}

if [ "${operation}" = "add" ]; then
	if [ "${target_chain}" = "pre" ]; then
		try_add_pre_chain
	fi
elif [ "${operation}" = "del" ]; then
	if [ "${target_chain}" = "pre" ]; then
		try_del_pre_chain
	fi
elif [ "${operation}" = "init" ]; then
	init_ipsec_chain
	init_mangle_ipsec_mss_chain
else 
	exit 0
fi

