#!/bin/sh
#when nat restart, all nat rules about pppox should be setted again
#author: wangdechu, ********

. /lib/functions.sh
. /lib/firewall/fw.sh
. /lib/pppox/pppox-default-variables.sh

get_param()
{
	config_get netmode $1 "netmode"
	config_get remotesubnet $1 "remotesubnet"
	config_get workmode $1 "workmode"
}

do_setnat()
{
	[ ${workmode} = "nat" ] && {
		iptables -w -t nat -C POSTROUTING -o ${ifname} -j MASQUERADE 2>/dev/null
		[ "$?" != "0" ] && fw add 4 nat POSTROUTING MASQUERADE  $ { -o ${ifname} }
	}
}

#traverse all tunnel to set nat
for dir in `ls ${pppox_ppppath}/pid 2>/dev/null`; do
	username=`uci -c ${pppox_ppppath}/pid get ${dir}.@info[0].username 2>/dev/null`
	isserver=`uci -c ${pppox_ppppath}/pid get ${dir}.@info[0].isserver 2>/dev/null`
	configpath=`uci -c ${pppox_ppppath}/pid get ${dir}.@info[0].configpath 2>/dev/null`
	ifname=`uci -c ${pppox_ppppath}/pid get ${dir}.@info[0].ifname 2>/dev/null`
	
	[ -z "${configpath}" ] && continue
	#configpath like "/tmp/l2tp/server/server1", note: no "config"
	path=${configpath}/config
	UCI_CONFIG_DIR=${path}
	
	if [ "${isserver}" = "1" ]; then
#workmode for server must be route
#		configname=${pppox_if_config}
#		config_load ${configname}
#		get_param ${username}
#		config_foreach config_clear
		workmode=route
	elif [ "${isserver}" = "0" ]; then
		configname=${pppox_configname}
		
		config_load ${configname}
		config_foreach get_param
		config_foreach config_clear
		netmode=lan2lan
	else
		return 1
	fi
	
	do_setnat

done

