#!/bin/sh

[ "$INTERFACE" == "loopback" ] && exit 0

get_isp_inface()
{
	config_get interface $1 if

	[ "$INTERFACE" == "$interface" ] && flag=1
}

check_inface_reset_isp()
{
	local flag=0

	. /lib/functions.sh

	config_load isp_route
	config_foreach get_isp_inface rule

	[ $flag -eq 1 ] && /etc/init.d/isp_route restart
}

. /lib/balance/api.sh

state=`get_balance_global_state`
[ "$state" == "off" ] && return

case "$ACTION" in
        ifup)
			[ -f /tmp/isp_route.ready ] && check_inface_reset_isp
        ;;
        ifdown)
			[ -f /tmp/isp_route.ready ] && check_inface_reset_isp
        ;;
esac
