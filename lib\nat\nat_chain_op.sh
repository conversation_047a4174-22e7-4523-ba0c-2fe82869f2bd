#!/bin/sh /etc/rc.common

IPT4="iptables -w -t nat"
IPT6="ip6tables -w -t nat"

nat_add_chain()
{
    local chain_name=$1
    local chain_name_main=$2
    local proto=$3
    local IPT=$IPT4
    #pos
    #A: the last position of the chain
    #I: the first position of the chain
    #default value: A
    local pos="A"
    if [ -n "$4" ];then
        pos=$4
    fi

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    local chain_exit=`$IPT -nvL | grep -w ${chain_name}`
    if [ -z "$chain_exit" ];then
        $IPT -N $chain_name
        if [ $? -ne 0 ]; then
            return
        fi
        $IPT -$pos $chain_name_main -j $chain_name

        echo "$IPT -N ${chain_name}" > /dev/console
        echo "$IPT -${pos} ${chain_name_main} -j ${chain_name}" > /dev/console
    fi
}

nat_del_chain()
{
    local chain_name=$1
    local chain_name_main=$2
    local proto=$3
    local IPT=$IPT4

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    local chain_rule_num=`$IPT -S ${chain_name} | wc -l`
    if [ -z "$chain_rule_num" -o $chain_rule_num -eq 1 ];then
        echo "$IPT -D ${chain_name_main} -j ${chain_name}" > /dev/console
        echo "$IPT -X ${chain_name}" > /dev/console
        $IPT -D ${chain_name_main} -j ${chain_name}
        $IPT -X ${chain_name}
    fi
}

nat_chain_exist()
{
    local chain_name=$1
    local proto=$2

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    local chain_exist=`$IPT -nvL | grep -w ${chain_name}`

    # echo $chain_exist > /dev/console

    if [ -z "$chain_exist" ];then
        echo "0"
    else
        echo "1"
    fi
}

# 查看链中是否还剩规则
nat_chain_rule_exist()
{
    local chain_name=$1
    local proto=$2

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    local chain_rule_num=`$IPT -S ${chain_name} | wc -l`

    if [ -z "$chain_rule_num" -o "$chain_rule_num" -eq 1 ]
    then
        echo "0"
    else
        echo "1"
    fi
}
