#!/bin/sh /etc/rc.common
# Copyright (C) 2006 OpenWrt.org
START=65
STOP=60
WIFIDOG_BIN="/usr/bin/wifidog"


start() {
	ipset create "Wechat Wifi" hash:mac -exist
	
	
	if [ ! -e "/var/run/wifidog.pid" ];then
		touch /var/run/wifidog.pid
		echo "wifidog service start" > /dev/console
		($WIFIDOG_BIN -d 4;sleep 2;/usr/lib/lua/wifidog/wifidog_reload.lua wifidog_load_portal;/usr/lib/lua/wifidog/wifidog_reload.lua wifidog_wlan_reload) &
		ipset -N AuthIplimit hash:ip,txrate,rxrate -exist
		ipset flush AuthIplimit

        has_wifidog_monitor=`cat /etc/sys_monitor.conf|grep wifidog`
        if [ "$has_wifidog_monitor" == "" ];then
            echo "" >> /etc/sys_monitor.conf
            echo 'wifidog#/etc/init.d/wifidog stop;/etc/init.d/wifidog start;touch /tmp/wifidog_restart.log;echo `date`>> /tmp/wifidog_restart.log' >> /etc/sys_monitor.conf
            sed -i '/^$/d' /etc/sys_monitor.conf
        fi
	fi

}

stop() {	
    PID=`ps | grep usr| grep bin| grep wifidog| grep -v grep | awk '{print $1}'`
    if [ "$PID" != "" ];then
        kill $PID > /dev/null
    fi

    rm /var/run/wifidog.pid

    has_wifidog_monitor=`cat /etc/sys_monitor.conf|grep wifidog`
    if [ "$has_wifidog_monitor" != "" ];then
        sed -i '/^$/d' /etc/sys_monitor.conf
        sed -i '/wifidog/d' /etc/sys_monitor.conf
    fi
}

restart() {
	stop
	start
}

reload() {
	restart
}
