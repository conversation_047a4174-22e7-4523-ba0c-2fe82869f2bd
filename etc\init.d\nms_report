#!/bin/sh /etc/rc.common

START=31

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/nms_report.pid

init_nms_report_cfg(){
	if [ ! -f "/etc/nms_report/event_params_default" ];then
		cp -f  /etc/nms_report/event_params /etc/nms_report/event_params_default
	fi

	if [ ! -f "/etc/nms_report/stats_report_default" ];then
		cp -f  /etc/nms_report/stats_report /etc/nms_report/stats_report_default
	fi
}

start() {
	init_nms_report_cfg
	if [ ! -f ${SERVICE_PID_FILE} ];then
		service_start /usr/sbin/nms_report &
	fi
}

_stop()
{
	service_stop /usr/sbin/nms_report
	if [ -f ${SERVICE_PID_FILE} ];then
		rm -f ${SERVICE_PID_FILE}
	fi
}

stop() {
	_stop
}


restart()
{
	echo "restarting cfg_save service" >/dev/console
	stop
	start
	touch /tmp/nms_report_log
	echo "nms_report restart" >> /tmp/nms_report_log
	date >> /tmp/nms_report_log
}

reload()
{
	restart
}
