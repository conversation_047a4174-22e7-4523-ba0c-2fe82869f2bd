--[[

File:		l2tp_process_manager.lua
Details:	check whether the l2tp related process need to exist
Author:		Chen Xing
Version:	0.1
Date:		15 May, 2017

]]--

local uci    = require "luci.model.uci"
local dbg    = require "luci.tools.debug"
local sys    = require "luci.sys"
local socket = require "socket"
local util = require "luci.util"
local err = require "luci.torchlight.error"
local uci_r = uci.cursor()

local PROCESS_FILTER = "l2tp"
local PROCESS_NAME = " xl2tpd"
local PID_NAME = "xl2tpd"

function check_l2tp_enable()

    local lac_list = {}
    local lns_list = {}
    local l2tp_client_state = "off"
    local l2tp_server_state = "off"
    local l2tp_state = "off"
    ----------------------------get all connection section ----------------------------
    uci_r:foreach("vpn", "lac",
        function(section)
            lac_list[#lac_list + 1] = uci_r:get_all("vpn", section[".name"])
            if not lac_list[#lac_list] then
                return false,err.ERR_COM_TABLE_ITEM_UCI_GET
            end        
        end     
    )

    for k, lac in ipairs(lac_list) do	
    	if nil ~= lac.enable and "on" == lac.enable then
    		l2tp_client_state = "on"
    		--dbg("there is l2tp client that enable")
    		break
    	end
    end
    uci_r:foreach("vpn", "lns",
        function(section)
            lns_list[#lns_list + 1] = uci_r:get_all("vpn", section[".name"])
            if not lns_list[#lns_list] then
                return false,err.ERR_COM_TABLE_ITEM_UCI_GET
            end        
        end     
    )

    for k, lns in ipairs(lns_list) do   
        if nil ~= lns.enable and "on" == lns.enable then
            l2tp_server_state = "on"
            --dbg("there is l2tp server that enable")
            break
        end
    end

    if l2tp_client_state == "on" or l2tp_server_state == "on" then
        l2tp_state = "on"
    end

    return l2tp_state
end

local function found_pid(my_cmd)

    local ps_info = io.popen("ps | grep %q" % my_cmd)
    local pid = false
    local tbl

    if ps_info then
        while true do
            local line = ps_info:read("*l")
            if not line then 
                break 
            end
            line = util.trim(line)
            tbl  = util.split(line, " +", 99, true)
            local cmd = tbl[5]
            --dbg(cmd)
            if cmd and cmd:match("^" .. my_cmd .. "$") then
                pid = tbl[1]
                --dbg(pid)
                break
            end
        end
        ps_info:close()
    end
    return pid
end

local function check_process()

    local ps_info = io.popen("ps | grep %q" % PROCESS_FILTER)
    local process_exist = false

    if ps_info then
        while true do
            local line = ps_info:read("*l")
            if not line then 
                break 
            end
            --dbg(line)
            local i,j = string.find(tostring(line),PROCESS_NAME)
    		if i and j then
    			process_exist = true
    			--dbg("find process exist")
    			break
    		end  	
        end
        ps_info:close()
    end
    return process_exist
end


local function manage_process(l2tp_state,process_state)
	if nil == l2tp_state or nil == process_state then
		return false
	end

	if "on" == l2tp_state and false == process_state then
        dbg("L2TP_PROCESS_MANAGER: the l2tp is enable but the process is not on, so start xl2tpd")
        cmd=string.format("xl2tpd")              
        sys.fork_call(cmd)

    --[[elseif "off" == l2tp_state and true == process_state then
        dbg("L2TP_PROCESS_MANAGER: the l2tp is not enable but the process is not on, so kill xl2tpd")
        local pid = found_pid(PID_NAME)
        if pid then
            luci.sys.call("kill -9 %q" % pid)
        end
    ]]--
    end

	return true
end

--dbg("l2tp process manager begin")
local t1 = socket.gettime()

local l2tp_state = check_l2tp_enable()
local process_state = check_process()

manage_process(l2tp_state,process_state)

local t2 = socket.gettime()

dbg("  l2tp_check_process.lua time=[", t2-t1, "]")






