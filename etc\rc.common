#!/bin/sh
# Copyright (C) 2006-2012 OpenWrt.org

. $IPKG_INSTROOT/lib/functions.sh
. $IPKG_INSTROOT/lib/functions/service.sh

initscript=$1
action=${2:-help}
shift 2

start() {
	return 0
}

stop() {
	return 0
}

reload() {
	return 1
}

restart() {
	trap '' TERM
	stop "$@"
	start "$@"
}

boot() {
	start "$@"
}

shutdown() {
	stop
}

disable() {
	name="$(basename "${initscript}")"
	rm -f "$IPKG_INSTROOT"/etc/rc.d/S??$name
	rm -f "$IPKG_INSTROOT"/etc/rc.d/K??$name
}

enable() {
	name="$(basename "${initscript}")"
	disable
	[ -n "$START" -o -n "$STOP" ] || {
		echo "/etc/init.d/$name does not have a START or STOP value"
		return 1
	}
	[ "$START" ] && ln -s "../init.d/$name" "$IPKG_INSTROOT/etc/rc.d/S${START}${name##S[0-9][0-9]}"
	[ "$STOP"  ] && ln -s "../init.d/$name" "$IPKG_INSTROOT/etc/rc.d/K${STOP}${name##K[0-9][0-9]}"
}

enabled() {
	name="$(basename "${initscript}")"
	[ -x "$IPKG_INSTROOT/etc/rc.d/S${START}${name##S[0-9][0-9]}" ]
}

depends() {
	return 0
}

help() {
	cat <<EOF
Syntax: $initscript [command]

Available commands:
	start	Start the service
	stop	Stop the service
	restart	Restart the service
	reload	Reload configuration files (or restart if that fails)
	enable	Enable service autostart
	disable	Disable service autostart
$EXTRA_HELP
EOF
}

${INIT_TRACE:+set -x}

. "$initscript"

ALL_COMMANDS="start stop reload restart boot shutdown enable disable enabled depends ${EXTRA_COMMANDS}"
list_contains ALL_COMMANDS "$action" || action=help
[ "$action" = "reload" ] && action='eval reload "$@" || restart "$@" && :'
$action "$@"
