#!/bin/sh
. /lib/functions.sh
. /lib/netifd/netifd-proto.sh

PPP_IPPARAM="$6"

dhcp6c_dir="/tmp/dhcp6c/$PPP_IPPARAM"
conffile="$dhcp6c_dir/dhcp6c.conf"
dhcp6cpid="$dhcp6c_dir/dhcp6c.pid"
dhcp6cscript="/lib/netifd/ppp-dhcp6c.script"

proto_dhcpcv6_create_conffile(){
	local pd_mode="$1"
	local ifname="$2"
	local ip_config="$3"
	local dhcp_if="$4"
	local id

	local mac="$(ifconfig "$dhcp_if" | sed -ne 's/[[:space:]]*$//; s/.*HWaddr //p')"
	local oIFS="$IFS"; IFS=":"; set -- $mac; IFS="$oIFS"
	let "id = ((0x$1^0x$2^0x$3^0x$4) << 16) | (0x$5 << 8) | (0x$6)"
	local id2=`expr $id + 1`

	rm -rf ${dhcp6c_dir}
	mkdir -p ${dhcp6c_dir}

	if [ "$ip_config" == "static" ]; then
		echo "interface $ifname {" > "${conffile}"
		[ -z "$dns6" ] && {
			echo -e "\tinformation-only;" >> "${conffile}"
			echo -e "\trequest domain-name-servers;" >> "${conffile}"
		}
		echo -e "\tscript \"$dhcp6cscript\";" >> "${conffile}"
		echo "};" >>  "${conffile}"
	else
		[ "$pd_mode" == "prefix" ] && {
			echo "interface $ifname {" > "${conffile}"
			[ "$ip_config" == "dhcpv6" ] && echo -e "\tsend ia-na $id;" >> "${conffile}"
			echo -e "\tsend ia-pd $id2;" >> "${conffile}"
			[ -z "$dns6" ] && echo -e "\trequest domain-name-servers;" >> "${conffile}"
			echo -e "\tscript \"$dhcp6cscript\";" >> "${conffile}"
			echo -e "};\n" >>  "${conffile}"
			echo -e "id-assoc na $id {};\n" >> "${conffile}"
			echo "id-assoc pd $id2 {" >> "${conffile}"
			echo -e "\tprefix-interface $dhcp_if {" >> "${conffile}"
			echo -e "\t\tsla-id 1;" >> "${conffile}"
			echo -e "\t};" >>  "${conffile}"
			echo "};" >>  "${conffile}"
		}
		[ "$pd_mode" == "non_temp" ] && {
			echo "interface $ifname {" > "${conffile}"
			[ "$ip_config" == "dhcpv6" ] && echo -e "\tsend ia-na $id;" >> "${conffile}"
			[ "$ip_config" == "slaac" ] && echo -e "\tinformation-only;" >> "${conffile}"
			[ -z "$dns6" ] && echo -e "\trequest domain-name-servers;" >> "${conffile}"
			echo -e "\tscript \"$dhcp6cscript\";" >> "${conffile}"
			echo "};" >>  "${conffile}"
			echo "id-assoc na $id {};" >> "${conffile}"
		}
	fi
}

dhcpcv6_write_duid() {
	local client_device="$2"
	local mac="${1:-$(ifconfig "$client_device" | sed -ne 's/[[:space:]]*$//; s/.*HWaddr //p')}"
	local pat="[0-9A-F][0-9A-F]"
	case "$mac" in
		# exactly 6 octets -> assume type 3 (DUID-LL - RFC3315, 9.4)
		$pat:$pat:$pat:$pat:$pat:$pat)
			local oIFS="$IFS"; IFS=":"; set -- $mac; IFS="$oIFS"

			# low endian
			if [ "$(printf \\1 | hexdump -n1 -ve '8/2 "" "%04x" ""')" = "0001" ]; then
				printf \\x0a\\x00

			# big endian
			else
				printf \\x00\\x0a
			fi

			printf \\x00\\x03\\x00\\x06\\x$1\\x$2\\x$3\\x$4\\x$5\\x$6
		;;
		# at least 7 octets -> could be type 1 or type 2
		$pat:$pat:$pat:$pat:$pat:$pat:*)
			local len_id=":$(echo "$mac" | sed -e 's/[^:]//g')"
			local len_hi=$(printf "%02x" $((${#len_id} / 0xFF)) )
			local len_lo=$(printf "%02x" $((${#len_id} % 0xFF)) )

			# low endian
			if [ "$(printf \\1 | hexdump -n1 -ve '8/2 "" "%04x" ""')" = "0001" ]; then
				printf \\x$len_lo\\x$len_hi

			# big endian
			else
				printf \\x$len_hi\\x$len_lo
			fi

			printf $(echo "$mac" | sed -e 's/^/\\x/; s/:/\\x/g')
		;;
		*)
			echo "Unable to derive DUID from interface '$client_device' and no valid user DUID given"
		;;
	esac
}

setup_slaac () {
	local interface=$1
	local ifname=$2

	proto_init_update "$ifname" 1
	proto_set_keep 1

	while read line
	do
		[ $ifname != ${line##* } ] && continue
		[ "fe80" = ${line:0:4} ] && continue
		element=${line:0:32}
		ip6addr=${element:0:4}:${element:4:4}:${element:8:4}:${element:12:4}
		ip6addr=$ip6addr:${element:16:4}:${element:20:4}:${element:24:4}:${element:28:4}
		new_ip6addr=$ip6addr
	done </proc/net/if_inet6

	[ -z "$new_ip6addr" ] && {
		proto_notify_error "$interface" "NO ADDRESS"
		sleep 3
		proto_setup_failed "$interface"
		return
	}

	if [ -n "$new_ip6addr" ]; then
		proto_add_ipv6_address "$new_ip6addr" "64"
	fi

	local ip6gw=`cat /proc/sys/net/ipv6/conf/$ifname/default_gateway`
	[ ${#ip6gw} -ne 0 ] && proto_add_ipv6_route "::" 0 "$ip6gw"

	# not support parse RDNSS options yet, set default dns6 instead
	[ -z "$dns6" ] && dns6="240c::6666 240c::6644"
	[ -n "$dns6" ] && {
		DNS2=${dns6##*[, ]}
		DNS1=${dns6%%[, ]*}
		[ -n "$DNS1" ] && {
			proto_add_dns_server "$DNS1"
		}
		[ -n "$DNS1" -a -n "$DNS2" -a "$DNS1" != "$DNS2" ] && {
			proto_add_dns_server "$DNS2"
		}
	}

	proto_send_update "$interface"

	# wait interface to setup
	sleep 3
	local gw_route=`route -A inet6 | grep ::/0`
	[ -z ${gw_route} ] && {
		local gateway=`cat /proc/sys/net/ipv6/conf/$ifname/default_gateway`
		[ ${#gateway} -ne 0 ] && route -A inet6 add default gw "$gateway" dev "$ifname" metric 1024
	}
}

config_load network
config_get ip_config $PPP_IPPARAM ip_config
config_get pd_mode $PPP_IPPARAM pd_mode
config_get dns $PPP_IPPARAM dns
config_get ip6addr $PPP_IPPARAM ip6addr
dns6=""
[ -n "$dns" ] && {
	for val in $dns; do
		local rslt=`echo $val | grep ":"`
		if [ -n "$rslt" ]; then
			[ -n "$dns6" ] && dns6="$dns6 $val" || dns6="$val"
		fi
	done
}

ppp_dir=/tmp/pppv6/$PPP_IPPARAM
ppp_up="$ppp_dir/ppp_up"

# no need to configure ipv4
[ -n "$LLREMOTE" ] || exit 1

if [ "$ip_config" == "static" ]; then
	[ -n "$LLREMOTE" ] && {
		proto_init_update "$IFNAME" 1
		proto_set_keep 1

		[ -n "$PPP_IPPARAM" ] && {
			[ -n "$LLLOCAL" ] && proto_add_ipv6_address "$LLLOCAL" 64
			[ -n "$ip6addr" ] && {
				ifconfig "$IFNAME" add "$ip6addr"
				local ipaddr=${ip6addr%%/*}
				local mask=${ip6addr##*/}
				proto_add_ipv6_address "$ipaddr" "$mask"
			}
			[ -n "$LLREMOTE" ] &&
			{
				proto_add_ipv6_route "::" 0 "$LLREMOTE"
				route -A inet6 add default gw "$LLREMOTE" dev "$IFNAME" metric 1024
			}
		}

		[ -n "$dns6" ] && {
			DNS2=${dns6##*[, ]}
			DNS1=${dns6%%[, ]*}
			[ -n "$DNS1" ] && {
				proto_add_dns_server "$DNS1"
			}
			[ -n "$DNS1" -a -n "$DNS2" -a "$DNS1" != "$DNS2" ] && {
				proto_add_dns_server "$DNS2"
			}
		}
		proto_send_update "$PPP_IPPARAM"
	}
else
	[ -n "$PPP_IPPARAM" -a -n "$LLREMOTE" ] && {
		mkdir -p $ppp_dir
		[ -n "$IPLOCAL" -a "$ipv4" == "1" ] && echo "$IPLOCAL" > "$ppp_up" || echo "none" > "$ppp_up"
		[ -n "$IPREMOTE" -a "$ipv4" == "1" ] && echo "$IPREMOTE" >> "$ppp_up" || echo "none" >> "$ppp_up"
		[ -n "$LLLOCAL" ] && echo "$LLLOCAL" >> "$ppp_up" || echo "none" >> "$ppp_up"
		[ -n "$LLREMOTE" ] && echo "$LLREMOTE" >> "$ppp_up" || echo "none" >> "$ppp_up"
		[ -n "$DNS1" -a "$ipv4" == "1" ] && echo "$DNS1" >> "$ppp_up" || echo "none" >> "$ppp_up"
		[ -n "$DNS2" -a "$DNS1" != "$DNS2" -a "$ipv4" == "1" ] && echo "$DNS2" >> "$ppp_up" ||  echo "none" >> "$ppp_up"
	}
fi

[ -n "$LLREMOTE" ] && {
	local mbit=1
	local count=15
	local dhcp_ifname=$2

	# reset the mbit value to -1, send RS
	# echo "-1" > /proc/sys/net/ipv6/conf/$IFNAME/ndisc_mbit
	local config=`uci get network.${PPP_IPPARAM%%-*}.t_bindif`
	icmpv6_rs $IFNAME $config
	[ "$ip_config" != "static" ] && echo "2"  > /proc/sys/net/ipv6/conf/$IFNAME/accept_ra

	# clear static route to enable receipt of RA
	[ "$ip_config" != "static" ] && route -A inet6 del default dev "$IFNAME" metric 1024

	# check slaac mode first
	[ "$ip_config" == "slaac" ] && {
		mbit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_mbit`
		while [ $count -ne 0 -a $mbit -eq -1 ]; do
			echo "2"  > /proc/sys/net/ipv6/conf/$IFNAME/accept_ra
			icmpv6_rs $IFNAME $config
			sleep 2
			mbit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_mbit`
			let "count=count-1"
		done

		if [ $mbit -ne 0 ]; then
			proto_notify_error "$PPP_IPPARAM" "INVALID_RADVD_SERVER"
			sleep 3
			local sharev4=$(uci get network.$PPP_IPPARAM.pppoe_sharev4)
			if [ $sharev4 == "off" ]; then
				proto_setup_failed "$PPP_IPPARAM"
			fi
			return
		fi
		obit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_obit`
	}

	[ "$ip_config" == "auto" ] && {
		# wait for RA
		mbit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_mbit`
		while [ $count -ne 0 -a $mbit -eq -1 ]; do
			echo "2"  > /proc/sys/net/ipv6/conf/$IFNAME/accept_ra
			icmpv6_rs $IFNAME $config
			sleep 2
			mbit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_mbit`
			let "count=count - 1"
		done

		# if timeout or mbit != 0, restart
		if [ $mbit -eq 0 ]; then
			ip_config="slaac"
			obit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_obit`
		elif [ $mbit -eq 1 ]; then
			ip_config="dhcpv6"
		else
			proto_notify_error "$PPP_IPPARAM" "NO_RADVD_SERVER"
			sleep 3
			local sharev4=$(uci get network.$PPP_IPPARAM.pppoe_sharev4)
			if [ $sharev4 == "off" ]; then
				proto_setup_failed "$PPP_IPPARAM"
			fi
			return
		fi
	}

	# dhcpv6 client is not necessary in stateless RDNSS mode
	[ "$ip_config" == "slaac" -a "$pd_mode" == "non_temp" ] && {
		if [ "$obit" -eq 0 -o "x$dns6" != "x" ]; then
			setup_slaac $PPP_IPPARAM $IFNAME
			return
		fi
	}

	# dhcpv6 client is not necessary in static ip with manual dns6 mode
	[ "$ip_config" == "static" ] && {
		return
	}

	# kill old dhcp6c process
	if [ -f $dhcp6cpid ]; then
		local pid=`cat $dhcp6cpid`
		[ -n "$pid" ] && kill -9 $pid
	fi

	proto_dhcpcv6_create_conffile "$pd_mode" "$IFNAME" "$ip_config" "$dhcp_ifname"
	local duid_file="$dhcp6c_dir/dhcp6c_duid"
	dhcpcv6_write_duid "$user_duid" "$dhcp_ifname" > $duid_file

	/usr/sbin/dhcp6c -f -p "$dhcp6cpid" -c "$conffile" -u "$duid_file" -t "$PPP_IPPARAM" "$IFNAME" &
}
