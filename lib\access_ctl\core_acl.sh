#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     access_ctl core.sh
# brief    
# author   <PERSON> chen
# version  1.0.0
# date     08June15
# histry   arg 1.0.0, 08June15, <PERSON> chen, Create the file. 
. /lib/access_ctl/time.sh

ACL_LOG()
{
	[ ! -e /tmp/access_ctl ] && mkdir touch /tmp/access_ctl
	[ -f /tmp/access_ctl/access_ctl.log ] && {
		local size="`ls -l /tmp/access_ctl/access_ctl.log | awk '{ print $5 }'`"
		if [ $size -gt 3072000 ]; then
			rm /tmp/access_ctl/access_ctl.log
		fi
	}
	[ ! -f /tmp/access_ctl/access_ctl.log ] && touch /tmp/access_ctl/access_ctl.log
	#echo "$1" >> /tmp/access_ctl/access_ctl.log
	echo "core_acl $1"
}

local known_service="FTP SSH TELNET SMTP DNS HTTP POP3 SNTP H323 TCPUDP"
fw_check()
{
	local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
	[ -n "$ret" ] && return 0||return 1
}

fw_acl_ipset_check()
{
	#echo "argv1=$1"
	local ret=$(ipset -L|grep $1 | awk '{print $2}')

	#[ "$ret" == "$1" ] && return 0||return 1
	return 0

}

fw_acl_config_get_rule_acl_inner() {
	fw_config_get_section "$1" rule_acl_inner { \
		string name "" \
		string zone "" \
		string policy "" \
		string service "" \
		string src "" \
		string dest "" \
		string time "" \
		string flag "" \
	} || return

}

fw_acl_config_get_rule_acl_btw() {
	fw_config_get_section "$1" rule_acl_btw { \
		string name "" \
		string zone "" \
		string policy "" \
		string service "" \
		string src "" \
		string dest "" \
		string time "" \
		string flag "" \
	} || return

}

service_config_get() {
	fw_config_get_section "$1" service { \
		string name "" \
		string proto "" \
		string sport "" \
		string dport "" \
		string type "" \
		string code "" \
		string comment "" \
		string flag "" \
	} || return
}

# need to reverse the rule order
fw_acl_do_acl_inner_rule()
{
	#fw_acl_config_get_rule_acl_inner $1
	local rule_acl_inner_service=""
	local rule_acl_inner_policy=""
	local rule_acl_inner_src=""
	local rule_acl_inner_time=""
	local rule_acl_inner_dest=""
	local rule_acl_inner_zone=""
	local rule_acl_inner_flag=""
	local rule_acl_inner_name=""
	local loop=0
	local destroy_flag=$2
	local result=$(uci get access_ctl.@rule_acl_inner[$loop].name 2>/dev/null)
	while [ x$result != 'x' ];do
		rule_acl_inner_service=$(uci get access_ctl.@rule_acl_inner[$loop].service 2>/dev/null)
		rule_acl_inner_policy=$(uci get access_ctl.@rule_acl_inner[$loop].policy 2>/dev/null)
		rule_acl_inner_src=$(uci get access_ctl.@rule_acl_inner[$loop].src 2>/dev/null)
		rule_acl_inner_time=$(uci get access_ctl.@rule_acl_inner[$loop].time 2>/dev/null)
		rule_acl_inner_dest=$(uci get access_ctl.@rule_acl_inner[$loop].dest 2>/dev/null)
		rule_acl_inner_zone=$(uci get access_ctl.@rule_acl_inner[$loop].zone 2>/dev/null)
		rule_acl_inner_name=$(uci get access_ctl.@rule_acl_inner[$loop].name 2>/dev/null)
		rule_acl_inner_flag=1

		ACL_LOG "$rule_acl_inner_service,$rule_acl_inner_policy,$rule_acl_inner_src,$rule_acl_inner_time,$rule_acl_inner_dest,$rule_acl_inner_zone,$rule_acl_inner_name"
		ACL_LOG "[destroy_flag:$destroy_flag]"
		#service_config_get $rule_acl_inner_service
		local service_name=""
		local service_sport=""
		local service_dport=""
		local service_proto=""
		local service_type=""
		local service_code=""
		local service_found=0
		local service_loop=0
		local result=$(uci get service.@service[$service_loop].name 2>/dev/null)
		while [ x$result != 'x' ];do
			service_name=$(uci get service.@service[$service_loop].name 2>/dev/null )
			service_sport=$(uci get service.@service[$service_loop].sport 2>/dev/null)
			service_dport=$(uci get service.@service[$service_loop].dport 2>/dev/null)
			service_proto=$(uci get service.@service[$service_loop].proto 2>/dev/null)
			service_type=$(uci get service.@service[$service_loop].type 2>/dev/null)
			service_code=$(uci get service.@service[$service_loop].code 2>/dev/null)
			if [ $service_name == $rule_acl_inner_service ];then
				service_found=1
				break
			fi
			service_loop=$(($service_loop+1))
			result=$(uci get service.@service[$service_loop].name 2>/dev/null)
		done

		if [ x$service_found != 'x' ];then
			ACL_LOG "uci_info_find:${service_name},${service_sport},${service_dport},${service_proto},${service_type},${service_code}"
		else
			ACL_LOG "service not found"
			return
		fi

		rule_acl_inner_policy=$(echo $rule_acl_inner_policy|tr 'a-z' 'A-Z')
		[ "$rule_acl_inner_policy" == "ACCEPT" ] && rule_acl_inner_policy="RETURN"

		[ "$rule_acl_inner_flag" == "1" ] && {
		if [ $destroy_flag == 1 ];then
			ACL_LOG "add rule ${loop}"
			ACL_LOG "[rule_acl_inner_time:$rule_acl_inner_time]"
			if [ $rule_acl_inner_time != "Any" ];then
				ACL_LOG "add_timeobj:$rule_acl_inner_name to time object:$rule_acl_inner_time,rule will be added by time_mngt"
				add_timeobj $rule_acl_inner_time $rule_acl_inner_name
				loop=$(($loop+1))
				result=$(uci get access_ctl.@rule_acl_inner[$loop].name 2>/dev/null)
				continue
			fi

			#devices=$(zone_get_effect_devices "${rule_acl_inner_zone}")
			ACL_LOG "[rule_acl_inner_src:$rule_acl_inner_src]"
			set_src_rule=""
			if [ $rule_acl_inner_src != "IPGROUP_ANY" ];then
				src_group=$(echo $rule_acl_inner_src |grep "_REV$")

				if [ -n "$src_group" ]; then
					rule_acl_inner_src=${rule_acl_inner_src%_*}

					fw_acl_ipset_check $rule_acl_inner_src
					[ x$? == x0 ] && {
						set_src_rule="-m set ! --match-set $rule_acl_inner_src src"
					} || return
					
						
				else
					fw_acl_ipset_check $rule_acl_inner_src
					[ x$? == x0 ] && {
						set_src_rule="-m set --match-set $rule_acl_inner_src src"
					} || return

				fi
			else
				set_src_rule=""		# IPGROUP_ANY
			fi
			ACL_LOG "set_src_rule=$set_src_rule"
			ACL_LOG "[rule_acl_inner_dest:$rule_acl_inner_dest]"
			set_dst_rule=""
			if [ $rule_acl_inner_dest != "IPGROUP_ANY" ];then
				dst_group=$(echo $rule_acl_inner_dest |grep "_REV$")
				if [ -n "$dst_group" ]; then
					rule_acl_inner_dest=${rule_acl_inner_dest%_*}
					fw_acl_ipset_check $rule_acl_inner_dest
					[ x$? == x0 ] && {
						set_dst_rule="-m set ! --match-set $rule_acl_inner_dest dst"

					} || return

						
				else
					fw_acl_ipset_check $rule_acl_inner_dest
					[ x$? == x0 ] && {
						set_dst_rule="-m set --match-set $rule_acl_inner_dest dst"
					} || return

				fi
					
			else
				set_dst_rule=""
			fi
			ACL_LOG "set_dst_rule=$set_dst_rule"

			local all_flag=0
			local sdvpn_flag=0
			[ "$rule_acl_inner_zone" == "ALL" ] && all_flag=1
			[ "$rule_acl_inner_zone" == "SDVPN_IFACES" ] && sdvpn_flag=1
			local interface=""
			local device=""
			ACL_LOG "[rule_acl_inner_zone:$rule_acl_inner_zone]"
			for interface in ${rule_acl_inner_zone};do
				local xyy
				if [ $all_flag == '1' -o $sdvpn_flag == '1' ];then
					xyy="eth0"
				else
					xyy=$(zone_get_effect_ifaces $interface)
					xyy=$(zone_get_device_byif $xyy)
				fi
				service_name=$(echo $service_name | tr 'a-z' 'A-Z')
				ACL_LOG "service_name:${service_name}"
				for device in $xyy;do
					[ -z $device ] && continue
					local iface_flag=1
					zone_dev_is_vpn $device
					if [ $? -eq 0 ]; then
						iface_flag=0
					fi

					if [ "$service_name" == "ALL" ]; then #all
						local rule=""
						if [ $all_flag == '1' ];then
							rule="$set_src_rule"" ""$set_dst_rule"
						elif [ $sdvpn_flag == '1' ]; then
							rule="-m iface_group --dev_set SDVPN_IFACES --iface_in 0 ""$set_src_rule"" ""$set_dst_rule"
						else
							rule="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag} ""$set_src_rule"" ""$set_dst_rule"
						fi

						#fw add 4 f access_ctl ${rule_acl_inner_policy} $ { "$rule" }
						ACL_LOG "position 1: add rule with device ${device} of loop ${loop} to iptables"
						iptables -w -A access_ctl $rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}

					else
						ACL_LOG "enter into non-all branch"
						if [ "$service_proto" != "icmp" ]; then
							service_proto=$(echo $service_proto | tr 'a-z' 'A-Z')
							ACL_LOG "service_proto:${service_proto}"
							if [ $service_proto == "TCP" -o $service_proto == "UDP" ];then # tcp or udp
								service_sport=$(echo $service_sport|tr '-' ':')
								service_dport=$(echo $service_dport|tr '-' ':')
								ACL_LOG "service_proto=$service_proto"
								#for proto in $service_proto; do
								local proto=$(echo $service_proto | tr 'A-Z' 'a-z')
								local tmp=""
									if [ $sdvpn_flag == '1' ]; then
										tmp="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
									elif [ $all_flag != '1' ];then
										tmp="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
									fi
									ACL_LOG "position 2: add rule with device ${device} of loop ${loop} to iptables"
									iptables -w -A access_ctl $tmp -p $proto -m $proto --sport $service_sport --dport $service_dport \
										$set_src_rule  $set_dst_rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}

								#done
							else  # proto other
								ACL_LOG "enter into proto other branch"  #tcp/udp
								if [ $service_proto == "TCP-UDP" ];then #TCP/UDP
									service_sport=$(echo $service_sport|tr '-' ':')
									service_dport=$(echo $service_dport|tr '-' ':')
									echo "service_proto=$service_proto"
									for proto in $service_proto; do
										local tmpx=""
										if [ $sdvpn_flag == '1' ]; then
											tmpx="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
										elif [ $all_flag != '1' ];then
											tmpx="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
										fi
										ACL_LOG "position 3: add rule with device ${device} of loop ${loop} to iptables"
										iptables -w -A access_ctl $tmpx -p tcp -m tcp --sport $service_sport --dport $service_dport \
											$set_src_rule  $set_dst_rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}
										iptables -w -A access_ctl $tmpx -p udp -m udp --sport $service_sport --dport $service_dport \
											$set_src_rule  $set_dst_rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}
									done
								else  #other
									for proto in $service_proto;do #other
										local tmpy=""
											if [ $sdvpn_flag == '1' ]; then
												tmpy="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
											elif [ $all_flag != '1' ];then
												tmpy="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
											fi
										ACL_LOG "position 4: add rule with device ${device} of loop ${loop} to iptables"
										iptables -w -A access_ctl $tmpy -p $proto $set_src_rule $set_dst_rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}
									done
								fi
								
							fi

						else  #icmp
							if [ "$service_name" == "ICMP_ALL" ]; then #icmp_all
								local tmpz=""
								if [ $sdvpn_flag == '1' ]; then
									tmpz="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
								elif [ $all_flag != '1' ];then
									tmpz="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
								fi
								ACL_LOG "position 5: add rule with device ${device} of loop ${loop} to iptables"
								iptables -w -A access_ctl $tmpz -p $service_proto --icmp-type any \
									$set_src_rule $set_dst_rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}

							else # icmp service_type & service_code
								local tmpu=""
								if [ $sdvpn_flag == '1' ]; then
									tmpu="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
								elif [ $all_flag != '1' ];then
									tmpu="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
								fi
								ACL_LOG "position 6: add rule with device ${device} of loop ${loop} to iptables"
								iptables -w -A access_ctl $tmpu -p $service_proto -m $service_proto --icmp-type $service_type/$service_code \
									$set_src_rule $set_dst_rule -m comment --comment ${rule_acl_inner_name}_1acl -j ${rule_acl_inner_policy}
							fi

						fi

					fi
				done
			done
		else
			ACL_LOG "this should print out"
			ACL_LOG "delelte rule ${loop}, actually do nothing"
			if [ $destroy_flag != 2 ];then
				ACL_LOG "destroy_flag is not 1 or 2"
				return 1
			fi
			ACL_LOG "del_timeobj:$rule_acl_inner_name to time object:$rule_acl_inner_time"
			[ $rule_acl_inner_time != "Any" ] && del_timeobj $rule_acl_inner_time $rule_acl_inner_name
		fi
		#conntrack -F
	}
	loop=$(($loop+1))
	result=$(uci get access_ctl.@rule_acl_inner[$loop].name 2>/dev/null)
done

	
}

fw_acl_do_acl_btw_rule()
{
	fw_acl_config_get_rule_acl_btw $1
	service_config_get $rule_acl_btw_service


	[ "$rule_acl_btw_flag" == "2" ] && {
		src_zone=${rule_acl_btw_zone%-*}
		dst_zone=${rule_acl_btw_zone#*-}
	}
	
	rule_acl_btw_policy=$(echo $rule_acl_btw_policy|tr 'a-z' 'A-Z')
	
	[ "$rule_acl_btw_flag" == "2" ] && {
		src_devices=$(zone_get_effect_devices "${src_zone}")
		dst_devices=$(zone_get_effect_devices "${dst_zone}")

		set_src_rule=""
		if [ $rule_acl_btw_src != "IPGROUP_ANY" ];then
			src_group=$(echo $rule_acl_btw_src |grep "REV")
			
			if [ -n "$src_group" ]; then
				rule_acl_btw_src=${rule_acl_btw_src%_*}
				
				fw_acl_ipset_check $rule_acl_btw_src
				[ x$? == x0 ] && {
					set_src_rule="-m set ! --match-set $rule_acl_btw_src src"
				} || return
				
					
			else
				fw_acl_ipset_check $rule_acl_btw_src
				[ x$? == x0 ] && {
					set_src_rule="-m set --match-set $rule_acl_btw_src src"
				} || return
				
			fi
		else
			set_src_rule=""		# IPGROUP_ANY
		fi
		#echo "set_src_rule=$set_src_rule"

		set_dst_rule=""
		if [ $rule_acl_btw_dest != "IPGROUP_ANY" ];then
			dst_group=$(echo $rule_acl_btw_dest |grep "REV")
			if [ -n "$dst_group" ]; then
				rule_acl_btw_dest=${rule_acl_btw_dest%_*}
				fw_acl_ipset_check $rule_acl_btw_dest
				[ x$? == x0 ] && {
					set_dst_rule="-m set ! --match-set $rule_acl_btw_dest dst"

				} || return
				
					
			else
				fw_acl_ipset_check $rule_acl_btw_dest
				[ x$? == x0 ] && {
					set_dst_rule="-m set --match-set $rule_acl_btw_dest dst"
				} || return
				
			fi
				
		else
			set_dst_rule=""
		fi
		#echo "set_dst_rule=$set_dst_rule"

		for src_device in $src_devices;do
			[ -z $src_device ] && continue
			for dst_device in $dst_devices; do
				[ -z $dst_device ] && continue
				if [ "$service_name" == "ALL" ]; then
					local rule="-i $src_device -o $dst_device ""$set_src_rule"" ""$set_dst_rule"
					fw add 4 f access_ctl ${rule_acl_btw_policy} $ { "$rule" }
				
			
				else
				
					if [ "$service_proto" != "icmp" ]; then
				
						service_sport=$(echo $service_sport|tr '-' ':')
						service_dport=$(echo $service_dport|tr '-' ':')

						for proto in $service_proto; do
							fw add 4 f access_ctl ${rule_acl_btw_policy} $ \
								{ -i $src_device -o $dst_device -p $proto -m $proto --sport $service_sport \
								--dport $service_dport "$set_src_rule"" ""$set_dst_rule" }
						done
					
					else
						if [ "$service_name" == "ICMP_ALL" ]; then
							
							
							fw add 4 f access_ctl ${rule_acl_btw_policy} $ \
								{ -i $src_device -o $dst_device -p $service_proto --icmp-type any \
								"$set_src_rule"" ""$set_dst_rule" }
							
						else
							fw add 4 f access_ctl ${rule_acl_btw_policy} $ \
								{ -i $src_device -o $dst_device -p $service_proto -m $service_proto \
								--icmp-type $service_type/$service_code "$set_src_rule"" ""$set_dst_rule" }
							
						fi
					fi
				fi
			done
		done
	}
}

#destroy_flag = 1 means 'add rules'
#destroy_flag = 2 means 'del rules'
fw_acl_load_rules () {
	local dos_defense_exit="1"
	fw add 4 f access_ctl
	fw_check "filter" "forwarding_rule" "-j dos_defense"
	[ x$? == x0 ] && {
		dos_defense_exit="0"
	}
	fw_check "filter" "forwarding_rule" "-j access_ctl"
	[ x$? == x0 ] && {
		iptables -w -A forwarding_rule -j access_ctl
	}
	
	dos_defense_exit="1"
	fw_check "filter" "input_rule" "-j dos_defense"
	[ x$? == x0 ] && {
		dos_defense_exit="0"
	}
	fw_check "filter" "input_rule" "-j access_ctl"
	[ x$? == x0 ] && {
		if [ ${dos_defense_exit} == "0" ]; then
			#echo "dos_defense rule doesn't exit in input_rule" > /dev/console
			fw add 4 f input_rule access_ctl 2
		else
			#echo "dos_defense rule exit in input_rule" > /dev/console
			fw add 4 f input_rule access_ctl 3
		fi
	}


	ACL_LOG "start load iptables"
	#config_foreach fw_acl_do_acl_btw_rule rule_acl_btw
	#config_load access_ctl
	fw_acl_do_acl_inner_rule rule_acl_inner 1
	lua /lib/access_ctl/attach_timeobj.lua
	#config_foreach fw_acl_do_acl_inner_rule rule_acl_inner 1
	#config_foreach config_clear
	ACL_LOG "stop"
}

fw_acl_clear()
{
	fw flush 4 f access_ctl
	fw_check "filter" "forwarding_rule" "-j access_ctl"
	while [ x$? == x1 ]; do
		fw del 4 f forwarding_rule access_ctl
	done
	
	fw_check "filter" "input_rule" "-j access_ctl"
	while [ x$? == x1 ];do
		fw del 4 f input_rule access_ctl
	done
	fw del 4 f access_ctl
	ACL_LOG "free access_ctl rule"
	fw_acl_do_acl_inner_rule rule_acl_inner 2
	ACL_LOG "free access_ctl rule stop"
	#config_load access_ctl
	#config_foreach fw_acl_do_acl_inner_rule rule_acl_inner 2
	#config_foreach config_clear
}


fw_acl_event_do_btw_interface()
{
	local iface=$2
	local action=$3
	local flag=""

	devices=$(zone_get_device_byif $iface)

	fw_acl_config_get_rule_acl_btw $1
	service_config_get $rule_acl_btw_service

	[ "$rule_acl_btw_flag" == "2" ] && {
		src_zone=${rule_acl_btw_zone%-*}
		dst_zone=${rule_acl_btw_zone#*-}
	}

	

	src_ifaces=$(zone_get_effect_ifaces $src_zone)
	
	for src_iface in $src_ifaces;do
		[ "$iface" == "$src_iface" ] && {
			flag=1
		}
	done
	
	[ "$flag" != "1" ] && {
		dst_ifaces=$(zone_get_effect_ifaces $dst_zone)
		for dst_iface in $dst_ifaces;do
			[ "$iface" == "$dst_iface" ] && {
				flag=1
			}
		done
	}
	

	[ "$flag" == 1 ] && {
		[ "$rule_acl_btw_flag" == "2" ] && {
			src_zone=${rule_acl_btw_zone%-*}
			dst_zone=${rule_acl_btw_zone#*-}
		}
	
		rule_acl_btw_policy=$(echo $rule_acl_btw_policy|tr 'a-z' 'A-Z')

		[ "$rule_acl_btw_flag" == "2" ] && {
			src_devices=$(zone_get_effect_devices "${src_zone}")
			dst_devices=$(zone_get_effect_devices "${dst_zone}")

			set_src_rule=""
			if [ $rule_acl_btw_src != "IPGROUP_ANY" ];then
				src_group=$(echo $rule_acl_btw_src |grep "REV")
				
				if [ -n "$src_group" ]; then
					rule_acl_btw_src=${rule_acl_btw_src%_*}
					
					fw_acl_ipset_check $rule_acl_btw_src
					[ x$? == x0 ] && {
						set_src_rule="-m set ! --match-set $rule_acl_btw_src src"
					} || return
					
						
				else
					fw_acl_ipset_check $rule_acl_btw_src
					[ x$? == x0 ] && {
						set_src_rule="-m set --match-set $rule_acl_btw_src src"
					} || return
					
				fi
			else
				set_src_rule=""		# IPGROUP_ANY
			fi
			#echo "set_src_rule=$set_src_rule"

			set_dst_rule=""
			if [ $rule_acl_btw_dest != "IPGROUP_ANY" ];then
				dst_group=$(echo $rule_acl_btw_dest |grep "REV")
				if [ -n "$dst_group" ]; then
					rule_acl_btw_dest=${rule_acl_btw_dest%_*}
					fw_acl_ipset_check $rule_acl_btw_dest
					[ x$? == x0 ] && {
						set_dst_rule="-m set ! --match-set $rule_acl_btw_dest dst"

					} || return
				else
					fw_acl_ipset_check $rule_acl_btw_dest
					[ x$? == x0 ] && {
						set_dst_rule="-m set --match-set $rule_acl_btw_dest dst"
					} || return
					
				fi
					
			else
				set_dst_rule=""
			fi
			#echo "set_dst_rule=$set_dst_rule"

			for src_device in $src_devices;do
				for dst_device in $dst_devices; do
					if [ "$service_name" == "ALL" ]; then

						local rule="-i $src_device -o $dst_device ""$set_src_rule"" ""$set_dst_rule"
						fw $action 4 f access_ctl ${rule_acl_btw_policy} $ { "$rule" }

					else
					
						if [ "$service_proto" != "icmp" ]; then
					
							service_sport=$(echo $service_sport|tr '-' ':')
							service_dport=$(echo $service_dport|tr '-' ':')

							for proto in $service_proto; do
								
								fw $action 4 f access_ctl ${rule_acl_btw_policy} $ \
									{ -i $src_device -o $dst_device -p $proto -m $proto --sport $service_sport \
									--dport $service_dport "$set_src_rule"" ""$set_dst_rule" }
								
							done
						
						else
							if [ "$service_name" == "ICMP_ALL" ]; then
								
								fw $action 4 f access_ctl ${rule_acl_btw_policy} $ \
									{ -i $src_device -o $dst_device -p $service_proto --icmp-type any \
									"$set_src_rule"" ""$set_dst_rule" }
								
							else
								fw $action 4 f access_ctl ${rule_acl_btw_policy} $ \
									{ -i $src_device -o $dst_device -p $service_proto -m $service_proto \
									--icmp-type $service_type/$service_code "$set_src_rule"" ""$set_dst_rule" }
								
							fi

						fi
				
					fi

				done

			done

		}

	}

}


fw_acl_event_interface()
{
	local iface=$1
	local action=$2

	
	[ -z "$iface" -o -z "$action" ] && {
		return
	}
	
	devices=$(zone_get_device_byif $iface)

	[ "del" == "$action" ] && {
		fw_acl_clear
		fw_acl_load_rules
		#config_foreach fw_acl_event_do_btw_interface rule_acl_btw $iface $action
	}

	[ "add" == "$action" ] && {
		fw_acl_clear
		fw_acl_load_rules
		# also can change fw_acl_event_do_btw_interface
		#config_foreach fw_acl_event_do_btw_interface rule_acl_btw $iface $action
	}
	
}


fw_acl_event_vpniface()
{
	local iface=$1
	local action=$2


	[ -z "$iface" -o -z "$action" ] && {
		return
	}

	devices=$(zone_get_device_byif $iface)

	[ "del" == "$action" ] && {
	#	config_foreach fw_acl_event_do_btw_interface rule_acl_btw $iface $action
		fw_acl_clear
		fw_acl_load_rules
	}

	[ "add" == "$action" ] && {
		fw_acl_clear
		fw_acl_load_rules
		# also can change fw_acl_event_do_btw_interface
		# config_foreach fw_acl_event_do_btw_interface rule_acl_btw $iface $action
	}
	
}


