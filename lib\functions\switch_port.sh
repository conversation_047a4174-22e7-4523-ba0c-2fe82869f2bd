#!/bin/sh /etc/rc.common

SFP0_IF_NAME="8"
SFP0_SWITCH_PORT=24
SFP1_IF_NAME="9"
SFP1_SWITCH_PORT=25

# 初始化光口速率
sfp_speed_init() {
	# Set ssdk sfp_instance(1/2)'s to force 10G (Avoid auto negotiation)
	echo 10000 > /sys/ssdk/sfp_instance1
	echo 10000 > /sys/ssdk/sfp_instance2

	#10GE1
	local speed=$(uci get network.${SFP0_IF_NAME}.speed)
	local serdes_media="fiber10g"

	if [ "$speed" == "1000" ]; then
		serdes_media="fiber1g"
	elif [ "$speed" == "2500" ]; then
		serdes_media="fiber2_5g"
	fi
	rtk_set_speed_sfp "${SFP0_SWITCH_PORT}" "$serdes_media"

	serdes_media="fiber10g"

	#10GE2
	speed=$(uci get network.${SFP1_IF_NAME}.speed)
	if [ "$speed" == "1000" ]; then
		serdes_media="fiber1g"
	elif [ "$speed" == "2500" ]; then
		serdes_media="fiber2_5g"
	fi
	rtk_set_speed_sfp "${SFP1_SWITCH_PORT}" "$serdes_media"
}

rtk_set_speed_sfp() {
	local port=$1
	local serdes_media=$2

	rtk_config_cmd "port set serdes-media port ${port} ${serdes_media}"
}

#RTK.0> port dump port 8
#Port  8 :
#        Media                   : Copper
#        Admin                   : Enable
#        Mac Tx                  : Enable
#        Mac Rx                  : Enable
#        Back Pressure           : Enable
#        Link                    : UP    Speed : 1000F
#        AutoNego                : Enable
#        AutoNego Ability        : 10H 10F 100H 100F 1000F Flow-Control
#        Giga-Lite               : Disable
#        Flow Control (actual)   : Enable
#        Cross Over Mode         : Auto MDI/MDIX

get_cur_speed_port() {
	local if_name=$1
	local port=""
	local port_list="8 9 10 11 12 13 14 15 24 25"
	local cur_if="MGMT"
	local speed="DOWN"

	for __port in $port_list; do
		[ "$cur_if" = "$if_name" ] && {
			port=$__port
			break
		}
		if [ "$cur_if" = "MGMT" ]; then
			cur_if=1
		else
			cur_if=$((cur_if+1))
		fi
	done

	[ -z "$port" ] && {
		echo "invalid if: $if_name" >> /dev/console
		echo "$speed"
		return
	}

	local link_str=$(echo -e "port dump port ${port}\n exit\n" | rtk-diag | grep -w "Link")
	[ -n "$link_str" ] && {
		echo "$link_str" | grep -wq "UP"
		[ $? -eq 0 ] && {
			speed=$(echo "$link_str" | sed -r 's/.*:\s([0-9]+([.]{1}[0-9]+){0,1}[GHF]).*/\1/')
			echo $speed
			return
		}
	}
	echo "$speed"
}

set_speed_sfp() {
	local if_name=$1
	local speed=$2
	local port=""
	local serdes_media="fiber10g"

	if [ $if_name == "${SFP0_IF_NAME}" ]; then
		port="${SFP0_SWITCH_PORT}"
	elif [ $if_name == "${SFP1_IF_NAME}" ]; then
		port="${SFP1_SWITCH_PORT}"
	else
		echo "invalid sfp port" >> /dev/console
		return
	fi

	if [ "$speed" == "1000" ]; then
		serdes_media="fiber1g"
	elif [ "$speed" == "2500" ]; then
		serdes_media="fiber2_5g"
	fi

	rtk_config_cmd "port set serdes-media port ${port} ${serdes_media}"
}

disable_ssdk_portLearn(){
	ssdk_sh fdb portLearn set 1 disable
	ssdk_sh fdb portLearn set 2 disable
	ssdk_sh fdb portLearn set 3 disable
	ssdk_sh fdb portLearn set 4 disable
	ssdk_sh fdb portLearn set 5 disable
	ssdk_sh fdb portLearn set 6 disable
}

enable_all_phy() {
	disable_ssdk_portLearn #禁用CPU直出口地址表
	sfp_speed_init

	rtk_switch_port_init
	rtk_enable_port 8
	rtk_enable_all_port
}

disable_all_phy() {
	rtk_disable_port 8
	rtk_disable_all_port
}

enable_phy_GE5() {
	disable_ssdk_portLearn #禁用CPU直出口地址表
	rtk_switch_port_init
	rtk_enable_port 8
	rtk_disable_all_port
}

rtk_config_cmd() {
	local cmd="$1"

	echo "$cmd
	exit" | rtk-diag > /dev/null
}

rtk_enable_port() {
	local port="$1"

	rtk_config_cmd "port set port ${port} state enable"
	rtk_config_cmd "bandwidth set ingress flow-control port ${port} state enable"
}

rtk_disable_port() {
	local port="$1"

	rtk_config_cmd "port set port ${port} state disable"
}

rtk_config_port_isolation() {
	local port="$1"
	local cpu_port="$2"

	rtk_config_cmd "port set isolation src-port ${port} dst-port ${cpu_port}"
}

rtk_enable_all_port() {
	rtk_enable_port 9
	rtk_enable_port 10
	rtk_enable_port 11
	rtk_enable_port 12
	rtk_enable_port 13
	rtk_enable_port 14
	rtk_enable_port 15
	rtk_enable_port 24
	rtk_enable_port 25
}

rtk_disable_all_port() {
	rtk_disable_port 9
	rtk_disable_port 10
	rtk_disable_port 11
	rtk_disable_port 12
	rtk_disable_port 13
	rtk_disable_port 14
	rtk_disable_port 15
	rtk_disable_port 24
	rtk_disable_port 25
}

#add default for switch port
rtk_config_dft_vlan() {
	local port="$1"
	local cpu_port="$2"
	local vlan_id="$3"

	#配置pvid
	rtk_config_cmd "vlan set pvid inner port ${port} ${vlan_id}"
	rtk_config_cmd "vlan set pvid outer port ${port} ${vlan_id}"
	#配置vlan
	rtk_config_cmd "vlan create vlan-table vid ${vlan_id}"
	rtk_config_cmd "vlan set vlan-table vid ${vlan_id} member ${port},${cpu_port}"
	rtk_config_cmd "vlan set vlan-table vid ${vlan_id} untag-port ${port}"
	#配置port vlan
	rtk_config_cmd "vlan set ingress port ${port} inner keep-tag state enable"
	rtk_config_cmd "vlan set ingress port ${port} outer keep-tag state disable"
	rtk_config_cmd "vlan set ingress port ${port} inner tpid 0x0"
	rtk_config_cmd "vlan set ingress port ${port} outer tpid 0x1"
	#配置double vlan
	rtk_config_cmd "vlan set protocol-double-vlan port ${port} group 0 vid ${vlan_id} state enable priority 0 state enable"
	#配置端口隔离
	#rtk_config_port_isolation ${port} ${cpu_port}

	# 禁用端口学习
	rtk_config_cmd "l2-table set limit-learning port ${port} 0x0"
	# 清空已经学习到的 MAC 转发表
	rtk_config_cmd "l2-table set flush flush-entry port ${port} include-static"
}

rtk_config_cpu_port() {
	local cpu_port="$1"

	#启用端口
	rtk_config_cmd "port set port ${cpu_port} state enable"

	rtk_config_cmd "vlan set ingress port ${cpu_port} inner keep-tag state enable"
	rtk_config_cmd "vlan set ingress port ${cpu_port} outer keep-tag state disable"
	rtk_config_cmd "vlan set ingress port ${cpu_port} inner tpid 0x1"
	rtk_config_cmd "vlan set ingress port ${cpu_port} outer tpid 0x1"

	rtk_config_cmd "vlan set egress port ${cpu_port} outer tpid 1"

	# 禁用端口学习
	rtk_config_cmd "l2-table set limit-learning port ${cpu_port} 0x0"
	# 清空已经学习到的 MAC 转发表
	rtk_config_cmd "l2-table set flush flush-entry port ${cpu_port} include-static"
}

rtk_switch_port_init() {
	#配置double vlan策略
	rtk_config_cmd "vlan set protocol-vlan group 0 frame-type ethernet frame-value 0x8100"
	#配置各个端口的默认配置
	rtk_config_dft_vlan 8 26 1000 #1,port8 eth0.1000
	rtk_config_dft_vlan 9 27 1001 #2,port9 eth1.1001
	rtk_config_dft_vlan 10 26 1002 #3,port10 eth0.1002
	rtk_config_dft_vlan 11 27 1003 #4,port11 eth1.1003
	rtk_config_dft_vlan 12 26 1004 #5,port12 eth0.1004
	rtk_config_dft_vlan 13 27 1005 #6,port13 eth1.1005
	rtk_config_dft_vlan 14 26 1006 #7,port14 eth0.1006
	rtk_config_dft_vlan 15 27 1007 #8,port15 eth1.1007
	rtk_config_dft_vlan 24 26 1008 #9,port24 eth0.1008
	rtk_config_dft_vlan 25 27 1009 #10,port25 eth1.1009
	#配置switch与cpu之间连接的port
	rtk_config_cpu_port 26
	rtk_config_cpu_port 27
	#link down时flush转发表
	rtk_config_cmd "l2-table set link-down-flush state enable"
}
