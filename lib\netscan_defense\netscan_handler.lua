--[[
Copyright(c) 2008-2014 Shenzhen TP-LINK Technologies Co.Ltd.

File    :  netscan_handler.lua
Details :  handler for netscan function
Author  :  chenxing_w8217
Version :  1.0.0
Date    :  13 June, 2019
]]--
local uci = require "luci.model.uci"
local sys = require "luci.sys"
local dbg = require "luci.tools.debug"
local err = require("luci.torchlight.error")

-- ipt executor
IPT_EXECUTOR = "/lib/fw_policy_manager/ipt_rule_executor.sh"
-- uci name(package)
UCI_FILE = "netscan_defense"
-- policy type name(table)
SECTION = "global"

function add_netscan_rules()
	local cmd = nil
	-- init the conf chain
	local uci_r = uci.cursor()
	local netscan_section = uci_r:get_all(UCI_FILE, SECTION)
	if nil == netscan_section then
		return false
	end

	local ipscan_def = netscan_section["ipscan_def"]
	local ipscan_rate = netscan_section["ipscan_rate"]
	local ipscan_ageing_time = netscan_section["ipscan_ageing_time"]

	local portscan_def = netscan_section["portscan_def"]
	local portscan_rate = netscan_section["portscan_rate"]
	local portscan_ageing_time = netscan_section["portscan_ageing_time"]

	if "1" == ipscan_def and "1" == portscan_def then
		cmd = string.format([[%s add_rule mangle netscan_defense "-m netscan --ipscan %s:%s --portscan %s:%s" "DROP" 0]], IPT_EXECUTOR,ipscan_rate,ipscan_ageing_time,portscan_rate,portscan_ageing_time)
		sys.fork_call(cmd)
	elseif "1" == ipscan_def then
		cmd = string.format([[%s add_rule mangle netscan_defense "-m netscan --ipscan %s:%s" "DROP" 0]], IPT_EXECUTOR,ipscan_rate,ipscan_ageing_time)
		sys.fork_call(cmd)
	elseif "1" == portscan_def then
		cmd = string.format([[%s add_rule mangle netscan_defense "-m netscan --portscan %s:%s" "DROP" 0]], IPT_EXECUTOR,portscan_rate,portscan_ageing_time)
		sys.fork_call(cmd)
	end
end

function del_netscan_rules()
	local cmd = nil
	cmd = string.format([[%s flush_subchain mangle netscan_defense]], IPT_EXECUTOR)
	sys.fork_call(cmd)
end

local cmd = nil
local operation = arg[1]
local ret = true

if nil == operation or "" == operation then
	dbg("params error!")
	return false
end

if operation == "start" then
	-- init the chain
	cmd = string.format([[%s add_subchain mangle netscan_defense]], IPT_EXECUTOR)
	sys.fork_call(cmd)
	-- add the chain to the PRE_ROUTING
	cmd = string.format([[%s add_rule mangle PREROUTING "-m conntrack ! --ctstate RELATED,ESTABLISHED" "netscan_defense" 0]], IPT_EXECUTOR)
	sys.fork_call(cmd)
	-- add rule in the netscan_defense chain
	ret = add_netscan_rules()
elseif operation == "stop" then
	-- flush and del the netscan_defense chain
	cmd = string.format([[%s del_subchain mangle netscan_defense]], IPT_EXECUTOR)
	sys.fork_call(cmd)
elseif operation == "reload" then
	del_netscan_rules()
	ret = add_netscan_rules()
end

return ret