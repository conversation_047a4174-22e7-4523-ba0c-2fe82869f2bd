#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"
local uci = require ("luci.model.uci")
local sys = require("luci.sys")

-- uci config_sync
local uci_system = {
    name = "system",
    snames = {
        {
            name = "sys",
            additional_fields = {
                has_set_pwd = function(index, entry)
                    local uci_r = uci:cursor()
                    -- 之前没有这个字段的话，字段取值取决于是否是出厂状态
                    if tostring(entry.is_factory) == "0" then
                        return "1"
                    else
                        return "0"
                    end
                end,
                apmng_detection = function(index, entry)
                    if tostring(entry.is_factory) == "0" then
                        return "off"
                    else
                        return "on"
                    end
                end
            },
            list_contains = {}
        }
    }
}

-- independence of frame should notify to save cfg itself
local function system_device_name_config()
	local is_save = false
	local uci_r = uci.cursor()
	local dev_alias  = uci_r:get("system", "sys", "dev_alias")
	if nil == dev_alias then
		-- 兼容已出货使用cloud_config.info.device_name的机型
		local old_device_name  = uci_r:get("cloud_config", "info", "device_name")
		if nil ~= old_device_name then
			uci_r:set("system", "sys", "dev_alias", tostring(old_device_name))
			uci_r:commit("system")
			uci_r:delete("cloud_config", "info", "device_name")
			uci_r:commit("cloud_config")
			is_save = true
		else
			local device_name=uci_r:get("device_info", "info", "device_name")
			if nil ~= device_name then
				uci_r:set("system", "sys", "dev_alias", tostring(device_name))
				uci_r:commit("system")
				is_save = true
			end
		end
	end
	if true == is_save then
		cfgsync.set_config_changed()
	end
end

-- independence of frame should notify to save cfg itself
local function system_bridge_set_enable_config()
	local is_save = false
	local uci_r = uci.cursor()
	local is_factory = uci_r:get("system", "sys", "is_factory")
	local bridge_set_enable = uci_r:get("system", "sys", "bridge_set_enable")
	if is_factory ~= nil and tostring(is_factory) == "0" and bridge_set_enable ~= nil and tostring(bridge_set_enable) == "1" then
		uci_r:set("system", "sys", "bridge_set_enable", "0")
		uci_r:commit("system")
		is_save = true
	end
	if true == is_save then
		cfgsync.set_config_changed()
	end
end

-- independence of frame should notify to save cfg itself
local function system_is_factory_config()
	local is_save = false
	local uci_r = uci.cursor()
	local has_set_pwd  = uci_r:get("system", "sys", "has_set_pwd")
	--没有出厂第一次登录页面强制修改密码的流程，无需此兼容
	local force_change_pwd = uci_r:get_profile("global", "force_change_pwd")
	if nil ~= has_set_pwd and  tostring(force_change_pwd) == "1" then
		-- 兼容已出货修改了用户名密码，但未修改is_factory字段
		local is_factory  = uci_r:get("system", "sys", "is_factory")
		if "1" == tostring(has_set_pwd)  and "1" == tostring(is_factory) then
			uci_r:set("system", "sys", "is_factory", "0")
			uci_r:commit("system")
			is_save = true
		end
	end
	if true == is_save then
		cfgsync.set_config_changed()
	end
end

system_device_name_config()
system_bridge_set_enable_config()
-- database config_sync
cfgsync.config_sync(uci_system, "configfile")
system_is_factory_config()
