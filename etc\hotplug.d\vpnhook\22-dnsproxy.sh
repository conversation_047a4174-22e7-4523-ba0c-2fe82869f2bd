#!/bin/sh

_delete_rule() {                                        
    local cfg="$1"                                     
    local iface="$2"                                                                                       
    local out_if   
	
    out_if=$(uci_get dns "$cfg" out_if)
    echo "cfg=$cfg,iface=$iface,out_if=$out_if"  >> /tmp/dnsproxy_wanhook.log    	
    [ "$out_if" == "$iface" ] && {      
        uci delete dns.$cfg
        uci_commit dns
    }                                                         
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
		    echo "interfaces=$interfaces" >> /tmp/dnsproxy_wanhook.log    
		    interfaces=${interfaces//,/ } 
			for element in $interfaces   
			do  
				echo "element=$element"  >> /tmp/dnsproxy_wanhook.log    
				config_load dns
				config_foreach _delete_rule rule $element
			done  
		}
	;;
	ADD)
	;;
	*)
	;;
esac