#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat.init
# brief
# author   <PERSON> chen
# version  1.0.0
# date     21Apr15
# histry   arg 1.0.0, 21Apr15, <PERSON> chen, Create the file.

. /lib/functions.sh
. /lib/nat/nat_public.sh

NAT_ALL_NETWORK=
NAT_ZONES=

#NAT_FILTER_CHAINS="zone_lan_natfd_vpn zone_lan_natfd_vs zone_lan_natfd_dmz forwarding_rule_pt "\
#"zone_wan1_natfd_vs zone_wan1_natfd_dmz zone_wan2_natfd_vs zone_wan2_natfd_dmz"
NAT_FILTER_CHAINS=

NAT_PREROUTNIG_RULE_CHAIN="prerouting_rule"
NAT_POSTROUTING_RULE_CHAIN="postrouting_rule"
NAT_DMZ_CHAINS="prerouting_rule_dmz"

NAT_PREROUTING_CHAINS="vs one_dmz dmz"
NAT_POSTROUTING_CHAINS="one_nat multi_nat vs"

nat_config_default() {

	# Prerouting nat rule chain order
	#config_get NAT_PREROUTING_CHAINS "default" "prerouting"
	#config_get NAT_POSTROUTING_CHAINS "default" "postrouting"
	export "nat_prerouting_chains"="$NAT_PREROUTING_CHAINS"
	export "nat_postrouting_chains"="$NAT_POSTROUTING_CHAINS"

	# constant chains
	export "nat_prerouting_rule_chain"="$NAT_PREROUTNIG_RULE_CHAIN"
	export "nat_postrouting_rule_chain"="$NAT_POSTROUTING_RULE_CHAIN"
	[ -z $PRODUCT_WVR450 ] || export "pt_filter_chain"="$NAT_PT_FILTER_CHAIN"

	# volatile chains
	export "nat_filter_chains"="$NAT_FILTER_CHAINS"
	export "nat_dmz_chains"="$NAT_DMZ_CHAINS"
}
