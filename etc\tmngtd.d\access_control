#!/bin/sh
. /lib/functions.sh
. /lib/zone/zone_api.sh
. /lib/firewall/fw.sh

cmd=$1
rulename="$2"


[ $cmd == "RESET" ] && return 0
shift
{
 # flock 6
case $cmd in
    *ACTIVE)         
	echo "ACTIVE" >/tmp/access_ctl/$rulename
    /lib/access_ctl/access_func.sh $rulename 
    ;;
    
    *EXPIRE)        
	 echo "EXPIRE" >/tmp/access_ctl/$rulename
    /lib/access_ctl/access_func.sh $rulename 0
    ;;
    
    *RESET)
        echo "reset evnet!"
    ;;   
      
esac 
} #6<>access_control