#!/bin/sh
# Copyright (C) 2006-2010 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

rootfs_pivot() {
	check_skip || jffs2_not_mounted || {
		main_version=`uname -r |awk -F'.' '{print $1}'`
		minor_version=`uname -r |awk -F'.' '{print $2}'`
		if [ ${main_version} -eq 3 -a ${minor_version} -ge 18 ]||[ ${main_version} -ge 3 ]; then
			mkdir -p /tmp/overlay/root /tmp/overlay/work
			mount -o move /tmp/overlay /overlay 2>&-
			fopivot /overlay/root /overlay/work /rom && pi_mount_skip_next=true
		else
			mount -o move /tmp/overlay /overlay 2>&-
			fopivot /overlay /rom && pi_mount_skip_next=true
		fi
		echo "0" > /tmp/jffs2_ready
	}
}

boot_hook_add preinit_mount_root rootfs_pivot

