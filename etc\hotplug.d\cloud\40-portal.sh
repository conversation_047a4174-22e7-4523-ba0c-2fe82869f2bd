#!/bin/sh
# hotplug event for ipgroup update

# record the log

mkdir -p "/tmp/portal"
local HOTP_LOG="/tmp/portal/cloud_hotplug.log"
[ ! -f $HOTP_LOG ] && echo -e "time \ttrigger \taction" > $HOTP_LOG
[ "`ls -l $HOTP_LOG | awk '{print $5}'`" -gt 100000 ] && echo -e "time \ttrigger \taction" > $HOTP_LOG

echo -e "`date +%Y%m%d-%H:%M:%S` $DOWNLOAD_URL $DOWNLOAD_PATH $DOWNLOAD_TYPE $DOWNLOAD_TEMPLETE" >> $HOTP_LOG

# response

#echo "HOTPLUG[CLOUD] URL is $DOWNLOAD_URL" > /dev/console
#echo "HOTPLUG[CLOUD] PATH is $DOWNLOAD_PATH" > /dev/console
#echo "HOTPLUG[CLOUD] TYPE is $DOWNLOAD_TYPE" > /dev/console
#echo "HOTPLUG[CLOUD] TEMPLATE is $DOWNLOAD_TEMPLETE" > /dev/console

if [ "$DOWNLOAD_TEMPLETE" == "" ];then
	[ ! -f /tmp/portal/.lock ] && mkdir -p /tmp/portal/ && touch /tmp/portal/.lock
	local PORTAL_CLOUD_LOCK="/tmp/portal/.lock"
	{
	    flock -w 10 25
	        (lua /usr/lib/lua/luci/model/cloud_template_handler.lua  "$DOWNLOAD_URL" $DOWNLOAD_PATH $DOWNLOAD_TYPE &)
	    flock -u 25
	} 25<> $PORTAL_CLOUD_LOCK
else
	bind_status=`uci get cloud_config.bind_info.bind_status`
	mngt_switch=`uci get cloud_config.conf_mngt.mngt_switch`

	#echo "bind_status is $bind_status" > /dev/console
	#echo "mngt_switch is $mngt_switch" > /dev/console

	if [ "$bind_status" == "1" -a "$mngt_switch" == "1" ];then
		echo ""
	else
		[ ! -f /tmp/portalTemplateList/result ] && mkdir -p  /tmp/portalTemplateList/ && ubus call cloudclient download_portal_object "{\"url_pre\":\"template\", \"url\":\"getPortalTemplateList\", \"path\":\"/tmp/portalTemplateList/result\", \"type\":\"json-string\"}"
	fi
fi


