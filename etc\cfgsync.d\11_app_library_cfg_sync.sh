#!/usr/bin/lua

--[[
1. 修改app_library对于smart_mode的设置接口，需要进行配置兼容
2. 增加应用特征库中内置应用组的废弃功能，进行如下配置兼容设计
   a. 如果security_policy中配置了应用组，且应用组的enable属性已经被置为0，新增内置应用组为同名自定义应用组，增加该组对应的应用
   b. 如果security_policy中配置了应用且配置路径为应用组，且应用组的enable属性已经被置为0，修改该路径为类别 - 子类别 - 应用（因为类别和子类别是应用必选的分组属性，适用于自定义应用和内置应用）
]]

local dbg 	  = require "luci.torchlight.debug"
local uci 	  = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"
local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()
local app_library = require "luci.controller.admin.app_library"
local group_id = 10001
local app_group_id_map = {}
local app_group_id_map_init = false

local function get_new_group_id()
    if app_group_id_map_init == false then
        -- 初始化自定义组id信息
        uci_r:load(conf_dir)

        uci_r.foreach("app_library", "app_grouping", function(section)
            if section["group_id"] then
                app_group_id_map[tonumber(section["group_id"])] = 1
            end
        end)
        app_group_id_map_init = true
    end

    while app_group_id_map[group_id] == 1 do
        group_id = group_id + 1
    end

    app_group_id_map[group_id] = 1

    return group_id
end

local function app_library_sync()
    local has_changed = 0
	uci_r:load(conf_dir)

    -- smart_mode配置兼容
    uci_r.foreach("app_library", "global", function(section)
        if section[".name"] == "global" then
            if section["is_smart_mode"] == '1' then
                has_changed = 1
                uci_r:delete("app_library", "global", "is_smart_mode")
                uci_r:set("app_library", "global", "smart_mode_enable", "on")
            elseif section["is_smart_mode"] == '0' then
                has_changed = 1
                uci_r:delete("app_library", "global", "is_smart_mode")
                uci_r:set("app_library", "global", "smart_mode_enable", "off")
            end
        end
    end)

    -- 内置应用组的废弃配置兼容
    local catalogue_table = app_library._read_db_catalogue_list(nil, true)
    local _, app_table, _ = app_library.get_app_list()
    local catalogue_map = {}
    local app_map = {}
    local app_group_add = {}

    -- 初始化catalogue信息map
    for _, section in pairs(catalogue_table) do
        catalogue_map[tonumber(section["id"])] = section
    end

    -- 初始化应用信息map
    for _, t in pairs(app_table) do
        for _, section in pairs(t) do
            app_map[tonumber(section["app_id"])] = section
        end
    end

    uci_r.foreach("security_policy", "sec_policy", function(section)
        local new_app_groups = {}
        local mark = 0

        if section["app_groups"] == nil or section["app_groups"] == "" then
            return
        end

        for _, line in pairs(section["app_groups"]) do
            local find = string.find(line, "]")
            if find == nil then
                dbg("Error: app_groups format error")
                return
            end

            local catalogue = string.sub(line, 2, find - 1)
            local apps = string.sub(line, find + 1, line.length)

            local paras = {}
            string.gsub(catalogue,"([^/]+)",function (p)
                table.insert(paras, p)
            end)

            -- 只看第二个catalogue，第一个catalogue为软件/类别，当类别被删除时，子类别也应当会被删除
            if #paras == 2 then
                catalogue = tonumber(paras[2])
            else
                catalogue = tonumber(catalogue)
            end

            if catalogue_map[catalogue] ~= nil and catalogue_map[catalogue]["enable"] == "0" then
                mark = 1
                if apps == "" or apps == nil then
                    -- 选择了组
                    if app_group_add[catalogue] == nil then
                        -- 新建自定义应用组
                        local app_id_list = app_library._read_req_catalogue_delete("group_id = " .. catalogue)
                        if app_id_list and #app_id_list ~= 0 then
                            local new_group = {}
                            new_group["app_id_list"] = app_id_list
                            new_group["name"] = catalogue_map[catalogue]["name"]
                            new_group["ref"] = 1
                            new_group["group_id"] = get_new_group_id()

                            uci_r:section("app_library","app_grouping","app_grouping_" .. new_group["group_id"], new_group)

                            app_group_add[catalogue] = new_group["group_id"]
                            new_app_groups[#new_app_groups + 1] = "[" .. new_group["group_id"] .. "]"
                        end
                    else
                        local sec_name = "app_grouping_" .. app_group_add[catalogue]
                        local ref = uci_r:get("app_library", sec_name , "ref")
                        uci_r:set("app_library", sec_name, "ref", tonumber(ref) + 1)

                        new_app_groups[#new_app_groups + 1] = "[" .. app_group_add[catalogue] .. "]"
                    end
                else
                    -- 选择了应用
                    string.gsub(apps,"([^,]+)",function (id)
                        local app = app_map[tonumber(id)]
                        if app and app["usage"] and app["sub_usage"] then
                            new_app_groups[#new_app_groups + 1] = "[" .. app["usage"] .. "/" .. app["sub_usage"] .. "]" .. id
                        end
                    end)
                end
            end
        end

        if mark == 1 then
            uci_r:delete("security_policy", section[".name"], "app_groups")
            uci_r:set("security_policy", section[".name"], "app_groups", new_app_groups)
            has_changed = 1
        end
    end)

    if has_changed == 1 then
     	uci_r:commit("app_library")
     	uci_r:commit("security_policy")
     	cfgsync.set_config_changed()
    end

	return
end

app_library_sync()