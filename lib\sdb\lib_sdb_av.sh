#! /bin/sh
#
# lib_sdb_av.sh
# author : f<PERSON><PERSON><PERSON>@tp-link.com.cn
# date   : 2021/5/25

. /lib/sdb/dbg.sh

# purpose: 读取特征库管理模块解包后的数据，将其导入到提交目录中
# call   : 当特征库从签名库导出后，会调用该接口对特征库数据进一步处理
#           从而得到CSE可用的数据
prepare_commit(){
    local dbname="av"
    local dataDir="$1"
    local commitDir="$2"
    if [ -z "$dataDir" ] || [ -z "$commitDir" ]; then
        dbg_error "param error: lack dataDir or commitDir"
        dbg_echo  "Usage : $SHELL_NAME prepare_commit dataDir commitDir"
        return 1
    fi

    local spec_num="$(. /lib/license/license_api.sh && license_get_spec_num ${dbname})"
    if [ -n "$spec_num" ]; then
        min_avid=$(av_database -f ${dataDir} -n ${spec_num})
        echo -e "config antivirus: \\" > ${commitDir}/av.conf
        echo -e "   effective_min_avid ${min_avid}" >> ${commitDir}/av.conf
    else
        echo -e "config antivirus: \\" > ${commitDir}/av.conf
        echo -e "   effective_min_avid 0" >> ${commitDir}/av.conf
    fi

    # 将.bin文件拷贝至提交目录
    mv ${dataDir}/*.bin ${commitDir}/
}

# purpose: 将提交目录和CSE中的数据清除
# call   : 当不需要该库时，会调用该接口清除commit目录下的文件，去除
#           CSE中该特征库数据
restore_factory_commit(){
    local commitDir="$1"
    if [ -z "$commitDir" ]; then
        dbg_error "param error: lack commitDir"
        return 1
    fi

    echo \# > ${commitDir}/av_md5.bin
    echo \# > ${commitDir}/av_pe_md5.bin
    echo \# > ${commitDir}/av_sha2.bin
}

# purpose : 用于提交后，将占用内存较大的文件删除，减少内存占用
#           内存占用较小，可以不做处理
clean_after_commit(){
    local commitDir="$1"
    if [ -z "$commitDir" ]; then
        dbg_error "param error: lack commitDir"
        return 1
    fi

    rm -f ${commitDir}/av_md5.bin
    rm -f ${commitDir}/av_pe_md5.bin
    rm -f ${commitDir}/av_sha2.bin
}

case "$1" in
prepare)
    prepare_commit "$2" "$3"
    ;;
restore)
    restore_factory_commit "$2"
    ;;
clean)
    clean_after_commit "$2"
    ;;
esac

exit "$?"
