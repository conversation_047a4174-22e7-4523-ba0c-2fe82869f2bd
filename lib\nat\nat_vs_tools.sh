#!/bin/sh /etc/rc.common
# 虚拟服务器的iptables命令会多一些参数，不太适合放在标准的NAT的iptables命令中


IPT4="iptables -w -t nat"
IPT6="ip6tables -w -t nat"

chain_name_pre="prerouting_rule_vs"
chain_name_post="postrouting_rule_vs"

vs_ipset_name_file="/tmp/vs_ipset_v4.name"
vs_ipset_name_file_v4="/tmp/vs_ipset_v4.name"
vs_ipset_name_file_v6="/tmp/vs_ipset_v6.name"

vs_add_loopback_snat()
{
    local ipset_name=$1
	local loopback_ipaddr_slots=$2
	if [ "${loopback_ipaddr_slots}" == "---" ]; then
		return
	fi
	local array=${loopback_ipaddr_slots//,/ }
	local var
	for var in ${array}
	do
		ipset add ${ipset_name} ${var} -exist
	done
}

vs_add_mul_lan_snat()
{
	. /lib/zone/zone_api.sh
	. /lib/firewall/fw.sh
	local ipset_name=$1
	local proto=$2
	#multi lan
	local lan_ifaces=$(zone_get_normal_ifaces)
	append lan_ifaces "lan"
	local lan_iface
	local lan_ipaddr
	local lan_netmask
	local prefix
	local subnet
	local lan_ip6ifaceid
	local lan_ip6addr
	for lan_iface in $lan_ifaces
	do
		case $proto in
		IPv4)
		lan_ipaddr=$(uci get network.$lan_iface.ipaddr)
		lan_netmask=$(uci get network.$lan_iface.netmask)
		if [ "$lan_ipaddr" != "" -a "$lan_netmask" != "" ]; then
			prefix=$(ipcalc -p $lan_ipaddr $lan_netmask)
			prefix=${prefix#*=}
			subnet=$(ipcalc -n $lan_ipaddr $lan_netmask)
			subnet=${subnet#*=}
			ipset add ${ipset_name} ${subnet}/${prefix} -exist
		fi
		;;
		IPv6)
		lan_ip6ifaceid=$(uci get network.$lan_iface.ip6ifaceid)
		if [ "$lan_ip6ifaceid" == "manual" ]; then
			lan_ip6addr=$(uci get network.$lan_iface.ip6addr)
		elif [ "$lan_ip6ifaceid" == "eui64" ] ; then
			lan_ip6addr=$(uci get network.$lan_iface.ip6prefix)
		fi
		[ -n "$lan_ip6addr" ] && ipset add ${ipset_name} ${lan_ip6addr} -exist
		;;
		esac
	done
}

vs_rule_creat_white()
{
	local name=$1
	local ip_proto=$2
	local dest_ip=$3
	local dest_port=$4
	local proto=$5

	case $ip_proto in
	IPv4)
	# do nothing
	;;
	IPv6)
	local chain_exist=`ip6tables -w -t filter -nvL | grep -w ipv6_vs_forward`
	[ -z "$chain_exist" ] && {
		fw add 6 f ipv6_vs_forward
		ip6tables -w -t filter -I forwarding_rule -j ipv6_vs_forward
	}
	local dest_port_form2=${dest_port//-/:}
	for var in $proto
	do
		fw del 6 f ipv6_vs_forward ACCEPT { -d $dest_ip -p $var -m multiport --dports ${dest_port_form2} -m comment --comment $name }
		fw add 6 f ipv6_vs_forward ACCEPT { -d $dest_ip -p $var -m multiport --dports ${dest_port_form2} -m comment --comment $name }
	done
	;;
	esac

}

vs_rule_destroy_white()
{
	local rule_name=$1
	local ip_proto=$2
	local chain_name="ipv6_vs_forward"

	case $ip_proto in
	IPv4)
	# do nothing
	;;
	IPv6)
	#find the number of the rule
	local rule_num=`ip6tables -w -t filter -nv --line-number -L ${chain_name} | grep -w "\/\* ${rule_name} \*\/" | sort -r -n | cut -d " " -f 1`

	for nums in $rule_num
	do
		ip6tables -w -t filter -D ${chain_name} ${nums}
	done
	;;
	esac
}

vs_rule_create_ipset()
{
	local name=$1
	local proto=$2

	case $2 in
	IPv4)
	ipset create ${name} hash:net -exist
	;;
	IPv6)
	ipset create ${name} hash:net family inet6 -exist
	;;
	esac

	ipset flush ${name}
}

vs_rule_destroy_ipset()
{
	local name=$1
	ipset destroy $1
}