#!/usr/bin/env lua
local csc = require("luci.torchlight.config_sync")
local dbg = require("luci.torchlight.debug")
local uci = require("luci.model.uci")
local sys = require("luci.sys")

local UCI_PATH       = "/etc/config/"
local DBG_PREFIX     = "[cfgsync/alarm] "

local uci_rw     = uci:cursor()
local sec_entry = nil
local level_map = {
    ["Emergency"]     = "0",
    ["Alert"]         = "1",
    ["Critical"]      = "2",
    ["Error"]         = "3",
    ["Warning"]       = "4",
    ["Notification"]  = "5",
    ["Informational"] = "6",
    ["Debugging"]     = "7"
}
local tls_map = {
    ["on"]  = "starttls",
    ["off"] = "tls"
}
local isChanged =false

-- Step 1: Prepare before sync
uci_rw:set_confdir(UCI_PATH)

-- Step 2: Sync 'event' section
sec_entry = uci_rw:get_all("alarm", "event")

-- Remove option no longer needed.
if sec_entry["module_switch"] then
    isChanged = true
    uci_rw:delete("alarm", "event", "module_switch")
end
if sec_entry["level_switch"] then
    isChanged = true
    uci_rw:delete("alarm", "event", "level_switch")
end

-- Rename "_switch" to "_enable".
if sec_entry["alarm_switch"] then
    isChanged = true
    uci_rw:delete("alarm", "event", "alarm_switch")
    uci_rw:set("alarm", "event", "alarm_enable", sec_entry["alarm_switch"] or "on")
end

-- Adjust values of list option 'levels' to new standard.
local new_levels = {}
for _, v in ipairs(sec_entry["levels"]) do
    if "ALL" == v then
        new_levels = {"0","1","2","3","4","5","6","7"}
        break
    end

    table.insert(new_levels,level_map[v])
end
if 0 < #new_levels then
    isChanged = true
    uci_rw:set("alarm", "event", "levels", new_levels)
end

-- Step 3: Sync 'email' section
sec_entry = uci_rw:get_all("alarm", "email")

-- Rename "_switch" to "_enable"
if sec_entry["email_switch"] then
    isChanged = true
    uci_rw:delete("alarm", "email", "email_switch")
    uci_rw:set("alarm", "email", "email_enable", sec_entry["email_switch"] or "off")
end
if sec_entry["auth_switch"] then
    isChanged = true
    uci_rw:delete("alarm", "email", "auth_switch")
    uci_rw:set("alarm", "email", "auth_enable", sec_entry["auth_switch"] or "off")
end

-- Correct mis-spelling of "receiver"
if sec_entry["reciever"] then
    isChanged = true
    uci_rw:delete("alarm", "email", "reciever")
    uci_rw:set("alarm", "email", "receiver", sec_entry["reciever"])
end

-- Add "encrypt_type" if not_exisit or rename "starttls" to "encrypt_type"
if sec_entry["starttls"] then
    isChanged = true
    uci_rw:delete("alarm", "email", "starttls")
    uci_rw:set("alarm", "email", "encrypt_type", tls_map[sec_entry["starttls"]] or "starttls")
elseif not sec_entry["encrypt_type"] then
    isChanged = true
    uci_rw:set("alarm", "email", "encrypt_type", "starttls")
end

-- Step 4: Sync finish, clean up.
if isChanged then
    --dbg(DBG_PREFIX.."UCI needs sync")
    -- Commit changes
    if not uci_rw:commit("alarm") then
        dbg(DBG_PREFIX.."Failed to commit change")
    end

    csc.set_config_changed()
end
