#!/bin/sh
#
# Copyright (c) 2015, 2019 The Linux Foundation. All rights reserved.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
# OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
#

[ -e /etc/config/skb_recycler ] && exit 0

touch /etc/config/skb_recycler

. /lib/functions/uci-defaults.sh
. /lib/ipq806x.sh

ucidef_set_skb_recycler

board=$(ipq806x_board_name)
max_skbs=1024
max_spare_skbs=256
memtotal=$(grep MemTotal /proc/meminfo | awk '{print $2}')
[ $memtotal -lt 131072 ] && max_skbs=512

[ -e /proc/device-tree/MP_512 ] && max_skbs=512 && max_spare_skbs=128

case "$board" in
*)
	ucidef_set_skb "max_skbs" $max_skbs
	ucidef_set_skb "max_spare_skbs" $max_spare_skbs
	;;
esac

uci commit skb_recycler

exit 0
