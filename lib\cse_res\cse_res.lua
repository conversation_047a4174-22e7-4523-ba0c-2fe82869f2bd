local dbg = require "luci.tools.debug"
local sys = require("luci.torchlight.sys")
local sqlite = require ("luci.torchlight.uac.firewall_sqlApi")
local socket = require("socket")

local MD5_TABLE = "md5"
local URL_TABLE = "url"
local MD5_GET_CMD = "dmesg | grep -o cse_res_md5:.* | awk '{print $2}'"
local URL_GET_CMD = "dmesg | grep -o cse_res_url:.* | awk '{print $2}'"
local dbenv, dbcon

local Usage = string.format([[
    Usage: lua /lib/cse_res/cse_res.lua OPTIONS [DB_PATH] [INTERVAL] [MD5] [URL] &
    OPTIONS include:
        start       start collect cse result
        stop        stop collect cse result
        dump        dump md5/url collected
    DB_PATH is the name an SQLite database (default /tmp/cse_res.db)
    INTERVAL is the interval second this script visit dmesg (default 1)
    MD5 is the switch to collect/dump md5, 0 or 1 (default 1)
    URL is the switch to collect/dump url, 0 or 1 (default 1)
    examples:
        1)lua /lib/cse_res/cse_res.lua start /tmp/cse_res.db 1 1 1 &
        2)lua /lib/cse_res/cse_res.lua stop
        3)lua /lib/cse_res/cse_res.lua dump /tmp/cse_res.db 0 1 0
    ]])

local function get_cmd_res(cmd)
    local res_list = {}
    for ln in io.popen(cmd, 'r'):lines() do
        res_list[#res_list+1] = tostring(ln)
    end
    return res_list
end

local function init_database(database_path)
    local md5_create_table = string.format([[CREATE TABLE %s(
        md5                 VARCHAR PRIMARY KEY
        )]], MD5_TABLE)
    local url_create_table = string.format([[CREATE TABLE %s(
        url                 VARCHAR PRIMARY KEY
        )]], URL_TABLE)
    sys.fork_call("touch "..database_path)
    dbenv, dbcon = sqlite.open_sqlite(database_path)
    sqlite.create_table(dbcon, MD5_TABLE, md5_create_table)
    sqlite.create_table(dbcon, URL_TABLE, url_create_table)
end

local function append_val_to_db(interval, md5_on, url_on)
    while 1 do
        if 1 == md5_on then
            local md5_list = get_cmd_res(MD5_GET_CMD)
            for idx, md5 in pairs(md5_list) do
                if md5 and '' ~= md5 then
                    local md5_search = sqlite.select_all_by_key(dbcon, MD5_TABLE, "md5='"..md5.."'")
                    if md5_search == {} or md5_search == nil or #md5_search < 1 then
                        sqlite.insert(dbcon, MD5_TABLE, nil, "'"..md5.."'")
                    end
                end
            end
        end
        if 1 == url_on then
            local url_list = get_cmd_res(URL_GET_CMD)
            for idx, url in pairs(url_list) do
                if url and '' ~= url then
                    local url_search = sqlite.select_all_by_key(dbcon, URL_TABLE, "url='"..url.."'")
                    if url_search == {} or url_search == nil or #url_search < 1 then
                        sqlite.insert(dbcon, URL_TABLE, nil, "'"..url.."'")
                    end
                end
            end
        end
        socket.sleep(interval)
    end
end

local function dump_table(tableName)
    local res = sqlite.select_all(dbcon, tableName)
    dbg(tableName.." has "..#res.." elements")
    dbg(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    for idx, ele in pairs(res) do
        for col, col_ele in pairs(ele) do
            dbg(col_ele)
        end
    end
    dbg("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
end

local op = arg[1]
local db_path = arg[2] or "/tmp/cse_res.db"
local process_interval = arg[3] or 1
local md5_record = arg[4] or 1
local url_record = arg[5] or 1

if #arg < 1 or #arg > 5 then
    dbg(Usage)
else
    process_interval = tonumber(process_interval)
    md5_record = tonumber(md5_record)
    url_record = tonumber(url_record)
    if process_interval == nil or md5_record == nil or url_record == nil then
        dbg(Usage)
        os.exit()
    end
    if "stop" == op then
        dbg("stop collecting cse result")
        sys.fork_exec("echo 0 > /proc/policy/cse_res_dump;")
        sys.fork_exec("pgrep -f cse_res.lua | xargs kill;")
        sqlite.close_sqlite(dbenv, dncon)
    elseif "start" == op then
        if db_path == nil or db_path == "" or process_interval == nil then
            dbg(Usage)
        else
            dbg("start collecting cse result, DB_PATH:"..db_path.." INTERVAL:"..process_interval.." RECORD-MD5:"..md5_record.." RECORD-URL:"..url_record)
            sys.fork_call("echo 1 > /proc/policy/cse_res_dump;")
            init_database(db_path)
            append_val_to_db(process_interval, md5_record, url_record)
        end
    elseif "dump" == op then
        init_database(db_path)
        if md5_record == 1 then
            dump_table(MD5_TABLE)
        end
        if url_record == 1 then
            dump_table(URL_TABLE)
        end
    else
        dbg(Usage)
    end
end