#!/bin/sh /etc/rc.common

START=20

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1


start() {

	lua /lib/network/network_ipv6_generate.lua

	[ -e /proc/sys/kernel/core_pattern ] && {
		ulimit -c unlimited
		echo '/tmp/%e.%p.%s.%t.core' > /proc/sys/kernel/core_pattern
	}

	service_start /sbin/netifd

	setup_switch() { return 0; }

	include /lib/network
	setup_switch

	ubus call tddpServer start
}

restart() {
	stop
	start
}

shutdown() {
	stop
}

stop() {
	local pid=$(pidof netifd)

	ifdown -a
	sleep 10
	if [ x"$pid" != x ]; then
		sleep 1
	fi

	service_stop /sbin/netifd

}

reload() {
	/etc/init.d/zone restart

	ubus call network reload
}
