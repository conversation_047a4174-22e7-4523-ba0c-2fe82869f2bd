module("luci.sgi.cgi", package.seeall)
local i = require("luci.ltn12")
local l = require("nixio")
require("luci.dispatcher")
local function o(t, e)
    e = e or 0
    local n = i.BLOCKSIZE
    return function()
        if e < 1 then
            t:close()
            return nil
        else
            local n = (e > n) and n or e
            e = e - n
            local e = t:read(n)
            if not e then t:close() end
            return e
        end
    end
end
function run()
    local t = luci.http.Request(l.getenv(), o(io.stdin, tonumber(
                                                  l.getenv("CONTENT_LENGTH"))),
                                i.sink.file(io.stderr))
    local e = coroutine.create(luci.dispatcher.httpdispatch) -- 协程
    local n = ""
    local o = true
    while coroutine.status(e) ~= "dead" do
        local r, e, t, i = coroutine.resume(e, t) --- resume 协程 httpdispatch 执行
        if not r then
            print("Status: 500 Internal Server Error")
            print("Content-Type: text/plain\n")
            print(e)
            break
        end
        if o then
            if e == 1 then
                io.write("Status: " .. tostring(t) .. " " .. i .. "\r\n")
            elseif e == 2 then
                n = n .. t .. ": " .. i .. "\r\n"
            elseif e == 3 then
                io.write(n)
                io.write("\r\n")
            elseif e == 4 then
                io.write(tostring(t or ""))
            elseif e == 5 then
                io.flush()
                io.close()
                o = false
            elseif e == 6 then
                t:copyz(l.stdout, i)
                t:close()
            end
        end
    end
end
