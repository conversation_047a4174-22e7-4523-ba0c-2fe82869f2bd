#!/bin/sh

_delete_rule()
{
	external_ifaces=`uci get upnpd.config.external_iface`
	for iface in $external_ifaces
	do
		if [ $iface == "$1" ];then
			uci del_list upnpd.config.external_iface=$iface
			uci_commit upnpd
		fi
	done
}

state=`uci get upnpd.config.enable_upnp 2>/dev/null`
[ "$state" == "on" ] || return

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _delete_rule $element
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac