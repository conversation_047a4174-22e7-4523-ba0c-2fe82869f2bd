#!/bin/sh

get_inface_name()
{
	config_get userif $1 if
	append userifs $userif
}

check_inface_reset_static_route()
{
	local userifs

	config_load network

	config_foreach get_inface_name user_route

	[ -n "$userifs" ] || return

	for userif in $userifs;do
		if [ "$userif" = $USERIF ];then
			/etc/init.d/static_route restart
			return
		fi
	done
}

. /lib/zone/zone_api.sh
. /lib/route/route_api.sh

case "$ACTION" in
		ifup)

			USERIF=`zone_get_userif_bydev $DEVICE`
			[ -z "$USERIF" -o "$DEVICE" = "lo" ] && return

			check_inface_reset_static_route

		;;
		ifdown)

			USERIF=`zone_get_userif_bydev $DEVICE`
			[ -z "$USERIF" -o "$DEVICE" = "lo" ] && return

			check_inface_reset_static_route
		;;
		ifupdate)
			# do nothing
		;;
esac



