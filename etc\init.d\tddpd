#!/bin/sh /etc/rc.common
# Copyright (C) 2014 Fang Zhao

START=15

TDDP_BIN="/bin/tddpd"

setup() {
	#Setup /tmp/rsa_check/rsa_result
	mkdir -p /tmp/rsa_check/
	if [ ! -e /proc/flash_verify_result ]; then
		echo "flash_verify_result don't exist" > /dev/console
		echo "FAIL" > /tmp/rsa_check/rsa_result
	else
		rsa=`cat /proc/flash_verify_result`
		if [ $rsa -eq 0 ]; then
			echo "PASS" > /tmp/rsa_check/rsa_result
		else
			echo "FAIL" > /tmp/rsa_check/rsa_result
		fi
	fi
}

start() {
	setup

	service_start $TDDP_BIN
}

stop() {
	service_stop $TDDP_BIN
}

reload() {
	#/*!<为避免TCP连接断开，延时1s后再创建桥  */
		#tddpd_helper.lua会判断是否需要创建网桥
	{
		sleep 1
		#recheck sign
		echo "invalidate" > /proc/flash_verify_result
		setup
		rsa=`cat /proc/flash_verify_result`
		if [ $rsa -eq 0 ]; then
			#Enable switch & phy
			[ -f /etc/init.d/phy_ctl ] && /etc/init.d/phy_ctl start
			[ -f /etc/init.d/switch ] && /etc/init.d/switch start
		fi

		[ -f /usr/lib/lua/tddpd/tddpd_helper.lua ] && lua /usr/lib/lua/tddpd/tddpd_helper.lua
	} &
}
