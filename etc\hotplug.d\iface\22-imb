#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

[ "$DEVICE" == "lo" ] && exit 0

. /lib/functions.sh
. /lib/zone/zone_api.sh

IMB_PREFIX="imb_ipmac_"
IP_PREFIX="imb_ip_"

local USERIF=$(zone_iface_to_userif $INTERFACE)
local interfaces=$(uci get arp_defense.global.interface)
case "$ACTION" in
	ifup)
		[ ! -e /tmp/.imb.ready ] && return
		echo "$interfaces" | grep -wq "$USERIF" || return
		/lib/imb/delete_restart.sh &

	;;
	ifdown)
		[ ! -e /tmp/.imb.ready ] && return
		echo "$interfaces" | grep -wq "$USERIF" || return
		/lib/imb/delete_restart.sh &
	
	;;
	ifupdate)
		# do nothing
	;;
esac
