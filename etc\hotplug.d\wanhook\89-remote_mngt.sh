#!/bin/sh

. /lib/functions.sh

# 本来 mngt_iface section 是没有名字的，但为了方便操作，这里加上
_create_rule() {
    local iface=$1

    echo "
        set remote_mngt.$1=mngt_iface
        set remote_mngt.$1.iface=$1
        set remote_mngt.$1.enabled=off
    " | uci batch
}

# 删除时则要考虑 section 没有名字的情况
_delete_rule() {
    local sect_name=$1
    local sect_iface
    local iface=$2

    config_get sect_iface "$sect_name" iface
    [ "$iface" = "$sect_iface" ] && {
        uci delete remote_mngt."$sect_name"
    }
}

case ${ACTION} in
    DELETE)
        [ -n "${interfaces}" ] && {
            echo "interfaces=$interfaces" >> /tmp/remote_mngt_wanhook.log
            interfaces=${interfaces//,/ }

            config_load remote_mngt
            for iface in $interfaces; do
                config_foreach _delete_rule mngt_iface $iface
            done
            uci commit remote_mngt

            /etc/init.d/remote_mngt reload
        }
    ;;
    ADD)
        [ -n "${interfaces}" ] && {
            echo "interfaces=$interfaces" >> /tmp/remote_mngt_wanhook.log
            interfaces=${interfaces//,/ }

            for iface in $interfaces; do
                _create_rule $iface
            done
            uci commit remote_mngt

            /etc/init.d/remote_mngt reload
        }
    ;;
    WANMOD)
        [ -n "${interfaces}" ] && {
            echo "interfaces=$interfaces" >> /tmp/remote_mngt_wanhook.log
            /etc/init.d/remote_mngt reload
        }
    ;;
    *)
    ;;
esac

