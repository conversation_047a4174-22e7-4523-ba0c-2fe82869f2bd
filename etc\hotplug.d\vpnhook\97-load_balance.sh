#!/bin/sh

_delete_rule()
{
    infaces=`uci get load_balance.basic.use_if`
    for iface in $infaces
	do
		if [ $iface == "$1" ];then
			uci del_list load_balance.basic.use_if=$iface
			uci_commit load_balance
			break
		fi
	done

	uci delete mwan3.$1
	uci_commit mwan3
}

_add_rule()
{
	ret=`uci get mwan3.$1 2>&1`
	if [ "$ret" != "if" ];then
		uci set mwan3.$1=if
		uci set mwan3.$1.enabled=1
		uci set mwan3.$1.balance=1
		uci set mwan3.$1.ref=0

		uci_commit mwan3
		fi
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
		    interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _delete_rule $element
			done
		}
	;;
	ADD)
		[ -n "${interfaces}" ] && {
		    interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _add_rule $element
			done
		}
	;;
	*)
	;;
esac