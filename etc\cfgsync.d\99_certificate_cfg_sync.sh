#!/usr/bin/env lua
local csc = require("luci.torchlight.config_sync")
local dbg = require("luci.torchlight.debug")
local uci = require("luci.model.uci")
local sys = require("luci.sys")

local UCI_PATH       = "/etc/config/"
local DBG_PREFIX     = "[cfgsync/certificate] "

local uci_r     = uci:cursor()
local uci_w     = uci:cursor()
local sec_entry = nil
local isChanged = false

-- Step 1: Prepare before sync.
uci_r:set_confdir(UCI_PATH)
uci_w:set_confdir(UCI_PATH)

-- Step 2: Sync 'cert_upload'.
-- This section is only needed while uploading a cert, we can safely delete it.
sec_entry = uci_r:get_all("certificate", "cert_upload")
if sec_entry then
    isChanged = true
    uci_w:delete("certificate", "cert_upload")
end

-- Step 3: Sync certificates' UCI
uci_r:foreach("certificate", "server_ca_cert", function(section)
    local new_sec = {}

    isChanged = true
    new_sec["name"] = section["name"]
    new_sec["file_path"] = section["file_path"]
    uci_w:section("certificate", "ca_cert", string.gsub(section[".name"], "^ssl_cert", "ca_cert"), new_sec)
    uci_w:delete("certificate", section[".name"])
end)

-- Step 4: Sync finished, clean up.
if isChanged then
    --dbg(DBG_PREFIX.."UCI needs sync")
    -- Commit changes
    if not uci_w:commit("certificate") then
        dbg(DBG_PREFIX.."Failed to commit change")
    end

    csc.set_config_changed()
end
