#!/usr/bin/lua
local dbg 	  = require "luci.torchlight.debug"
local cfgsync = require "luci.torchlight.config_sync"
local uci 	  = require "luci.model.uci"
local conf_dir = "/tmp/etc/uc_conf"
local cjson_api = require "cjson"

--将old_para设置为new_para, 并删除原本old_para
function changePara(conf_section, old_para, new_para)
    local option = conf_section[old_para]
    local has_changed = false
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    if option ~= nil then
        uci_r:set("ips", conf_section[".name"], new_para, conf_section[old_para])
        uci_r:delete("ips", conf_section[".name"], old_para)

        has_changed = true
    else
        has_changed = false
    end

    if has_changed == true then
        uci_r:commit("ips")
        cfgsync.set_config_changed()
    end
    return
end

--原type改为flag 取值范围"system","user",default_value为即无type值也无flag值的配置设置默认flag值
function addFlag(conf_section, default_value)
    local has_changed = false
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    if conf_section["type"] ~= nil then
        uci_r:set("ips", conf_section[".name"], "flag", conf_section["type"])
        uci_r:delete("ips", conf_section[".name"], "type")
        has_changed = true
    elseif conf_section["flag"] == nil then
        uci_r:set("ips", conf_section[".name"], "flag", default_value)
        has_changed = true
    end

    if has_changed == true then
        uci_r:commit("ips")
        cfgsync.set_config_changed()
    end
    return
end

--修改blacklist格式 原'sid:expire_time:direction' 实例 '1111:10:dst'
--现为json串格式, 实例'{"sid":"1111","expire_time":"10","direction":"dst"}'
function changeBlacklistException(conf_section)
    local blacklist = conf_section["blacklist_exception"]
    local new_list = {}
    local has_changed = false
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    if blacklist == nil then
        return has_changed
    end

    for _, para in pairs(blacklist) do
        if string.find(para, ",") ~= nil then
            has_changed = false
        else
            local parts = {}
            local to_json = {}

            for w in para:gmatch("[^:]+") do
                parts[#parts + 1] = w
            end

            to_json["sid"] = parts[1]
            to_json["expire_time"] = parts[2]
            to_json["direction"] = parts[3]

            -- 使用 cjson_api.encode 结果的顺序不固定
            new_list[#new_list + 1] = cjson_api.encode(to_json)
            has_changed = true
        end
    end

    if has_changed == true then
        uci_r:delete("ips",conf_section[".name"], "blacklist_exception")
        uci_r:set_list("ips",conf_section[".name"], "blacklist_exception", new_list)
        uci_r:commit("ips")
        cfgsync.set_config_changed()
    end

    return
end

local function ipsConfSync()
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    uci_r.foreach("ips", "ips_profile", function(conf_section)
        changePara(conf_section,"malicious_domain_check", "malicious_domain_check_enable")
        addFlag(conf_section, "user")
    end)

    uci_r.foreach("ips", "signature", function(conf_section)
        addFlag(conf_section, "system")
    end)

    uci_r.foreach("ips", "signature_filter", function(conf_section)
        addFlag(conf_section, "user")
    end)

    uci_r.foreach("ips", "ips_profile", function(conf_section)
        changeBlacklistException(conf_section)
    end)

    return
end

ipsConfSync()