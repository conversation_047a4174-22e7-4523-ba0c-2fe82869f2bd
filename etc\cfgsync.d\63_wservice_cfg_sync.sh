#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"

local db_wserv = {
    name = "wserv.db",
    tables = {
        {
            name = "wserv_tbl",
            columns= {
                {
                    name = "serv_id",
                    stype = "INTEGER",
                    db_attr = {"db_index"},
                    default_value = nil
                }, {
                    name = "enable",
                    stype = "VARCHAR ( 4 )",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "auto_bind",
                    stype = "VARCHAR ( 4 )",
                    db_attr = {},
                    default_value = "off"
                }, {
                    name = "default_bind_freq",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = "32639" -- 0b0111111101111111
                }, {
                    name = "default_bind_vlan",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = "0"
                }, {
                    name = "ssid",
                    stype = "VARCHAR ( 128 )",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "ssid_code_type",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "desc",
                    stype = "VARCHAR ( 128 )",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "isolate",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "ssidbrd",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "encryption",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "auth",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "cipher",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "radius_ip",
                    stype = "VARCHAR ( 40 )",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "radius_port",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "radius_acct_port",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "radius_pwd",
                    stype = "VARCHAR ( 128 )",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "key_update_intv",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "key",
                    stype = "VARCHAR ( 128 )",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "bw_ctrl_enable",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "bw_ctrl_mode",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "up_limit",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "down_limit",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "priv_key",
                    stype = "INTEGER",
                    db_attr = {"db_key", "db_auto_increment"},
                    default_value = nil
                }, {
                    name = "ssid_id",
                    stype = "VARCHAR ( 65 )",
                    db_attr = {},
                    default_value = nil
                }
            },--end columns
            datas = {}
        }
    }--end tables
}

local db_wserv_rd = {
    name = "wserv_rd.db",
    tables = {
        {
            name = "bind_tbl",
            columns= {
                {
                    name = "radio_id",
                    stype = "INTEGER",
                    db_attr = {"db_index"},
                    default_value = nil
                }, {
                    name = "wserv1",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan1",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv2",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan2",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv3",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan3",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv4",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan4",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv5",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan5",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv6",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan6",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv7",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan7",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "wserv8",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "vlan8",
                    stype = "INTEGER",
                    db_attr = {},
                    default_value = nil
                }, {
                    name = "priv_key",
                    stype = "INTEGER",
                    db_attr = {"db_key", "db_auto_increment"},
                    default_value = nil
                }
            },--end columns
            datas = {}
        }
    }--end tables
}

--uci config_sync

--database config_sync
cfgsync.config_sync(db_wserv, "database")
cfgsync.config_sync(db_wserv_rd, "database")
