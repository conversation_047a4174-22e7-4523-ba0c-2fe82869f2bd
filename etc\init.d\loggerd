#!/bin/sh /etc/rc.common
# Copyright (C) 2013 OpenWrt.org

# start after and stop before networking
START=15
PIDCOUNT=0

# USE_PROCD=1
SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/loggerd.pid
PROG=/usr/sbin/logger_read

validate_log_section()
{
	uci_validate_section system system "${1}" \
		'log_file:string' \
		'log_size:uinteger' \
		'log_ip:ipaddr' \
		'log_remote:bool:1' \
		'log_port:port:514' \
		'log_proto:or("tcp", "udp"):udp' \
		'log_prefix:string'
}

validate_log_daemon()
{
	uci_validate_section system system "${1}" \
		'log_size:uinteger:0' \
		'log_buffer_size:uinteger:0'
}

start_service_daemon()
{
	local log_buffer_size log_size
	log_buffer_size=$(uci_get system syslog log_size)
	[ -z "$log_buffer_size" ] && log_buffer_size=8192

	service_start /usr/sbin/loggerd -C ${log_buffer_size} -k
}

start_service_klog()
{
	/sbin/klogd &
}

start_service_file()
{
	PIDCOUNT="$(( ${PIDCOUNT} + 1))"
	local pid_file="/var/run/logread.${PIDCOUNT}.pid"
	local log_file log_size

	validate_log_section "${1}" || {
		echo "validation failed"
		return 1
	}
	[ -z "${log_file}" ] && return

	procd_open_instance
	procd_set_param command "$PROG" -f -F "$log_file" -p "$pid_file"
	[ -n "${log_size}" ] && procd_append_param command -S "$log_size"
	procd_close_instance
}

start_service_remote()
{
	PIDCOUNT="$(( ${PIDCOUNT} + 1))"
	local pid_file="/var/run/logread.${PIDCOUNT}.pid"
	local log_ip log_port log_proto log_prefix log_remote

	validate_log_section "${1}" || {
		echo "validation failed"
		return 1
	}
	[ "${log_remote}" -ne 0 ] || return
	[ -z "${log_ip}" ] && return

	procd_open_instance
	procd_set_param command "$PROG" -f -r "$log_ip" "${log_port}" -p "$pid_file"
	[ "${log_proto}" != "udp" ] || procd_append_param command -u
	[ -z "${log_prefix}" ] || procd_append_param command -P "${log_prefix}"
	procd_close_instance
}

service_triggers()
{
	procd_add_reload_trigger "system"
	procd_add_validation validate_log_section
}

#start_service()
start()
{
	config_load system
	start_service_klog
	#relay fs klog
	insmod relaylog 2>/dev/null
	start_service_daemon
}

#stop_searvice
stop()
{
	killall klogd
	service_stop /usr/sbin/loggerd
    if [ -f ${SERVICE_PID_FILE} ];then
        rm -f ${SERVICE_PID_FILE}
    fi
	#relay fs klog
	rmmod relaylog 2>/dev/null
}

restart()
{
	echo "restarting loggerd service" > /dev/console
	stop
	start
}

reload() {
    restart
}
