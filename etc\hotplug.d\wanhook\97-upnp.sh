#!/bin/sh

_delete_rule()
{
	local num=0 delnum=0

	external_ifaces=`uci get upnpd.config.external_iface`
	for iface in $external_ifaces
	do
		if [ $iface == "$1" ];then
			uci del_list upnpd.config.external_iface=$iface
			uci_commit upnpd
			let delnum++
		fi
		let num++
	done

	if [ $num -eq $delnum ];then
		uci set upnpd.config.enable_upnp="off"
		/etc/init.d/miniupnpd stop
		uci commit upnpd
	fi

}

state=`uci get upnpd.config.enable_upnp 2>/dev/null`
[ "$state" == "on" ] || return

case ${ACTION} in
	DELETE)

		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _delete_rule $element
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac