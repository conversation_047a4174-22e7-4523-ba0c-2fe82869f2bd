local uci = require("luci.model.uci")
local cjson = require("cjson")
local dbg = require("luci.torchlight.debug")
local zone_api = require "luci.torchlight.zone_api"
local MODULE_CONFIG = 'nqa'
local OPTIONS_CASE = 'check_case'
local OPTIONS_SUIT = 'check_suit'

local HC_CONFIG_FILE = "/etc/health_check/health_check.json"


function format_str(str)
    -- 这里由于lua语言的限制，将check_suit check_case 进行修改,手动将{} 转成[]
    local new_str
    new_str = string.gsub(str, "\"check_case\":{}", "\"check_case\":[]")
    new_str = string.gsub(new_str, "\"check_suit\":{}", "\"check_suit\":[]")
    return new_str
end
function health_check_config_reload()
    local case_items={}
    local suit_items={}
    local effect_dev = {}
    local uci_r = uci.cursor()
    uci_r:foreach( MODULE_CONFIG, OPTIONS_CASE,
        function(section)
            local item ={}

            item["case_id"] = tonumber(section["case_id"])
            item["name"] = section["name"]
            effect_dev=zone_api.zone_userif_to_effective_dev(section["interface"])
            if type(effect_dev) == "table" then
                effect_dev =effect_dev[1]
            end
            if effect_dev ~= nil then
                item["interface"] =effect_dev
            else
                return
            end
            item["type"] = section["type"]
            item["dst"] = section["dst"]
            item["probe_interval"] = tonumber(section["probe_interval"])*1000
            item["timeout"] = tonumber(section["timeout"])*1000
            item["retry"] = tonumber(section["retry"])

            local nqa={}
            nqa["nqa_enable"] = section["nqa_enable"]
            nqa["delay"] = tonumber(section["delay"] or -1)
            nqa["lose"] = tonumber(section["lose"] or -1)/100
            nqa["jitter"] = tonumber(section["jitter"] or -1)
            nqa["udp_jitter"] = tonumber(section["udp_jitter"] or -1)
            -- 发送间隔暂不生效，默认设置成20 后续要改的话再改动
            nqa["packet_interval"] = 20
            if nqa["nqa_enable"] == "on" then
                nqa["packet_num"] = tonumber(section["packet_num"])
            else
                nqa["packet_num"] = 1
            end

            local data ={}
            data["domain"] = section["domain"]
            data["src_port"] = tonumber(section["src_port"] or 0)
            data["dst_port"] = tonumber(section["dst_port"] or 0)
            item["data"]=data
            item["nqa"] =nqa
            -- 单位转化
            case_items[#case_items+1]=item
        end
    )
    uci_r:foreach( MODULE_CONFIG, OPTIONS_SUIT,
        function(section)
            local case_id_list={}
            if section["case_id_list"] then
                for _,case_id in pairs(section["case_id_list"]) do
                    case_id_list[#case_id_list+1] = tonumber(case_id)
                end
            end
            local item ={
                ["suit_id"] =tonumber(section["suit_id"]),
                ["name"] =section["name"],
                ["case_id_list"] =case_id_list,
                ["threshold"] =tonumber(section["threshold"]),
            }
            suit_items[#suit_items+1]=item
        end
    )
    for key, val in pairs(case_items) do
        val[".name"] = nil
        val[".type"] = nil
        val[".anonymous"] = nil
        val[".index"] = nil
    end
    for key, val in pairs(suit_items) do
        val[".name"] = nil
        val[".type"] = nil
        val[".anonymous"] = nil
        val[".index"] = nil
    end
    --case id 冲突
    for i=1,#case_items do
        for j=i+1, #case_items do
            if case_items[i]["case_id"] ==case_items[j]["case_id"] then
                dbg(" health_check config reload error,case_item id conflict")
                return
            end
        end
    end
    --suit id 冲突
    for i=1,#suit_items do
        for j=i+1, #suit_items do
            if suit_items[i]["suit_id"] ==suit_items[j]["suit_id"] then
                dbg(" health_check config reload error,suit item id conflict")
                return
            end
        end
    end
    --检查绑定关系是否冲突

    for i = 1, #suit_items do
        local case_id_list = suit_items[i]["case_id_list"]
        if case_id_list then
            for j = 1, #case_id_list do
                local exist = false
                for k = 1, #case_items do
                    if case_items[k]["case_id"] == case_id_list[j] then
                        exist = true
                    end
                end
                if exist ~= true then
                    dbg(" health_check config reload error,suit cannot match case_id")
                    return
                end
            end
        end
    end
    --重写配置文件
    local hc_data ={
        ["check_case"] = case_items,
        ["check_suit"] = suit_items
    }
    local str = string.gsub(format_str(cjson.encode(hc_data)),"\"case_id_list\":{}","\"case_id_list\":[]")
    local file = io.open(HC_CONFIG_FILE, "w")
    assert(file)
    file:write(str)
    file:close()
end
health_check_config_reload()