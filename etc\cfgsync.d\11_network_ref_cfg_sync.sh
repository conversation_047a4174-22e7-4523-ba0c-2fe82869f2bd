#!/usr/bin/lua

--[[
FW6600v1第一版软件的网桥仅支持出厂配置下进行设置，第二版软件支持非出厂模式下也可以配置，但是前一版本软件在修改网桥设置的时候，会对接口的ref进行+1，导致ref在删除网桥后也不是0，对再次配置网桥造成影响
]]

local dbg 	   = require "luci.torchlight.debug"
local uci 	   = require "luci.model.uci"
local cfgsync  = require "luci.torchlight.config_sync"
local conf_dir = "/tmp/etc/uc_conf"

local uci_r	      = uci.cursor()
local uci_t	      = uci.cursor()
local inf_ref     = {}
local inf_sname   = {}
local ori_inf_ref = {}
local has_changed = 0

uci_r:load(conf_dir)
uci_t:load(conf_dir)

-- 对接口名列表计算ref
local function add_ref_list(list, uci_file, uci_table)
	if list == nil then
		return
	end
	if type(list) == "string" then
		list = {list}
	end
	table.foreachi(list, function(_, name)
		if inf_ref[name] then
			inf_ref[name] = inf_ref[name] + 1
		end
	end)
end

-- 对uci配置文件的选项计算ref
local function add_ref_uci_list(uci_file, uci_table, uci_list)
	if uci_file == nil or uci_table == nil or uci_list == nil then
		return
	end
	uci_r.foreach(uci_file, uci_table, function(section)
		add_ref_list(section[uci_list], uci_file, uci_table)
	end)
end

local function network_ref_cfg()
    -- 遍历所有接口，初始化ref表
    uci_r.foreach("network", "interface", function(section)
    	if section["t_name"] then
    		inf_ref[section["t_name"]] = 0
    		inf_sname[section["t_name"]] = section[".name"]

    		ori_inf_ref[section["t_name"]] = section["t_reference"]
    	end
    end)

    -- 记录原有网桥、pppoe、ethernet接口的ref
    uci_r.foreach("network", "interface", function(section)
    	if section["t_type"] == "bridge" and section["t_bindif"] then
    		add_ref_list(section["t_bindif"], "network", "interface")
    	end
    	if section["t_type"] == "pppoe" and section["t_bindif"] then
    		add_ref_list(section["t_bindif"], "network", "interface")
    	end
    	if section["t_type"] == "ethernet" and section["t_bindif"] then
    		add_ref_list(section["t_bindif"], "network", "interface")
    	end
    end)

    -- 遍历所有需要对接口ref+1的功能
    -- 带宽策略
    add_ref_uci_list("qos", "rule", "if_ping")
    add_ref_uci_list("qos", "rule", "if_pong")

    -- NAPT策略
    add_ref_uci_list("nat", "rule_napt", "if")

    -- 一对一NAT
    add_ref_uci_list("nat", "rule_onenat", "if")

    -- 服务器映射
    add_ref_uci_list("firewall", "redirect", "if")

    -- NAT-DMZ
    add_ref_uci_list("nat", "rule_dmz", "if")

    -- UPNP
    uci_r.foreach("upnpd", "upnp", function(section)
    	if section["enable_upnp"] == "on" then
    		add_ref_list(section["internal_iface"], "upnpd", "upnp")
    		add_ref_list(section["external_iface"], "upnpd", "upnp")
    	end
    end)

    -- IP-MAP绑定 ARP防欺骗功能，不计算

    -- IP-MAC绑定规则列表
    add_ref_uci_list("ip_mac_bind", "user_bind", "interface")

	-- MAC地址过滤功能
	uci_r.foreach("mac_filter", "mac_filter", function(section)
    	if section["enable"] == "1" then
    		add_ref_list(section["interfaces"], "mac_filter", "mac_filter")
    	end
    end)

	-- 安全区域
	uci_r.foreach("zone", "zone", function(section)
    	if section["type"] == "user" then
    		-- 用户可修改、用户配置的安全区域
    		add_ref_list(section["iface"], "zone", "zone")
    	end
    end)

	-- DHCP服务
	add_ref_uci_list("dhcpd", "dhcpd_list", "interface")

	-- 路由智能均衡
	add_ref_uci_list("load_balance", "load_balance", "use_if")

	-- ISP选路规则列表
	add_ref_uci_list("isp_route", "rule", "if")

	-- 线路备份
	add_ref_uci_list("line_backup", "rule", "master_if")
	add_ref_uci_list("line_backup", "rule", "slave_if")

	-- 策略路由
	uci_r.foreach("policy_route", "policy_rule", function(section)
    	if section["use_policy"] then
    		local group = uci_t:get_all("policy_route", section["use_policy"])
    		if group then
    			add_ref_list(group["use_iface"], "policy_route", "policy_rule")
    		end
    	end
    end)

	-- 静态路由
	add_ref_uci_list("network", "user_route", "if")

	-- IPSec安全策略
	uci_r.foreach("vpn", "ipsec_connection", function(section)
		-- 不计算通过L2TP配置的IPSec策略
    	if section["bindif"] and section["name"] then
    		if string.find(section["name"], "X_l2tp_") == nil then
    			inf_ref[section["bindif"]] = inf_ref[section["bindif"]] + 1
    		end
    	end
    end)

	-- L2TP服务器设置服务接口
	add_ref_uci_list("vpn", "lns", "bindif")

	-- L2TP客户端设置出接口
	add_ref_uci_list("vpn", "lac", "outif")

	-- PPTP服务器设置服务接口
	add_ref_uci_list("vpn", "pns", "bindif")

	-- PPTP客户端设置出接口
	add_ref_uci_list("vpn", "pac", "outif")

	-- DNS代理服务接口
	add_ref_uci_list("dns", "rule", "service_if")
	add_ref_uci_list("dns", "rule", "out_if")

	-- 花生壳动态域名服务接口
	add_ref_uci_list("phddns", "phddns", "interface")

	-- 科迈动态域名服务接口
	add_ref_uci_list("cmxddns", "cmxddns", "interface")

	-- 3322动态域名服务接口
	add_ref_uci_list("dyn3322ddns", "phddns", "interface")

	-- 主备倒换规则列表
	uci_r.foreach("keepalived", "global", function(section)
    	if section["vrrp_interface_enable"] == "on" then
    		add_ref_list(section["vrrp_interface"], "keepalived", "global")
    	end
    end)
	add_ref_uci_list("keepalived", "rule", "effect_interface")

    -- IPv6桥模式
    local is_enable = uci_r:get("network", "bridge_v6", "bridge6_enable")
    if "on" == is_enable then
        add_ref_uci_list("network", "interface", "ipv6_WAN")
        add_ref_uci_list("network", "interface", "ipv6_LAN")
    end
    

	table.foreach(inf_ref, function(name, _)
		if ori_inf_ref[name] and inf_sname[name] and tonumber(ori_inf_ref[name]) ~= inf_ref[name] then
			uci_r:set("network", inf_sname[name], "t_reference", inf_ref[name])
			has_changed = 1
		end
	end)

	if has_changed == 1 then
		uci_r:commit("network")
	    cfgsync.set_config_changed()
	end

	return
end

network_ref_cfg()
