#!/bin/sh

[ -x /usr/sbin/dhcp6c ] || exit 0

[ -n "$INCLUDE_ONLY" ] || {
	. /lib/functions.sh
	. ../netifd-proto.sh
	init_proto "$@"
}

setup_slaac () {
	local interface=$1
	local ifname=$2
	local dns=$3

	proto_init_update "$ifname" 1

	while read line
	do
		[ $ifname != ${line##* } ] && continue
		[ "fe80" = ${line:0:4} ] && continue
		element=${line:0:32}
		ip6addr=${element:0:4}:${element:4:4}:${element:8:4}:${element:12:4}
		ip6addr=$ip6addr:${element:16:4}:${element:20:4}:${element:24:4}:${element:28:4}
		new_ip6addr=$ip6addr
	done </proc/net/if_inet6

	[ -z "$new_ip6addr" ] && {
		proto_notify_error "$interface" "NO ADDRESS"
		sleep 3
		proto_setup_failed "$interface"
		return
	}

	if [ -n "$new_ip6addr" ]; then
		proto_add_ipv6_address "$new_ip6addr" "64"
	fi

	local ip6gw=`cat /proc/sys/net/ipv6/conf/$ifname/default_gateway`
	[ ${#ip6gw} -ne 0 ] && proto_add_ipv6_route "::" 0 "$ip6gw"

	# not support parse RDNSS options yet, set default dns instead
	[ -z "$dns" ] && dns="240c::6666 240c::6644"
	[ -n "$dns" ] && {
		DNS2=${dns##*[, ]}
		DNS1=${dns%%[, ]*}
		[ -n "$DNS1" ] && {
			proto_add_dns_server "$DNS1"
			[ "$DNS1" != "$lan_dns1" ] && let "reload_flag+=1"
		}
		[ -n "$DNS1" -a -n "$DNS2" -a "$DNS1" != "$DNS2" ] && {
			proto_add_dns_server "$DNS2"
			[ "$DNS2" != "$lan_dns2" ] && let "reload_flag+=1"
		}
	}

	proto_send_update "$interface"

	# wait interface to setup
	sleep 3
	local gw_route=`route -A inet6 | grep ::/0`
	[ -z ${gw_route} ] && {
		local gateway=`cat /proc/sys/net/ipv6/conf/$ifname/default_gateway`
		[ ${#gateway} -ne 0 ] && route -A inet6 add default gw "$gateway" dev "$ifname" metric 1024
	}
}

proto_dhcpv6_create_conffile() {
	local ifname="$1"
	local pd_mode="$2"
	local ip_config="$3"
	local dns6="$4"
	local config="$5"
	local id

	local mac="$(ifconfig "$ifname" | sed -ne 's/[[:space:]]*$//; s/.*HWaddr //p')"
	local oIFS="$IFS"; IFS=":"; set -- $mac; IFS="$oIFS"
	let "id = ((0x$1^0x$2^0x$3^0x$4) << 16) | (0x$5 << 8) | (0x$6)"
	local id2=`expr $id + 1`

	local dhcp6c_dir="/tmp/dhcp6c/$config"
	local conffile="$dhcp6c_dir/dhcp6c.conf"
	local dhcp6c_pid="$dhcp6c_dir/dhcp6c.pid"
	rm -rf ${dhcp6c_dir}
	mkdir -p ${dhcp6c_dir}


	[ "$pd_mode" == "prefix" ] && {
		echo "interface $ifname {" > "${conffile}"
		[ "$ip_config" == "dhcpv6" ] && echo -e "\tsend ia-na $id;" >> "${conffile}"
		echo -e "\tsend ia-pd $id2;" >> "${conffile}"
		[ "x$dns6" == "x" ] && echo -e "\trequest domain-name-servers;" >> "${conffile}"
		echo -e "\tscript \"/lib/netifd/dhcp6c.script\";" >> "${conffile}"
		echo -e "};\n" >>  "${conffile}"
		echo -e "id-assoc na $id {};\n" >> "${conffile}"
		echo "id-assoc pd $id2 {" >> "${conffile}"
		echo -e "\tprefix-interface $ifname {" >> "${conffile}"
		echo -e "\t\tsla-id 1;" >> "${conffile}"
		echo -e "\t};" >>  "${conffile}"
		echo "};" >>  "${conffile}"
	}

	[ "$pd_mode" == "non_temp" ] && {
		echo "interface $ifname {" > "${conffile}"
		[ "$ip_config" == "dhcpv6" ] && echo -e "\tsend ia-na $id;" >> "${conffile}"
		[ "$ip_config" == "slaac" ] && echo -e "\tinformation-only;" >> "${conffile}"
		[ "x$dns6" == "x" ] && echo -e "\trequest domain-name-servers;" >> "${conffile}"
		echo -e "\tscript \"/lib/netifd/dhcp6c.script\";" >> "${conffile}"
		echo "};" >>  "${conffile}"
		echo "id-assoc na $id {};" >> "${conffile}"
	}
}

proto_dhcpv6_setup() {
	local config="$1"
	local ifname="$2"
	local count=10
	local mbit=1
	local obit=1

	echo '-1' > /proc/sys/net/ipv6/conf/$ifname/ndisc_mbit
	echo '2' > /proc/sys/net/ipv6/conf/$ifname/accept_ra
	icmpv6_rs $ifname $config

	local pd_mode=`uci get network.$config.pd_mode`
	local ip_config=`uci get network.$config.ip_config`
	local dns=`uci get network.$config.dns`
	local dns6=""
	[ -n "$dns" ] && {
		for val in $dns; do
			local rslt=`echo $val | grep ":"`
			if [ -n "$rslt" ]; then
				[ -n "$dns6" ] && dns6="$dns6 $val" || dns6="$val"
			fi
		done
	}

	# check slaac mode first
	[ "$ip_config" == "slaac" ] && {
		mbit=`cat /proc/sys/net/ipv6/conf/$ifname/ndisc_mbit`
		while [ $count -ne 0 -a $mbit -eq -1 ]; do
			sleep 1
			mbit=`cat /proc/sys/net/ipv6/conf/$ifname/ndisc_mbit`
			let "count=count-1"
		done

		if [ $mbit -ne 0 ]; then
			proto_notify_error "$config" "NO_SLAAC_SERVER"
			sleep 3
			proto_setup_failed "$config"
			return
		fi
		obit=`cat /proc/sys/net/ipv6/conf/$ifname/ndisc_obit`
	}

	[ "$ip_config" == "auto" ] && {
		# wait for RA
		mbit=`cat /proc/sys/net/ipv6/conf/$ifname/ndisc_mbit`
		while [ $count -ne 0 -a $mbit -eq -1 ]; do
			sleep 1
			mbit=`cat /proc/sys/net/ipv6/conf/$ifname/ndisc_mbit`
			let "count=count-1"
		done

		if [ $mbit -eq 0 ]; then
			ip_config="slaac"
			obit=`cat /proc/sys/net/ipv6/conf/$ifname/ndisc_obit`
		elif [ $mbit -eq 1 ]; then
			ip_config="dhcpv6"
		else
			sleep 3
			proto_setup_failed "$config"
			return
		fi
	}

	# dhcpv6 client is not necessary in stateless RDNSS mode
	[ "$ip_config" == "slaac" -a "$pd_mode" == "non_temp" ] && {
		if [ "$obit" -eq 0 -o "x$dns6" != "x" ]; then
			setup_slaac "$config" "$ifname" "$dns6"
			return
		fi
	}

	proto_dhcpv6_create_conffile "$ifname" "$pd_mode" "$ip_config" "$dns6" "$config"

	local dhcp6c_dir="/tmp/dhcp6c/$config"
	local conffile="$dhcp6c_dir/dhcp6c.conf"
	local dhcp6c_pid="$dhcp6c_dir/dhcp6c.pid"
	local duid="$dhcp6c_dir/dhcp6c_duid"

	proto_run_command "$config" /usr/sbin/dhcp6c -f \
		-p "$dhcp6c_pid" \
		-c "$conffile" \
		-u "$duid" \
		-t "$config" \
		"$ifname"
}

proto_dhcpv6_teardown() {
	local interface="$1"
	local ifname="$2"

	case "$ERROR" in
		11|19)
			proto_notify_error "$interface" AUTH_FAILED
			proto_block_restart "$interface"
		;;
		2)
			proto_notify_error "$interface" INVALID_OPTIONS
			proto_block_restart "$interface"
		;;
	esac

	local dhcp6c_dir="/tmp/dhcp6c/$interface"
	local conffile="$dhcp6c_dir/dhcp6c.conf"
	local dhcp6c_pid="$dhcp6c_dir/dhcp6c.pid"
	local prefix="$dhcp6c_dir/prefix.info"
	[ -d "$dhcp6c_dir" ] && {
		# kill dhcp6c
		if [ -f $dhcp6c_pid ]; then
			local pid=`cat $dhcp6c_pid`
			if [ -n "$pid" ]; then
				kill $pid
			fi
		fi
		rm -rf $dhcp6c_dir
	}

	[ -n "$ifname" ] && {
		echo "1" > /proc/sys/net/ipv6/conf/$ifname/accept_ra
		route -A inet6 del default dev "$ifname" metric 1024
	}

	proto_kill_command  "$interface" 15
}

[ -n "$INCLUDE_ONLY" ] || {
	add_protocol dhcpv6
}
