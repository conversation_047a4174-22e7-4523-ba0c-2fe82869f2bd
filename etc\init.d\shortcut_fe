#!/bin/sh /etc/rc.common
# Copyright (C) 2015 TP-Link

START=75

SFE_SWITCH=/sys/fast_classifier/sfe_switch
PROC_TCP_SET=/proc/sys/net/netfilter/nf_conntrack_tcp_no_window_check

load_switch()
{
	config_load shortcut_fe
	config_get_bool switch main enable

	echo $switch > $SFE_SWITCH
}

boot()
{
	insmod shortcut_fe
	insmod fast_classifier
	start
}

start()
{
	#卸载时也不能改回为0，否则存在内网的数据包可能出现在外网中
	echo 1 > $PROC_TCP_SET

	load_switch
}

stop()
{
	echo 0 > $SFE_SWITCH
}

restart()
{
	stop
	start
}

reload()
{
	load_switch
}
