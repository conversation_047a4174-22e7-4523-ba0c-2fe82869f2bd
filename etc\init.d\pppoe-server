#!/bin/sh /etc/rc.common
# Copyright (C) 2006-2011 OpenWrt.org

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/pppoeserver.pid

START=97

DEFAULT=/tmp/pppoe/server/config/pppoe-server-argvs

start() {
	if [ -f ${SERVICE_PID_FILE} ];then
		echo "pppoeserver is existent" >> /dev/console
		return
	fi

	local state=`uci get pppoe_server.setting.enable`
	if [ "$state" == "off" ];then
		echo "state is off" >> /dev/console
		return
	fi
	
	lua /usr/lib/lua/pppoeserver/pppoeserver_start.lua
	if [ -f $DEFAULT ] && . $DEFAULT; then
		echo "pppoe server starting" >> /dev/console
		service_start /usr/sbin/pppoe-server $OPTIONS
	fi
}

stop() {
	if [ -f ${SERVICE_PID_FILE} ];then
		lua /usr/lib/lua/pppoeserver/pppoeserver_stop.lua
		service_stop /usr/sbin/pppoe-server
		rm -f ${SERVICE_PID_FILE}
	fi
}

restart() {
	stop
	start
}
