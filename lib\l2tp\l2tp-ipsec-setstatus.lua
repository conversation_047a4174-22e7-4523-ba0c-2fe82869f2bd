-- now, this script can only modify the status of ipsec
-- usage: lua <this script> <l2tp section name> [on|off]
-- arg[1]:l2tp section name  arg[2]: on|off
-- for example: lua l2tp-ipsec-operate.lua lac_12345544 on

local dbg = require "luci.tools.debug"
local l2tp_utility= require "luci.controller.admin.l2tp"
local uci = require "luci.model.uci"
local uci_r = uci.cursor()

local l2tp_config = "vpn"
local l2tp_sectype = "lac"
local l2tp_secname = ""
local newenable = ""

--get the section of lac
local function get_old_section(sname)
    local ret = nil
    uci_r:foreach(l2tp_config, l2tp_sectype, function(section)
            if section[".name"] == sname then
                ret = section
            end
        end
        )
    return ret
end

if #arg < 2 then
    return false
end

if arg[2] ~= "on" and arg[2] ~= "off" then
    return false
end

l2tp_secname = arg[1]
newenable = arg[2]

dbg("L2TP:make " .. l2tp_secname .. " ipsec " .. newenable)

local old = get_old_section(l2tp_secname)
if not old then
    return false
end

local secid = string.sub(l2tp_secname, 5)
local ipsec_secname = "ipsec_connection_"..secid

local old_enable = uci_r:get("vpn", ipsec_secname, "enable")
if not old_enable or old_enable == newenable then
    dbg("the enable is not change")
    return true
end

local new = get_old_section(l2tp_secname)
new.enable = newenable

local res = l2tp_utility._modify_ipsec_conns_for_l2tp(old,new,"lac")

if not res then
    return false
else
    return true
end

