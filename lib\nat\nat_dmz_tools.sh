#!/bin/sh /etc/rc.common

# dmz服务相关的辅助函数
. /lib/nat/nat_chain_op.sh
. /lib/nat/nat_rule_op.sh
. /lib/firewall/fw.sh

TCP_SERVICE_LIST="http https pptp ssh"
UDP_SERVICE_LIST="ike l2tp sae"
TCP_SERVICE_LIST_NO_HTTP_NO_HTTPS_NO_SSH="pptp"

# open port
# this function may be rewrite for port change
nat_dmz_port_rule_add()
{
    local proto=$1
    local chain_name="prerouting_rule_dmz"
    local IPT=$IPT4

    # find the number of rules in the chain
    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    local chain_rule_num=`$IPT -S ${chain_name} | wc -l`

    if [ -z "$chain_rule_num" -o $chain_rule_num -eq 1 ];then
		local tcp_ports_default=$(nat_dmz_service_ports $TCP_SERVICE_LIST_NO_HTTP_NO_HTTPS_NO_SSH)
		local udp_ports=$(nat_dmz_service_ports $UDP_SERVICE_LIST)

		$IPT -I ${chain_name} -p udp -m multiport --dport ${udp_ports} -j RETURN
        $IPT -I ${chain_name} -p tcp -m multiport --dport ${tcp_ports_default} -j RETURN
        $IPT -I ${chain_name} -p 47 -j RETURN
        $IPT -I ${chain_name} -p 50 -j RETURN
        $IPT -I ${chain_name} -p 51 -j RETURN

		add_port_mngt_rule $1 ${chain_name} $2 $3
    fi
}

nat_dmz_port_rule_del()
{
    local proto=$1
    local chain_name="prerouting_rule_dmz"
    # find the number of rules in the chain
    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

	# 查看当前链中是否还存在dnat规则
	local dnat_rule_exit=`$IPT -nvL ${chain_name} | awk '{print $3}' | grep DNAT | wc -l`
	if [ -z "$dnat_rule_exit" -o $dnat_rule_exit -eq 0 ];then
		# 清空dmz链中的规则
		$IPT -F ${chain_name}

		# 规则删除完之后再删除链
		nat_del_chain ${chain_name} "prerouting_rule" ${proto}
		nat_del_chain "prerouting_rule" "PREROUTING" ${proto}
	fi
}

nat_dmz_service_ports(){
	local service_list=$@
	local ports port
	for ser in $service_list;do
		port=$(nat_service_to_port $ser)
		ports=${ports:+$ports,}${port:+$port}
	done
	echo $ports
	return 0
}

nat_service_to_port(){ #$service
	local port http_list https_list ssh_list
	case "$1" in
	http|HTTP|Http)
		port="$(uci_get_state uhttpd main listen_http |cut -d : -f 5)"
		[ -z "$port" ] && port=80
		;;
	https|HTTPS|Https)
		port="$(uci_get_state uhttpd main listen_https |cut -d : -f 5)"
		[ -z "$port" ] && port=443
		;;
	pptp|Pptp|PPTP)
		port=1723  #default port
		;;
	l2tp|L2TP|L2tp)
		port=1701  #default port
		;;
	IKE|ike|Ike)
		port=500  #default port
		;;
	ssh|SSH|Ssh)
		port=$(uci_get administration port SSH)
		[ -z "$port" ] && port=22
		;;
	sae|SAE|Sae)
		port=4500  #default port
		;;
	*)
		return 1
		;;
	esac

	echo "$port"
	return 0
}

add_port_mngt_rule()
{
	local proto=$1
	local chain_name=$2
	local remote_rule_status=$3
	local ipset_used_mngt=$4
	local IPT="iptables"
	local tcp_ports_rm=""

	case $proto in
    IPv4)
        IPT="iptables"
		IPSET="remote_mngt_accept_ip_set"
    ;;
    IPv6)
        IPT="ip6tables"
		IPSET="remote_mngt_accept_ip_set_ipv6"
    ;;
    esac

	# 处理远程管理
	if [ "${remote_rule_status}" = 1 ];then
		# 判断当前是否有远程管理规则
		local remote_rule_exit=`$IPT -t nat -w -nvL | grep -c "\* remote_mngt \*"`
		# 若当前不存在远程管理规则，则添加
		if [ 0 = "${remote_rule_exit}" ];then
			tcp_ports_rm=`$IPT -w -nvL remote_mngt_chain |grep -w "\* remote_mngt \*" | sed 's/.*multiport dports //g' | awk '{print $1}'`

			if [ 1 = "${ipset_used_mngt}" ];then
				$IPT -t nat -w -I ${chain_name} -p tcp -m set --match-set ${IPSET} src -m multiport --dport ${tcp_ports_rm} -m comment --comment remote_mngt -j RETURN
			else
				$IPT -t nat -w -I ${chain_name} -p tcp -m multiport --dport "$tcp_ports_rm" -m comment --comment remote_mngt -j RETURN
			fi
		fi

		echo "remote mngt is on, DMZ reject ralative tcp port: $tcp_ports_rm" > /dev/console
	fi

	# 处理已开启远程管理的接口
	local remote_rule_exit=`$IPT -t nat -w -nvL ${chain_name} | grep -c multinetdev`
	if [ 0 = "${remote_rule_exit}" ];then
		# 获取开启了远程管理的接口
		local iface_in_rm=""
		local mngt_iface=""
		case $proto in
		IPv4)
			mngt_ifaces=`$IPT -w -nvL remote_mngt_chain | grep -w ACCEPT | grep -v "\* remote_mngt \*" | awk '{print $6}'`
		;;
		IPv6)
			mngt_ifaces=`$IPT -w -nvL remote_mngt_chain | grep -w ACCEPT | grep -v "\* remote_mngt \*" | awk '{print $5}'`
		;;
		esac

		for mngt_iface in $mngt_ifaces; do
			if [ ${iface_in_rm} = ""]; then
				tcp_ports_rm=`$IPT -w -nvL remote_mngt_chain | grep -w "$mngt_iface" | awk '{print $NF}' | head -n1`
				iface_in_rm="$mngt_iface"
			else
				iface_in_rm="$mngt_iface,$iface_in_rm"
			fi
		done

		if [ "$iface_in_rm" != "" ]; then
			$IPT -t nat -w -I ${chain_name} -p tcp -m multinetdev --in ${iface_in_rm} -m multiport --dport ${tcp_ports_rm} -j RETURN
			echo "mngt ifaces exist, DMZ reject relative tcp port: $tcp_ports_rm" > /dev/console
		fi
	fi
}


dmz_rule_creat_white()
{
	local name=$1
	local ip_proto=$2
	local dest_ip=$3

	case $ip_proto in
	IPv4)
	# do nothing
	;;
	IPv6)
	local chain_exist=`ip6tables -w -t filter -nvL | grep -w ipv6_dmz_forward`
	[ -z "$chain_exist" ] && {
		fw add 6 f ipv6_dmz_forward
		ip6tables -w -t filter -I forwarding_rule -j ipv6_dmz_forward
	}

	fw del 6 f ipv6_dmz_forward ACCEPT { -d $dest_ip -m comment --comment $name }
	fw add 6 f ipv6_dmz_forward ACCEPT { -d $dest_ip -m comment --comment $name }
	;;
	esac

}

dmz_rule_destroy_white()
{
	local rule_name=$1
	local ip_proto=$2
	local chain_name="ipv6_dmz_forward"

	case $ip_proto in
	IPv4)
	# do nothing
	;;
	IPv6)
	#find the number of the rule
	local rule_num=`ip6tables -w -t filter -nv --line-number -L ${chain_name} | grep -w "\/\* ${rule_name} \*\/" | sort -r -n | cut -d " " -f 1`

	for nums in $rule_num
	do
		ip6tables -w -t filter -D ${chain_name} ${nums}
	done
	;;
	esac
}

del_port_mngt_rule()
{
	local proto=$1
	local chain_name=$2
	local IPT="iptables -t nat -w"

	case $proto in
    IPv4)
        IPT="iptables -t nat -w"
    ;;
    IPv6)
        IPT="ip6tables -t nat -w"
    ;;
    esac

	local ln=`$IPT -nvL ${chain_name} --line| grep -w "\* remote_mngt \*" | awk '{print $1}'`
	[ "$ln" != "" ] && ${IPT} -D ${chain_name} $ln
	ln=`$IPT -nvL ${chain_name} --line| grep -w multinetdev | awk '{print $1}'`
	[ "$ln" != "" ] && ${IPT} -D ${chain_name} $ln
}