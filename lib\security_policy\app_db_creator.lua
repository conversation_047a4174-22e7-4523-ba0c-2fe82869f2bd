--=================================================================
-- 创建时调用函数 db_creator()
-- 销毁时调用函数 db_destroy()，会删除ipset和/tmp/policy_db/app_db文件夹

-- 工作：
-- 生成策略DB需要的uci
-- 生成内容安全DB需要的snort规则
-- -- 邮箱 receiveridMark/senderidMark
-- -- 用户自定义的appid规则 appidMark
--=================================================================

-- 固定常量
local uci = require "luci.model.uci"
local sys = require "luci.sys"
local dbg = require "luci.tools.debug"
local bit = require "nixio".bit
-- 数据库
local sql = require "luci.torchlight.uac.firewall_sqlApi"
local wrapper = require "luci.torchlight.uac.luasqlite3_wrapper"

local APP_DB_PATH = "/tmp/sdb/app/app.db"
local DB_APP_LIST = "db_app_list"

local SERVICE_ID_SMTP = 1
local SERVICE_ID_IMAP = 2
local SERVICE_ID_POP3 = 3
local SERVICE_ID_HTTP = 4
local SERVICE_ID_FTP = 8

local APP_ACTION_ID_UPLOAD = "app_ipset_action_upload"
local APP_ACTION_ID_DOWNLOAD = "app_ipset_action_download"
local APP_ACTION_ID_UP_OR_DOWNLOAD = "app_ipset_action_up_or_download"
local APP_ACTION_ID_DELETE = "app_ipset_action_delete"
local APP_ACTION_ID_HTTP_POST = "app_ipset_action_http_post"
local APP_ACTION_ID_HTTP_PROXY = "app_ipset_action_http_proxy"
local APP_ACTION_ID_HTTP_WEB = "app_ipset_action_http_web"

local APP_SEPIPSET_FOR_DOWNLOAD = "app_ipset_action_spe_download"
local APP_SEPIPSET_FOR_WEB = "app_ipset_action_spe_web"

local ipset_name = "app_ipset_"

local NONE = "any"

local ACCEPT = "ACCEPT"
local DROP = "DROP"

local CONTENT_LOG = "CONTENT_LOG"
local APP_CONTROL_LOG = "APP_CONTROL_LOG"
local EMAIL_LOG = "EMAIL_LOG"
local CONTENT_ALERT = "CONTENT_ALERT"
local CONTENT_FILTER_LOG = "CONTENT_FILTER_LOG"

local FLOW_ID_UNKOWN = 1

local FILETYPE_MIN = 2
local FILETYPE_MAX = 255

local EMAIL_MIN = 2
local EMAIL_MAX = 255

-- PC版本QQ登录的id
local APPIDS_QQ_LOGIN = {}
local APPIDS_QQ_LOGIN_PATH = "/tmp/sdb/app/qq_account_appid.conf"

-- 设置uci路径
local db_dir = "/tmp/policy_db/app_db"
local conf_dir = "/etc/config"
local db_table_name = "app"
local uci_db = uci.cursor()
local uci_conf = uci.cursor()

-- snort规则输出文件
local sfile = nil
local pfile = nil

-- 文件后缀输出文件
local ffile = nil
-- 邮件地址输出文件
local efile = nil

-- 内容过滤规则输出文件
local cfile = nil

local ipset_counter = 1
local public_counter = 1
local filetypeMap = {}
local emailaddressMap = {}
local port_group_counter = 1

-- SID的范围
local snort_counter_AppRule_start = **********
local snort_counter_AppRule = **********
local snort_counter_AppRule_max = ********** - 1
local snort_counter_Email = **********
local snort_counter_Email_max = ********** - 1
local snort_counter_ContentFilter = **********
local snort_counter_ContentFilter_max = ********** - 1

-- app rules
local app_id_map = {}

-- 内容过滤关键字计算 sid与count相关 msg与groupid相关
local keywordCount = 1
local keywordGroupId = 0
local uploadKeywordArray = {}
local downloadKeywordArray = {}

-- 固定常量初始化
uci_db:set_confdir(db_dir)
uci_conf:set_confdir(conf_dir)

-- 转list
function to_list(para)
    local t = type(para)
    if t == "nil" or para == "" then
        return nil
    elseif t == "number" or t == "string" or t == "boolean" then
        para = tostring(para)
    elseif t == "table" then
        return para
    else
        error("can not unserialize a " .. t .. " type.")
    end
    local list = {}
    list[#list + 1] = para
    return list
end


--=========================扩展appids相关=================================

-- 预处理器可能会写入的基础appid，用于无应用特征库的场景
local DEFAULT_APPID_EXTEND_MAP = {0, 25001, 25002, 25003, 25005, 25013, 25014, 25015, 25016, 25017, 25018, 25019}
local DB_BASIC_GROUP = 125
local appid_extend_map = nil

function extendAppid(appid_list)
    if appid_list == nil then
        return appid_list
    end

    if appid_extend_map == nil then
        appid_extend_map = {}

        -- DB是否存在
        local file = io.open(APP_DB_PATH, "rb")
        if file then
            file:close()

            local dbenv, dbcon = sql.open_sqlite(APP_DB_PATH);
            local sql_cmd = "SELECT * FROM " .. DB_APP_LIST .. " where sub_usage ='" .. DB_BASIC_GROUP .. "' ;"
            local cur, errorString = wrapper.sqlite3_db_execute(dbcon, sql_cmd)
            if cur then
                row = cur:fetch ({}, "a")
                while row ~= nil do
                    if row["id"] then
                        appid_extend_map[row["id"]] = true
                    end
                    -- 取下一行
                    row = cur:fetch (row, "a")
                end
                wrapper.sqlite3_db_cursor_close(cur)
            end
        end

        -- 为确保准确，将DEFAULT_APPID_EXTEND_MAP中的id加入appid_extend_map
        for _, id in pairs(DEFAULT_APPID_EXTEND_MAP) do
            appid_extend_map[id] = true
        end
    end

    local extend_appid_list = {}
    for _, appid in pairs(appid_list) do
        if appid < 25000 or appid > 25999 then
            for eid, _ in pairs(appid_extend_map) do
                extend_appid_list[#extend_appid_list + 1] = eid * (2^16) + appid
            end
        else
            extend_appid_list[#extend_appid_list + 1] = appid * (2^16)
        end
    end

    return extend_appid_list
end


--==========================生成content_db snort规则函数=================================
----------------------------------安全配置文件相关---------------------------------------

function allocateFileTypeId()
    local counter = FILETYPE_MIN
    uci_conf.foreach("sec_content_conf", "file_content_filter_conf", function(conf_section)
        local exname_list = to_list(conf_section["exname_list"])
        for _, exname in pairs(exname_list) do
            if filetypeMap[exname] == nil and counter <= FILETYPE_MAX then
                filetypeMap[exname] = counter
                counter = counter + 1

                -- 文件后缀输出文件
                ffile:write(filetypeMap[exname] .. " " .. exname .. "\n")
            elseif counter > FILETYPE_MAX then
                dbg("Error: too much suffix")
            end
        end
    end)
end

function allocateEmailAccountIdOneProtocol(conf_section, counter, protocol)
    if conf_section[protocol .. "_sender_black_list_enable"] == "on" and conf_section[protocol .. "_sender_black_list"] ~= nil then
        local add_list = to_list(conf_section[protocol .. "_sender_black_list"])
        for _, add in pairs(add_list) do
            if emailaddressMap[add] == nil and counter <= EMAIL_MAX then
                emailaddressMap[add] = counter
                counter = counter + 1

                -- 邮箱地址输出文件
                efile:write(emailaddressMap[add] .. " " .. add .. "\n")
            end
        end
    end

    if conf_section[protocol .. "_sender_white_list_enable"] == "on" and conf_section[protocol .. "_sender_white_list"] ~= nil then
        local add_list = to_list(conf_section[protocol .. "_sender_white_list"])
        for _, add in pairs(add_list) do
            if emailaddressMap[add] == nil and counter <= EMAIL_MAX then
                emailaddressMap[add] = counter
                counter = counter + 1

                -- 邮箱地址输出文件
                efile:write(emailaddressMap[add] .. " " .. add .. "\n")
            end
        end
    end

    if conf_section[protocol .. "_receiver_black_list_enable"] == "on" and conf_section[protocol .. "_receiver_black_list"] ~= nil then
        local add_list = to_list(conf_section[protocol .. "_receiver_black_list"])
        for _, add in pairs(add_list) do
            if emailaddressMap[add] == nil and counter <= EMAIL_MAX then
                emailaddressMap[add] = counter
                counter = counter + 1

                -- 邮箱地址输出文件
                efile:write(emailaddressMap[add] .. " " .. add .. "\n")
            end
        end
    end

    if conf_section[protocol .. "_receiver_white_list_enable"] == "on" and conf_section[protocol .. "_receiver_white_list"] ~= nil then
        local add_list = to_list(conf_section[protocol .. "_receiver_white_list"])
        for _, add in pairs(add_list) do
            if emailaddressMap[add] == nil and counter <= EMAIL_MAX then
                emailaddressMap[add] = counter
                counter = counter + 1

                -- 邮箱地址输出文件
                efile:write(emailaddressMap[add] .. " " .. add .. "\n")
            end
        end
    end
    return counter
end

function allocateEmailAccountId()
    local counter = EMAIL_MIN
    uci_conf.foreach("sec_content_conf", "email_filter_conf", function(conf_section)
        counter = allocateEmailAccountIdOneProtocol(conf_section, counter, "smtp")
        counter = allocateEmailAccountIdOneProtocol(conf_section, counter, "pop3")
        counter = allocateEmailAccountIdOneProtocol(conf_section, counter, "imap")
    end)
end

function initQQLoginAppid()
    local file = io.open(APPIDS_QQ_LOGIN_PATH, "r")
    if file then
        local line = file:read()
        while line ~= nil do
            local id = tonumber(line)
            if id ~= nil then
                APPIDS_QQ_LOGIN[#APPIDS_QQ_LOGIN + 1] = id
            else
                dbg("illegal appid in qq_account_appid.conf")
            end
            line = file:read()
        end
        file:close()
    else
        dbg("Missing qq_account_appid.conf, cannot get qq login's appid")
    end
end


function generateFileTypeConf(conf_section)
    local idList = {}
    local exname_list = to_list(conf_section["exname_list"])
    for _, exname in pairs(exname_list) do
        idList[#idList + 1] = filetypeMap[exname]
    end
    return idList
end


function generateEmailAccountConf(list)
    local idList = {}

    if list ~= nil then
        for _, account in pairs(list) do
            idList[#idList + 1] = emailaddressMap[account]
        end
    end

    return idList
end

----------------------------------app相关---------------------------------------

function flushAppSecnameIdmap()
    uci_conf.foreach("app_library", "app_list", function(conf_section)
        app_id_map[conf_section[".name"]] = conf_section["app_id"]
    end)

    -- DB是否存在
    local file = io.open(APP_DB_PATH, "rb")
    if file then
        file:close()
    else
        dbg("Missing app.db, ignore the app rules of app provided.")
        return
    end

    local dbenv, dbcon = sql.open_sqlite(APP_DB_PATH);
    local sql_cmd = "SELECT * FROM " .. DB_APP_LIST .. ";"
    local cur, errorString = wrapper.sqlite3_db_execute(dbcon, sql_cmd)
    if cur then
        row = cur:fetch ({}, "a")
        while row ~= nil do
            app_id_map["db_app_" .. row["id"]] = row["id"]
            -- 取下一行
            row = cur:fetch (row, "a")
        end
        wrapper.sqlite3_db_cursor_close(cur)
    end
end

function getAppId(key)
    if app_id_map[key] ~= nil then
        return app_id_map[key]
    end
    return "0"
end

function handlePattern(mode, pattern)
    -- 转换pattern中的特殊字符
    if mode == nil or mode =="" or pattern == nil or pattern == "" then
        dbg("Error: no mode or no pattern")
        return
    end

    if mode == "regex" then
        if string.sub(pattern, 1, 1) == "^" then
            dbg("Error: regex pattern has ^")
            pattern = string.sub(pattern, 2, 0)
        end

        if string.sub(pattern, #pattern, 1) == "$" then
            dbg("Error: regex pattern has $")
            pattern = string.sub(pattern, 0, -2)
        end

        pattern = string.gsub(pattern, "[;]", "\\;")
    elseif mode == "substring" then
        -- 标点符号改为content形式的十六进制
        local i = 0
        while true do
            i = string.find(pattern, "%p", i + 1)
            if i == nil then
                break
            end

            byte = string.byte(string.sub(pattern, i , i + 1))
            byte = string.format("|%02x|", byte)
            pattern = string.sub(pattern, 1, i - 1) .. byte .. string.sub(pattern, i + 1, #pattern)

            i = i + 3
        end
    elseif mode == "tohex" then
        -- 标点符号改为pcre形式的十六进制
        local i = 0
        while true do
            i = string.find(pattern, "%p", i + 1)
            if i == nil then
                break
            end

            byte = string.byte(string.sub(pattern, i , i + 1))
            byte = string.format("\\x%02x", byte)
            pattern = string.sub(pattern, 1, i - 1) .. byte .. string.sub(pattern, i + 1, #pattern)

            i = i + 3
        end
    else
        dbg("Error: unknown mode" .. mode .. " in handlePattern")
    end
    return pattern
end

function generateAppIdRule(conf_section)
    local appid = getAppId(conf_section["parent_table_key"])
    if appid == "0" then
        return
    end

    local mask = "32"
    if conf_section["address"] ~= nil then
        local start_i = string.find(conf_section["address"], "/")
        if start_i then
            mask = string.sub(conf_section["address"], start_i + 1, -1)
        end
    end

    local address = "ANY"
    if conf_section["address"] ~= nil and conf_section["address"] ~= "" and conf_section["address"] ~= "/" and mask ~= "0" then
        address = conf_section["address"]
    end
    local port = "ANY"
    if conf_section["port"] ~= nil and conf_section["port"] ~= "" then
        port = conf_section["port"]
    end
    if string.find(port, ",") ~= nil then
        pfile:write("portvar CUSTOMER_PORTS_" .. port_group_counter .. " [" .. port .. "]\n")
        port = "$CUSTOMER_PORTS_" .. port_group_counter
        port_group_counter = port_group_counter + 1
    end

    -- 用户自定义应用规则优先级appidPriority为6 << 24
    if conf_section["pattern"] == nil or conf_section["pattern"] == "" then
        -- 没有pattern的情况
        if conf_section["protocol"] == "tcp" or conf_section["protocol"] == "tcp_or_udp" then
            sfile:write("appidMark tcp ANY ANY -> " .. address .. " " .. port .. " (msg:\"" .. appid .. "\"; appidPriority:100663296; sid:" .. snort_counter_AppRule .. ";)\n")
            snort_counter_AppRule = snort_counter_AppRule + 1
        end

        if conf_section["protocol"] == "udp" or conf_section["protocol"] == "tcp_or_udp" then
            sfile:write("appidMark udp ANY ANY -> " .. address .. " " .. port .. " (msg:\"" .. appid .. "\"; appidPriority:100663296; sid:" .. snort_counter_AppRule .. ";)\n")
            snort_counter_AppRule = snort_counter_AppRule + 1
        end
    else
        -- 有pattern
        local regex_pattern = handlePattern("regex", conf_section["pattern"])
        local string_pattern = handlePattern("substring", conf_section["pattern"])
        local pattern = ""
        if conf_section["field"] == "general" then
            if conf_section["mode"] == "regex" then
                pattern = "pcre:\"/" .. regex_pattern .. "/\";"
            elseif conf_section["mode"] == "substring" then
                pattern = "content:\"" .. string_pattern .. "\";"
            end
        elseif conf_section["field"] == "http_method" then
            -- 匹配payload开头
            if conf_section["mode"] == "regex" then
                pattern = "pcre:\"/" .. regex_pattern .. "/M\"; "
            elseif conf_section["mode"] == "substring" then
                pattern = "content:\"" .. string_pattern .. "\"; http_method;"
            end
        elseif conf_section["field"] == "http_host" then
            -- 匹配Host:
            if conf_section["mode"] == "regex" then
                pattern = "pcre:\"/" .. regex_pattern .. "/T\"; "
            elseif conf_section["mode"] == "substring" then
                pattern = "content:\"" .. string_pattern .. "\"; http_host;"
            end
        elseif conf_section["field"] == "http_uri" then
            -- 匹配报文开头还没有\r\n的部分
            if conf_section["mode"] == "regex" then
                pattern = "pcre:\"/" .. regex_pattern .. "/U\"; "
            elseif conf_section["mode"] == "substring" then
                pattern = "content:\"" .. string_pattern .. "\"; http_uri;"
            end
        elseif conf_section["field"] == "http_user_agent" then
            -- 匹配User-Agent:
            if conf_section["mode"] == "regex" then
                pattern = "pcre:\"/" .. regex_pattern .. "/N\"; "
            elseif conf_section["mode"] == "substring" then
                pattern = "content:\"" .. string_pattern .. "\"; http_ua;"
            end
        elseif conf_section["field"] == "http_content_type" then
            -- 匹配Content-Type:
            if conf_section["mode"] == "substring" then
                regex_pattern = handlePattern("tohex", conf_section["pattern"])
            end
            pattern = "pcre:\"/[Cc][Oo][Nn][Tt][Ee][Nn][Tt]-[Tt][Yy][Pp][Ee]\\s*\\x3A[^\\r\\n]*" .. regex_pattern .. "/\"; "
        elseif conf_section["field"] == "http_cookie" then
            -- 匹配Cookie:
            if conf_section["mode"] == "regex" then
                pattern = "pcre:\"/" .. regex_pattern .. "/C\"; "
            elseif conf_section["mode"] == "substring" then
                pattern = "content:\"" .. string_pattern .. "\"; http_cookie;"
            end
        else
            -- 不支持的字段
            dbg("Error: invalid field")
            return
        end

        local protocol = "tcp"
        if conf_section["protocol"] == "udp" then
            protocol = "udp"
        end

        if conf_section["direction"] == "origin" or conf_section["direction"] == "any" then
            sfile:write(string.format("appidMark %s ANY ANY -> %s %s (msg:\"%s\"; appidPriority:100663296; flow:to_server; %s sid:%d;)\n", protocol, address, port, appid, pattern, snort_counter_AppRule))
            snort_counter_AppRule = snort_counter_AppRule + 1

            if conf_section["protocol"] == "tcp_or_udp" then
                sfile:write(string.format("appidMark udp ANY ANY -> %s %s (msg:\"%s\"; appidPriority:100663296; flow:to_server; %s sid:%d;)\n", address, port, appid, pattern, snort_counter_AppRule))
                snort_counter_AppRule = snort_counter_AppRule + 1
            end
        end

        if conf_section["direction"] == "reply" or conf_section["direction"] == "any" then
            sfile:write(string.format("appidMark %s %s %s -> ANY ANY (msg:\"%s\"; appidPriority:100663296; flow:to_client; %s sid:%d;)\n", protocol, address, port, appid, pattern, snort_counter_AppRule))
            snort_counter_AppRule = snort_counter_AppRule + 1

            if conf_section["protocol"] == "tcp_or_udp" then
                sfile:write(string.format("appidMark udp %s %s -> ANY ANY (msg:\"%s\"; appidPriority:100663296; flow:to_client; %s sid:%d;)\n", address, port, appid, pattern, snort_counter_AppRule))
                snort_counter_AppRule = snort_counter_AppRule + 1
            end
        end
    end
end


--=========================生成policy_db conf函数=================================

function runCommonIpsetCmd(name, bias)
    local cmd = "ipset create " .. name .. " hash:net -exist;ipset flush " .. name .. ";"
    for i = 1, 127 do
        if bit.band(i, bias) ~= 0 then
            cmd = cmd .. "ipset add " .. name .. " " .. i .. ";"
        end
    end
    sys.fork_call(cmd)
end

function runCommonIpsetCmd2(name, bias1, bias2)
    local cmd = "ipset create " .. name .. " hash:net -exist;ipset flush " .. name .. ";"
    for i = 1, 127 do
        if bit.band(i, bias1) ~= 0 or bit.band(i, bias2) ~= 0 then
            cmd = cmd .. "ipset add " .. name .. " " .. i .. ";"
        end
    end
    sys.fork_call(cmd)
end

function flushCommonIpset()
    runCommonIpsetCmd(APP_ACTION_ID_UPLOAD, 1)
    runCommonIpsetCmd(APP_ACTION_ID_DOWNLOAD, 2)
    runCommonIpsetCmd2(APP_ACTION_ID_UP_OR_DOWNLOAD, 1, 2)
    runCommonIpsetCmd(APP_ACTION_ID_DELETE, 4)
    runCommonIpsetCmd(APP_ACTION_ID_HTTP_POST, 8)
    runCommonIpsetCmd(APP_ACTION_ID_HTTP_PROXY, 16)
    runCommonIpsetCmd(APP_ACTION_ID_HTTP_WEB, 32)

    -- 允许web，禁止download的情况下，download的appactionid ipset
    local cmd = "ipset create " .. APP_SEPIPSET_FOR_DOWNLOAD .. " hash:net -exist;ipset flush " .. APP_SEPIPSET_FOR_DOWNLOAD .. ";"
    for i = 1, 127 do
        if bit.band(i, 2) ~= 0 and bit.band(i, 32) == 0 then
            cmd = cmd .. "ipset add " .. APP_SEPIPSET_FOR_DOWNLOAD .. " " .. i .. ";"
        end
    end
    sys.fork_call(cmd)

    -- 允许download，禁止web的情况下，web的appactionid ipset
    cmd = "ipset create " .. APP_SEPIPSET_FOR_WEB .. " hash:net -exist;ipset flush " .. APP_SEPIPSET_FOR_WEB .. ";"
    for i = 1, 127 do
        if bit.band(i, 32) ~= 0 and bit.band(i, 2) == 0 then
            cmd = cmd .. "ipset add " .. APP_SEPIPSET_FOR_WEB .. " " .. i .. ";"
        end
    end
    sys.fork_call(cmd)
end


-- 创建一个ipset
function createIpset(list, ipsetType)
    if list ~= nil and #list~=0 then
        local name = ipset_name .. ipset_counter
        local cmd = "ipset create " .. name .. " hash:net -exist;ipset flush " .. name .. ";"

        for _, item in pairs(list) do
            cmd = cmd .. "ipset add " .. name .. " " .. item .. ";"
        end
        sys.fork_call(cmd)
        ipset_counter = ipset_counter + 1
        return name
    end
    return NONE
end

function createNumbers(list)
    if list == nil or #list == 0 then
        return NONE
    end

    local numbers = ""
    for _, id in pairs(list) do
        numbers = numbers .. id .. ","
    end
    numbers = string.sub(numbers, 0, -2)
    return numbers
end

-- 创建一个文件 / 加入一个section
function createFile(filename)
    local cmd = "rm -r /tmp/policy_db/app_db/" .. filename .. ";touch /tmp/policy_db/app_db/" .. filename .. ";"
    sys.fork_call(cmd)
end

function saveSection(filename, is_file_created, name, serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, dp_action, ...)
    if is_file_created == false then
        createFile(filename)
    end

    local value = {}
    value[#value + 1] = serviceid
    value[#value + 1] = appid
    value[#value + 1] = appactionid
    value[#value + 1] = filetype
    value[#value + 1] = filesize
    value[#value + 1] = httppostsize
    value[#value + 1] = qqaccount
    value[#value + 1] = receiverid
    value[#value + 1] = senderid
    value[#value + 1] = contentfilterid

    local cp_action = {}
    for _, action in pairs{...} do
        cp_action[#cp_action + 1] = action
    end

    local section = {}
    section["conf_name"] = name
    section["value"] = value
    section["dp_action"] = dp_action
    if #cp_action > 0 then section["cp_action"] = cp_action end

    uci_db:section(filename, db_table_name, filename .. public_counter, section)
    uci_db:commit(filename)
    public_counter = public_counter + 1
end

-- 文件内容过滤
-- 一个conf_section，转换为一个配置文件，一个配置文件里只有一个section
function generateFileContentFilterConf(conf_section)
    local serviceid = ""
    local appid = NONE
    local appactionid = NONE
    local filetype = NONE
    local filesize = NONE
    local httppostsize = NONE
    local qqaccount = NONE
    local receiverid = NONE
    local senderid = NONE
    local contentfilterid = NONE

    -- serviceid
    local app_list = to_list(conf_section["app_list"])
    for _, id in pairs(app_list) do
        serviceid = serviceid .. id .. ","
    end
    serviceid = string.sub(serviceid, 0, -2)

    -- appactionid
    appactionid = ""
    if conf_section["direction"] == "download" then
        appactionid = APP_ACTION_ID_DOWNLOAD
    elseif conf_section["direction"] == "upload" then
        appactionid = APP_ACTION_ID_UPLOAD
    elseif conf_section["direction"] == "both_direction" then
        appactionid = APP_ACTION_ID_UP_OR_DOWNLOAD
    else
        dbg("Error in generateFileContentFilterConf")
        return
    end

    -- -- filetype
    local filetypeList = generateFileTypeConf(conf_section)
    filetype = createIpset(filetypeList)

    if conf_section["action"] == "allow" then
        -- 白名单也需要日志
        saveSection(conf_section[".name"], false, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, CONTENT_LOG)
        -- 配合一条其他全部drop的策略
        dropList = {}
        dropList[#dropList + 1] = FLOW_ID_UNKOWN
        for exname, id in pairs(filetypeMap) do
            local has = false
            for _, knownId in pairs(filetypeList) do
                if id == knownId then
                    has = true
                    break
                end
            end
            if has == false then
                dropList[#dropList + 1] = id
            end
        end
        filetype = createIpset(dropList)
        saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, CONTENT_LOG)
    elseif conf_section["action"] == "alert" then  --alert比accept多一条日志
        saveSection(conf_section[".name"], false, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, CONTENT_LOG, CONTENT_ALERT)
    elseif conf_section["action"] == "block" then
        saveSection(conf_section[".name"], false, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, CONTENT_LOG)
    else
        dbg("Error in generateFileContentFilterConf")
        return
    end
end

-- 内容过滤配置文件
-- patt转二进制，返回16进制字符串
function toBinary(patt)
    local ret = ""
    for i = 1,string.len(patt)  do
        local temp = string.byte(patt,i,i)
        ret = ret..string.upper(string.format("%x",temp))
    end
    return ret
end
-- 利用iconv命令将utf编码转换成gbk编码
function utf2gbk(patt)
    local file = io.open("/tmp/codetrans_utf","w")
    local ret = ""
    io.output(file)
    io.write(patt)
    io.close(file)
    os.execute("iconv -f UTF-8 -t GBK /tmp/codetrans_utf > /tmp/codetrans_gbk")
    file = io.open("/tmp/codetrans_gbk","r")
    io.input(file)
    ret = io.read()
    io.close(file)
    os.execute("rm /tmp/codetrans*")
    return ret
end
--utf8转url编码
function urlEncode(patt)
    patt = string.gsub(patt, "([^%w%.%- ])", function(c) return string.format("%%%02X", string.byte(c)) end)
    return string.gsub(patt, " ", "+")
end

--把新的关键字添加到Array中，或者更新已有关键字的map
function addKeyword(Array, keyword, isRegex, id)
    for _, temp in pairs(Array) do
        if temp["keyword"] == keyword and temp["regex"] == isRegex then
            temp["map"][id] = 1
            return
        end
    end

    Array[#Array + 1] = {
        ["keyword"] = keyword,
        ["regex"] = isRegex,
        ["map"] = {
            [id] = 1
        }
    }
    return
end
--把map转换为写入到snort规则中的msg字段值，由8个32bit整数组成
function map2msg(map, trans)
    local str = ""
    local nums = {
        [0] = 0,[1] = 0,[2] = 0,[3] = 0,
        [4] = 0,[5] = 0,[6] = 0,[7] = 0
    }
    local temp = 0
    local max = 32
    for i = 1,256  do
        if i >= max then
            nums[math.floor(max / 32) - 1] = temp
            temp = 0
            max = max + 32
        end
        if map[i] ~= nil then
            temp = temp + trans[(i-1) % 32]
        end
    end
    nums[math.floor(max / 32)] = temp
    return string.format("%d,%d,%d,%d,%d,%d,%d,%d", nums[7], nums[6], nums[5], nums[4], nums[3],nums[2],nums[1],nums[0])
end
--先把关键字添加到指定Array中，最后再统一生成snort规则
function generateKeywordArray(conf_section, keywordGroupId)
    local keywordList = uci_conf.get_all("sec_content_conf", conf_section["keyword_list"])
    --添加预定义关键字
    if nil ~= keywordList["predefined_regex"] then
        --预定义关键字
        --银行卡和信用卡范围都为招行的银联卡
        local predefined_regex = {
            ["bank_card_number"] = "\\b62(2580|2588|2598|2609|1286|1483|1485|1486|1299)\\d{10}\\b",
            ["credit_card_number"] = "\\b62(2575|2576|2577|2578|2579|2581|2582|8290|8362|8262|5802|5803)\\d{10}\\b",
            ["mobile_phone_number"] = "\\b1(3[0-9]|4[5679]|5[0-3,5-9]|6[267]|7[1235678]|8[0-9]|9[********])\\d{8}\\b",
            ["ID_number"] = "\\b[1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]\\b"
        }
        if conf_section["direction"] == "download" or conf_section["direction"] == "both_direction" then
            for _, k in pairs(keywordList["predefined_regex"]) do
                addKeyword(downloadKeywordArray, predefined_regex[k], 1, keywordGroupId)
            end
        end
        if conf_section["direction"] == "upload" or conf_section["direction"] == "both_direction" then
            for _, k in pairs(keywordList["predefined_regex"]) do
                addKeyword(uploadKeywordArray, predefined_regex[k], 1, keywordGroupId)
            end
        end
    end
    --添加自定义关键字
    if nil ~= keywordList["text_keyword_list"] then
        if conf_section["direction"] == "download" or conf_section["direction"] == "both_direction" then
            for _, k in pairs(keywordList["text_keyword_list"]) do
                addKeyword(downloadKeywordArray, k, 0, keywordGroupId)
            end
        end
        if conf_section["direction"] == "upload" or conf_section["direction"] == "both_direction" then
            for _, k in pairs(keywordList["text_keyword_list"]) do
                addKeyword(uploadKeywordArray, k, 0, keywordGroupId)
            end
        end
    end
    --添加自定义正则表达式
    if nil ~= keywordList["regex_list"] then
        if conf_section["direction"] == "download" or conf_section["direction"] == "both_direction" then
            for _, k in pairs(keywordList["regex_list"]) do
                addKeyword(downloadKeywordArray, k, 1, keywordGroupId)
            end
        end
        if conf_section["direction"] == "upload" or conf_section["direction"] == "both_direction" then
            for _, k in pairs(keywordList["regex_list"]) do
                addKeyword(uploadKeywordArray, k, 1, keywordGroupId)
            end
        end
    end
end

function writeContentFilterRule(flow_dir, msg, content_str, pcre_str)
    local snort_head = "contentfilterMark TCP ANY ANY <> ANY ANY"
    local snort_content = ""
    local snort_pcre = ""

    if content_str ~= nil then
        snort_content = string.format("content:\"|%s|\";", content_str)
    end

    if pcre_str ~= nil then
        snort_pcre = string.format("pcre:\"/%s/\";", pcre_str)
    end

    if "to_server" == flow_dir then
        msg_id = 1
    else
        msg_id = 2
    end

    local snort_body = string.format(" (flow:%s; msg:\"%s,%d\"; sid:%d; file_data; %s %s)", flow_dir, msg, msg_id, snort_counter_ContentFilter + keywordCount, snort_content, snort_pcre)
    keywordCount = keywordCount + 1
    cfile:write(snort_head .. snort_body .. "\n")
end

-- 根据uci生成snort规则文件
function generateContentFilterConfRules()
    -- 如果keywordGroupId为0说明安全策略没有引用内容过滤,防止空文件不生效
    if keywordGroupId == 0 then
        cfile:write("#\n")
        return
    end

    local Head = "contentfilterMark TCP ANY ANY <> ANY ANY"
    local trans = {}
    local t = 1
    for i = 0, 31 do
        trans[i] = t
        t = t * 2
    end
    local flow_dirs = {"to_server", "to_client"}

    for _, flow_dir in ipairs(flow_dirs) do
        local keywordArray
        if flow_dir == "to_server" then
            keywordArray = uploadKeywordArray
        else
            keywordArray = downloadKeywordArray
        end

        for _, temp in pairs(keywordArray) do
            local msg = map2msg(temp["map"], trans)
            local snort_body
            if temp["regex"] == 0 then
                local k = temp["keyword"]
                local temp_k = utf2gbk(k)
                writeContentFilterRule(flow_dir, msg, toBinary(k), nil)

                if(temp_k ~= k) then
                    --GBK
                    writeContentFilterRule(flow_dir, msg, toBinary(temp_k), nil)

                    --URL编码
                    writeContentFilterRule(flow_dir, msg, toBinary(urlEncode(k)), nil)
                end
            else
                writeContentFilterRule(flow_dir, msg, nil, temp["keyword"])
            end
        end
    end

    --使用 optimized_rule 优化PCRE
    local cmd = "cd /tmp/sdb/csedb_shadow/;"
    cmd = cmd .. "optimize_rule -maxsid ********** -input content_filter.rules;"
    cmd = cmd .. "mv optimized_output.rules content_filter.rules"
    sys.exec(cmd)

end
-- 内容过滤 ipset相关
function generateContentFilterConf(conf_section)
    local serviceid = ""
    local appid = NONE
    local appactionid = NONE
    local filetype = NONE
    local filesize = NONE
    local httppostsize = NONE
    local qqaccount = NONE
    local receiverid = NONE
    local senderid = NONE
    local contentfilterid = NONE

    --无引用则返回
    if conf_section["ref"] == nil or conf_section["ref"] == '0' then
        return
    end

    keywordGroupId = keywordGroupId + 1
    -- 创建内容过滤的snort规则文件
    generateKeywordArray(conf_section, keywordGroupId)

    -- serviceid
    local app_list = to_list(conf_section["app_list"])
    for _, id in pairs(app_list) do
        serviceid = serviceid .. id .. ","
    end
    serviceid = string.sub(serviceid, 0, -2)

    contentfilterid = keywordGroupId
    if conf_section["method"] == "allow" then
        saveSection(conf_section[".name"], false, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, CONTENT_FILTER_LOG)
    elseif conf_section["method"] == "block" then
        saveSection(conf_section[".name"], false, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, CONTENT_FILTER_LOG)
    end

end

function oneAppMethodWithThreshold(conf_section, methodName, serviceid, appactionid)
    local appid = NONE
    local filetype = NONE
    local filesize = NONE
    local httppostsize = NONE
    local qqaccount = NONE
    local receiverid = NONE
    local senderid = NONE
    local contentfilterid = NONE

    if conf_section[methodName .. "_action"] == "allow" then
        if conf_section[methodName .. "_threshold_enable"] == "on" then
            if methodName == "http_post" then
                httppostsize = (conf_section[methodName .. "_alert_threshold"] + 1).. "-".. conf_section[methodName .. "_forbidden_threshold"]
                saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, APP_CONTROL_LOG)

                -- **********为32位最大无符号整数
                httppostsize = (conf_section[methodName .. "_forbidden_threshold"] + 1) .. "-**********"
                saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
            else
                -- 因为页面上http-post的阈值单位是B，其他是KB，所以其他要乘以1024
                filesize = ((conf_section[methodName .. "_alert_threshold"] * 1024) + 1).. "-".. (conf_section[methodName .. "_forbidden_threshold"] * 1024)
                saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, APP_CONTROL_LOG)

                -- **********为32位最大无符号整数
                filesize = ((conf_section[methodName .. "_forbidden_threshold"] * 1024) + 1) .. "-**********"
                saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
            end
        -- 选择允许的话添加一个允许
        elseif conf_section[methodName .. "_threshold_enable"] == "off" then
            -- 无阈值，不需要日志
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT)
        end
    elseif conf_section[methodName .. "_action"] == "block" then
        filesize = NONE
        httppostsize = NONE
        saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
    end
end

function oneAppMethodWithoutThreshold(conf_section, methodName, serviceid, appactionid)
    local appid = NONE
    local filetype = NONE
    local filesize = NONE
    local httppostsize = NONE
    local qqaccount = NONE
    local receiverid = NONE
    local senderid = NONE
    local contentfilterid = NONE

    if conf_section[methodName .. "_action"] == "block" then
        saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
    -- 选择允许的话添加一个允许
    elseif conf_section[methodName .. "_action"] == "allow" then
        saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT)
    end
end

-- 应用行为控制
-- 一个conf_section，转换为一个配置文件，一个配置文件里多个section
function generateAppControlFilterConf(conf_section)
    createFile(conf_section[".name"])

    local serviceid = SERVICE_ID_HTTP
    local appid = NONE
    local appactionid = NONE
    local filetype = NONE
    local filesize = NONE
    local httppostsize = NONE
    local qqaccount = NONE
    local receiverid = NONE
    local senderid = NONE
    local contentfilterid = NONE

    oneAppMethodWithThreshold(conf_section, "http_post", serviceid, APP_ACTION_ID_HTTP_POST)
    oneAppMethodWithoutThreshold(conf_section, "http_proxy", serviceid, APP_ACTION_ID_HTTP_PROXY)
    oneAppMethodWithThreshold(conf_section, "http_upload", serviceid, APP_ACTION_ID_UPLOAD)

    -- http浏览网页和http下载
    if conf_section["http_web_action"] == "block" and conf_section["http_download_action"] == "block" then
        oneAppMethodWithoutThreshold(conf_section, "http_web", serviceid, APP_ACTION_ID_HTTP_WEB)
        oneAppMethodWithThreshold(conf_section, "http_download", serviceid, APP_ACTION_ID_DOWNLOAD)
    elseif conf_section["http_web_action"] == "allow" and conf_section["http_download_action"] == "allow" then
        oneAppMethodWithoutThreshold(conf_section, "http_web", serviceid, APP_ACTION_ID_HTTP_WEB)
        oneAppMethodWithThreshold(conf_section, "http_download", serviceid, APP_SEPIPSET_FOR_DOWNLOAD)
    elseif conf_section["http_web_action"] == "allow" and conf_section["http_download_action"] == "block" then
        oneAppMethodWithoutThreshold(conf_section, "http_web", serviceid, APP_ACTION_ID_HTTP_WEB)
        oneAppMethodWithThreshold(conf_section, "http_download", serviceid, APP_SEPIPSET_FOR_DOWNLOAD)
    elseif conf_section["http_web_action"] == "block" and conf_section["http_download_action"] == "allow" then
        oneAppMethodWithoutThreshold(conf_section, "http_web", serviceid, APP_SEPIPSET_FOR_WEB)
        oneAppMethodWithThreshold(conf_section, "http_download", serviceid, APP_ACTION_ID_DOWNLOAD)
    end

    serviceid = SERVICE_ID_FTP

    oneAppMethodWithThreshold(conf_section, "ftp_download", serviceid, APP_ACTION_ID_DOWNLOAD)
    oneAppMethodWithThreshold(conf_section, "ftp_upload", serviceid, APP_ACTION_ID_UPLOAD)
    oneAppMethodWithoutThreshold(conf_section, "ftp_delete", serviceid, APP_ACTION_ID_DELETE)

    serviceid = NONE

    local appidList = {}
    appidList = extendAppid(APPIDS_QQ_LOGIN)
    appid = createIpset(appidList)

    if conf_section["qq_login_action"] == "allow" and #APPIDS_QQ_LOGIN > 0 then
        if conf_section["qq_black_list_enable"] == "on" then
            local qq_black_list = to_list(conf_section["qq_black_list"])
            local list = {}
            for _, account in pairs(qq_black_list) do
                list[#list + 1] = account
            end
            qqaccount = createIpset(list)
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
        else
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT)
        end
    elseif conf_section["qq_login_action"] == "block" and #APPIDS_QQ_LOGIN > 0  then
        if conf_section["qq_white_list_enable"] == "on" then
            local qq_white_list = to_list(conf_section["qq_white_list"])
            local list = {}
            for _, account in pairs(qq_white_list) do
                list[#list + 1] = account
            end
            -- 如果检测出qq账号为0，并且appid为1007，代表正在提交验证码，需要放行
            list[#list + 1] = 0
            qqaccount = createIpset(list)
            qqaccount = "!" .. qqaccount
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
        else
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, APP_CONTROL_LOG)
        end
    elseif #APPIDS_QQ_LOGIN <= 0 then
        dbg("Dont have "..APPIDS_QQ_LOGIN_PATH..", fail to create ipset")
    else
        dbg("Error in generateAppControlFilterConf")
    end
end

function oneEmailProtocol(conf_section, action, protocol)
    local serviceid = NONE
    local appid = NONE
    local appactionid = NONE
    local filetype = NONE
    local filesize = NONE
    local httppostsize = NONE
    local qqaccount = NONE
    local receiverid = NONE
    local senderid = NONE
    local contentfilterid = NONE

    if action == "smtp_action" then
        serviceid = SERVICE_ID_SMTP
    elseif action == "pop3_action" then
        serviceid = SERVICE_ID_POP3
    elseif action == "imap_action" then
        serviceid = SERVICE_ID_IMAP
    else
        dbg("Error in oneEmailProtocol")
        return
    end

    if conf_section[action] == "allow" then
        if conf_section[protocol .. "_sender_black_list_enable"] == "on" then
            receiverid = NONE
            local sender_black_list = to_list(conf_section[protocol .. "_sender_black_list"])
            sender_black_list = generateEmailAccountConf(sender_black_list)
            senderid = createNumbers(sender_black_list)

            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, EMAIL_LOG)
        end

        if conf_section[protocol .. "_receiver_black_list_enable"] == "on" then
            local receiver_black_list = to_list(conf_section[protocol .. "_receiver_black_list"])
            receiver_black_list = generateEmailAccountConf(receiver_black_list)
            receiverid = createNumbers(receiver_black_list)
            senderid = NONE

            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, EMAIL_LOG)
        end

        -- 选择允许的话添加一个允许，默认允许不需要日志
        receiverid = NONE
        senderid = NONE
        saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT)
    elseif conf_section[action] == "block" then
        receiverid = NONE
        senderid = NONE
        else_receiverid = NONE
        else_senderid = NONE

        if conf_section[protocol .. "_receiver_white_list_enable"] == "on" then
            -- 白名单列表
            local receiver_white_list = to_list(conf_section[protocol .. "_receiver_white_list"])
            receiver_white_list = generateEmailAccountConf(receiver_white_list)
            receiverid = createNumbers(receiver_white_list)

            -- 非白名单列表
            local dropList = {}
            dropList[#dropList + 1] = FLOW_ID_UNKOWN
            for _, id in pairs(emailaddressMap) do
                local has = false
                for _, knownId in pairs(receiver_white_list) do
                    if id == knownId then
                        has = true
                    end
                end
                if has == false then
                    dropList[#dropList + 1] = id
                end
            end
            else_receiverid = createNumbers(dropList)
        end

        if conf_section[protocol .. "_sender_white_list_enable"] == "on" then
            -- 白名单列表
            local sender_white_list = to_list(conf_section[protocol .. "_sender_white_list"])
            sender_white_list = generateEmailAccountConf(sender_white_list)
            senderid = createNumbers(sender_white_list)

            -- 非白名单列表
            local dropList = {}
            dropList[#dropList + 1] = FLOW_ID_UNKOWN
            for _, id in pairs(emailaddressMap) do
                local has = false
                for _, knownId in pairs(sender_white_list) do
                    if id == knownId then
                        has = true
                    end
                end
                if has == false then
                    dropList[#dropList + 1] = id
                end
            end
            else_senderid = createNumbers(dropList)
        end

        -- 同时配置了收发件人白名单，同时处在收发件人白名单才能收发
        if senderid ~= NONE and receiverid ~= NONE then
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, EMAIL_LOG)

            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, else_receiverid, else_senderid, contentfilterid, DROP, EMAIL_LOG)
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, else_senderid, contentfilterid, DROP, EMAIL_LOG)
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, else_receiverid, senderid, contentfilterid, DROP, EMAIL_LOG)

        -- 没有配置白名单，全部阻断
        elseif senderid == NONE and receiverid == NONE then
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, DROP, EMAIL_LOG)

        -- 只配置了收件人,收件人可以收取任意对象
        elseif senderid == NONE then
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, EMAIL_LOG)
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, else_receiverid, senderid, contentfilterid, DROP, EMAIL_LOG)

        -- 只配置了发件人,表示发件人发给任意对象
        else
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, senderid, contentfilterid, ACCEPT, EMAIL_LOG)
            saveSection(conf_section[".name"], true, conf_section["name"], serviceid, appid, appactionid, filetype, filesize, httppostsize, qqaccount, receiverid, else_senderid, contentfilterid, DROP, EMAIL_LOG)
        end
    end
end

-- 邮件内容过滤
-- 一个conf_section，转换为一个配置文件，一个配置文件里多个section
function generateEmailFilterConf(conf_section)
    createFile(conf_section[".name"])

    oneEmailProtocol(conf_section, "smtp_action", "smtp")
    oneEmailProtocol(conf_section, "imap_action", "imap")
    oneEmailProtocol(conf_section, "pop3_action", "pop3")
end

--=========================主接口函数=================================
function db_creator()
    -- 设置global_define
    local cmd = "mkdir /tmp/policy_db;mkdir /tmp/sdb;mkdir /tmp/sdb/csedb_shadow;"
    cmd = cmd .. "rm -rf /tmp/policy_db/app_db;mkdir /tmp/policy_db/app_db;touch /tmp/policy_db/app_db/global_define;"
    sys.fork_call(cmd)
    local tuple_name = {}
    local value_type = {}
    local global = {}

    table.insert(tuple_name, "serviceid")
    table.insert(tuple_name, "appid")
    table.insert(tuple_name, "appactionid")
    table.insert(tuple_name, "filetype")
    table.insert(tuple_name, "filesize")
    table.insert(tuple_name, "httppostsize")
    table.insert(tuple_name, "qqaccount")
    table.insert(tuple_name, "receiverid")
    table.insert(tuple_name, "senderid")
    table.insert(tuple_name, "contentfilterid")

    table.insert(value_type, "numbers")
    table.insert(value_type, "ipset")
    table.insert(value_type, "ipset")
    table.insert(value_type, "ipset")
    table.insert(value_type, "section")
    table.insert(value_type, "section")
    table.insert(value_type, "ipset")
    table.insert(value_type, "numbers")
    table.insert(value_type, "numbers")
    table.insert(value_type, "numbers")

    global["tuple_name"] = tuple_name
    global["value_type"] = value_type

    uci_db:section("global_define", "global", "global", global)
    uci_db:commit("global_define")

    -- 设置snort输出文件
    sfile = io.open("/tmp/sdb/csedb_shadow/app_customer.rules", "w")
    pfile = io.open("/tmp/sdb/csedb_shadow/app_customer_port.conf", "w")
    ffile = io.open("/tmp/sdb/csedb_shadow/file_filter_suffix", "w")
    efile = io.open("/tmp/sdb/csedb_shadow/email_address", "w")
    cfile = io.open("/tmp/sdb/csedb_shadow/content_filter.rules", "w")

    -- 安全配置文件转换==================================================================
    -- 设置app_conf_xxx
    flushCommonIpset()
    allocateFileTypeId()
    allocateEmailAccountId()
    initQQLoginAppid()

    uci_conf.foreach("sec_content_conf", "file_content_filter_conf", function(conf_section)
        generateFileContentFilterConf(conf_section)
    end)
    uci_conf.foreach("sec_content_conf", "app_control_filter_conf", function(conf_section)
        generateAppControlFilterConf(conf_section)
    end)
    uci_conf.foreach("sec_content_conf", "email_filter_conf", function(conf_section)
        generateEmailFilterConf(conf_section)
    end)
    uci_conf.foreach("sec_content_conf", "content_filter_conf", function(conf_section)
        generateContentFilterConf(conf_section)
    end)
    generateContentFilterConfRules()

    -- 用户自定义应用识别规则转换===========================================================
    -- 设置app_list | section[".name"] -> appid的映射
    flushAppSecnameIdmap()

    uci_conf.foreach("app_library", "app_rule", function(conf_section)
        generateAppIdRule(conf_section)
    end)
    -- 没有自定义应用时写入"#"
    if snort_counter_AppRule == snort_counter_AppRule_start then
        sfile:write("#")
    end

    sfile:close()
    pfile:close()
    ffile:close()
    efile:close()
    cfile:close()
end

function db_destroy()
    local cmd = "rm -rf /tmp/policy_db/app_db;"
    sys.fork_call(cmd)
end

--=================================================================
local operation = arg[1]
if operation == "create" then
    db_creator()
elseif operation == "destroy" then
    db_destroy()
end
--=================================================================
