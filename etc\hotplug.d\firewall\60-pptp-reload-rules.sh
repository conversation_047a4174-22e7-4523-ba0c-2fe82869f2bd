#!/bin/sh

. /lib/firewall/fw.sh
. /lib/zone/zone_api.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/access_ctl/core_acl.sh

for dir in `ls ${pppox_pptp_server_path} 2>/dev/null`;do
	if [ -d ${pppox_pptp_server_path}/$dir ];then
		local path=${pppox_pptp_server_path}/${dir}/config
		[ ! -e $path ] && continue
		local bindif=$(uci -c ${path} get ${pppox_configname}.@pns[0].bindif 2>/dev/null)
		local enable=$(uci -c ${path} get ${pppox_configname}.@pns[0].enable 2>/dev/null)
	
		if [ $enable = '1' -o $enable = 'on' ];then
			local devices=$(zone_get_effect_devices $bindif)
			local local_service_chain=local_service_rule
			for iface in $devices;do
				[ -n "$iface" -a "$iface" != "loopback" ] && {
					fw_check  "filter" "local_service_rule" "-i ${iface} -p tcp -m tcp --dport 1723 -j ACCEPT"
				    [ x$? == x0 ] && fw add 4 f $local_service_chain ACCEPT $ { -i ${iface} -p tcp --dport 1723 }
				}
			done
		fi
	fi
done

