#!/bin/sh
#author lijun4


get_inface_name()
{
	config_get interface $1 if_list
	interfaces="$interface $interfaces"
}

check_inface_reset_static_route()
{
	local interfaces

	config_load network

	config_foreach get_inface_name user_route
	config_foreach get_inface_name user_route_ipv6

	[ -n "$interfaces" ] || return
	for inface in $interfaces;do
		if [ "$inface" == "$IFACE" ] || [ "${inface}6" == "$IFACE" ] || [ "${inface}" == "---" ];then
			/etc/init.d/static_route restart
			return
		fi
	done
}

{
flock -x 220
# dev 与 iface 的映射关系不在强依赖于zone，改为环境变量传值
case "$ACTION" in
		ifup)
		{
			# 重新维护隧道接口配置的静态路由条目
			{
				[ -n "$IFACE" ] && check_inface_reset_static_route
			}
		};;
		ifdown)
			# 需要更新静态路由状态
			{
				[ -n "$IFACE" ] && check_inface_reset_static_route
			}
		;;
		ifupdate)
			# do nothing
		;;
esac
flock -u 220
} 220<>/tmp/ipsec_220.lock