#
# 
#
#	string - 0-253 octets
#	ipaddr - 4 octets in network byte order
#	integer - 32 bit value in big endian order (high byte first)
#	date - 32 bit value in big endian order - seconds since
#					00:00:00 GMT,  Jan.  1,  1970
#
#	Enumerated values are stored in the user file with dictionary
#	VALUE translations for easy administration.
#
#	Example:
#
#	ATTRIBUTE	  VALUE
#	---------------   -----
#	Framed-Protocol = PPP
#	7		= 1	(integer encoding)
#

#
#	Following are the proper new names. Use these.
#

#include main dictionary
$INCLUDE dictionary.rfc2865
$INCLUDE dictionary.rfc2866
$INCLUDE dictionary.rfc2867
$INCLUDE dictionary.rfc2868
$INCLUDE dictionary.rfc2869
$INCLUDE dictionary.rfc3162
$INCLUDE dictionary.rfc3576
$INCLUDE dictionary.rfc3580
$INCLUDE dictionary.rfc4072
$INCLUDE dictionary.rfc4372
$INCLUDE dictionary.rfc4603
$INCLUDE dictionary.rfc4675
$INCLUDE dictionary.rfc4679
$INCLUDE dictionary.rfc4818
$INCLUDE dictionary.rfc4849
$INCLUDE dictionary.rfc5176
$INCLUDE dictionary.rfc5447
$INCLUDE dictionary.rfc5580
$INCLUDE dictionary.rfc5607
$INCLUDE dictionary.rfc5904


#include vendor dictionary
$INCLUDE dictionary.microsoft
$INCLUDE dictionary.tplink