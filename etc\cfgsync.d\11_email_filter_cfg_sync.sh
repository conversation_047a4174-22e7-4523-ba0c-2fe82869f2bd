#!/usr/bin/lua

--[[
FW6600v1第二版软件的邮件过滤功能支持不同协议配置不同的收发件人黑白名单，第一版软件三个协议使用同一个收发件人黑白名单，需要在启动时实现配置兼容
]]

local dbg 	  = require "luci.torchlight.debug"
local uci 	  = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"
local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()

function copyOneProtocol(conf_section, protocol, has_changed)
    if conf_section[protocol .. "_method"] == "accept" then
        local option = conf_section["has_sender_black_list"]
        local list = conf_section["sender_black_list"]
        if option ~= nil then
            if option == "enable" and list ~= nil then
                uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_black_list_" .. protocol, "enable")
                uci_r:set("sec_content_conf", conf_section[".name"], "sender_black_list_" .. protocol, list)
            else
                uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_black_list_" .. protocol, "disable")
            end

            uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_white_list_" .. protocol, "disable")

            uci_r:delete("sec_content_conf", conf_section[".name"], "has_sender_black_list")
            uci_r:delete("sec_content_conf", conf_section[".name"], "sender_black_list")

            has_changed = 1
        end

        option = conf_section["has_receiver_black_list"]
        list = conf_section["receiver_black_list"]
        if option ~= nil then
            if option == "enable" and list ~= nil then
                uci_r:set("sec_content_conf", conf_section[".name"], "has_receiver_black_list_" .. protocol, "enable")
                uci_r:set("sec_content_conf", conf_section[".name"], "receiver_black_list_" .. protocol, list)
            else
                uci_r:set("sec_content_conf", conf_section[".name"], "has_receiver_black_list_" .. protocol, "disable")
            end

            uci_r:set("sec_content_conf", conf_section[".name"], "has_receiver_white_list_" .. protocol, "disable")

            uci_r:delete("sec_content_conf", conf_section[".name"], "has_receiver_black_list")
            uci_r:delete("sec_content_conf", conf_section[".name"], "receiver_black_list")

            has_changed = 1
        end
    elseif conf_section[protocol .. "_method"] == "drop" then
        local option = conf_section["has_sender_white_list"]
        local list = conf_section["sender_white_list"]
        if option ~= nil then
            if option == "enable" and list ~= nil then
                uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_white_list_" .. protocol, "enable")
                uci_r:set("sec_content_conf", conf_section[".name"], "sender_white_list_" .. protocol, list)
            else
                uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_white_list_" .. protocol, "disable")
            end

            uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_black_list_" .. protocol, "disable")

            uci_r:delete("sec_content_conf", conf_section[".name"], "has_sender_white_list")
            uci_r:delete("sec_content_conf", conf_section[".name"], "sender_white_list")

            has_changed = 1
        end

        option = conf_section["has_receiver_white_list"]
        list = conf_section["receiver_white_list"]
        if option ~= nil then
            if option == "enable" and list ~= nil then
                uci_r:set("sec_content_conf", conf_section[".name"], "has_receiver_white_list_" .. protocol, "enable")
                uci_r:set("sec_content_conf", conf_section[".name"], "receiver_white_list_" .. protocol, list)
            else
                uci_r:set("sec_content_conf", conf_section[".name"], "has_receiver_white_list_" .. protocol, "disable")
                uci_r:set("sec_content_conf", conf_section[".name"], "has_sender_black_list_" .. protocol, "disable")
            end

            uci_r:set("sec_content_conf", conf_section[".name"], "has_receiver_black_list_" .. protocol, "disable")

            uci_r:delete("sec_content_conf", conf_section[".name"], "has_receiver_white_list")
            uci_r:delete("sec_content_conf", conf_section[".name"], "receiver_white_list")

            has_changed = 1
        end
    end

    return has_changed
end

local function email_filter_sync()
    local has_changed = 0
	uci_r:load(conf_dir)

	uci_r.foreach("sec_content_conf", "email_filter_conf", function(conf_section)
		-- 判断是否为旧配置项目
		-- 是则重复拷贝到三个协议
        has_changed = copyOneProtocol(conf_section, "smtp", has_changed)
        has_changed = copyOneProtocol(conf_section, "pop3", has_changed)
        has_changed = copyOneProtocol(conf_section, "imap", has_changed)
	end)

    if has_changed == 1 then
    	uci_r:commit("sec_content_conf")
    	cfgsync.set_config_changed()
    end

	return
end

email_filter_sync()
