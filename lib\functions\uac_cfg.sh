#!/bin/sh

. /lib/functions/cp_specify_cfg.sh

UAC_CFG_DIR="/tmp/ac_config"
UAC_CFG_BEGIN_TO_COPY_FLAG="/tmp/ac_config/usr_save_request"
UAC_CFG_READ_TO_COPY_FLAG="/tmp/ac_config/uac_save_ok"
UAC_CFG_TMP_CFG_PATH="/tmp/ac_config/"
UAC_CFG_DB_DIR="/etc/nouci_config/dbs/"

uac_cfg_copy_init()
{
	rm -rf $UAC_CFG_DIR
	mkdir -p $UAC_CFG_DIR
	touch "$UAC_CFG_BEGIN_TO_COPY_FLAG"
}

uac_cfg_copy_action()
{
	local dst_path="$1"
	if [ ! -d "$dst_path" ]; then
		return 1
	fi

	if [ -f "$UAC_CFG_READ_TO_COPY_FLAG" ];then
		local uac_cfg_file=`ls  $UAC_CFG_TMP_CFG_PATH`
		for file in $uac_cfg_file;do
			if [ -f "$UAC_CFG_TMP_CFG_PATH$file" ];then
				#回调需要明确几个地方：
				#	1、文件拷贝都封装固定接口,区分uci和非uci
				#	2、参数1为待拷贝文件(夹)路径
				#	3、参数2是待拷贝文件（夹）作为运行时配置路径
				#	4、参数3目标路径
				cp_non_uci_sepcific_cfg_to_tmp_cb "$UAC_CFG_TMP_CFG_PATH$file" "/etc/nouci_config/dbs/$file" "$dst_path"
			fi
		done
		rm -rf $UAC_CFG_DIR
		return 0
	fi
	return 1
}

if [ "$1" == "copy_init" ];then
        uac_cfg_copy_init
elif [ "$1" == "copy_action" ];then
	uac_cfg_copy_action "$2"
	return $?
fi
