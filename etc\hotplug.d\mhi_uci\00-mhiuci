#!/bin/sh
: '
 Copyright (c) 2019, The Linux Foundation. All rights reserved.

 Permission to use, copy, modify, and/or distribute this software for any
 purpose with or without fee is hereby granted, provided that the above
 copyright notice and this permission notice appear in all copies.

 THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
'

DEVICE="mhi_0306_00.01.00_pipe_14"

if [ "$ACTION" = add ]; then
	if [ "$DEVICENAME" == "$DEVICE" ]; then
		cat /dev/$DEVICENAME &
		if [ $? -eq 0 ]; then
			printf "Sending udev events over mhi-uci" \
								> /dev/console
		else
			printf "Failed in sending udev events over  mhi-uci, check pci link and qrtr etc " \
								> /dev/console
		fi
	fi
fi

