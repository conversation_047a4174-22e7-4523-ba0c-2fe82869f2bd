#!/bin/sh

#the file that user info stored
#do not change this line

get_userinfo()
{
	( flock -x 86
	local path=/tmp/pppoe/server/pid/
	local userinfofile=/tmp/pppoe/server/pppoeserver_sessions
	echo > ${userinfofile}
	find ${path} -type f -exec cat {} \;>${userinfofile}
	) 86<>/tmp/.pppoeserver_userinfo_lock
}

ipset_modify()
{
        local iface=`uci get pppoe_server.setting.service_if`
        local ifaceset=${iface}"_IFACES"
        ipset ${1} ${ifaceset} ${2} -exist
}
