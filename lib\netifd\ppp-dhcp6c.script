#!/bin/sh

. /lib/functions.sh
. /lib/netifd/netifd-proto.sh

IFNAME=$ifname
INTERFACE=$interface
STATE=$new_dhcpc_state

ppp_dir="/tmp/pppv6/$INTERFACE"
ppp_up="$ppp_dir/ppp_up"

[ -z "$INTERFACE" ] && exit 1
[ -z "$IFNAME" ] && exit 1

reload_interface() {
	config_get prefix_if $1 prefix_if
	if [ -n "$prefix_if" ];then
		. /lib/zone/zone_api.sh
		local iface=$2
		local uci_iface=${iface}
		#convert ifacev6 to base interface
		local ifacev6_base=`echo "$iface" |grep -E "[6]+$"`
		[ -n "$ifacev6_base" ] && uci_iface=${iface%6*}
		local userif=`zone_iface_to_userif ${uci_iface} 2>/dev/null`
		if [ -n "$userif" -a "$prefix_if" == "$userif" ];then
			local old_prefix=`uci get network.$1.prefix 2>/dev/null`
			if [ "$3" != "$old_prefix" ];then
				# update lan and vlan prefix
				uci set network.$1.prefix="$3"
				uci set network.$1.ip6assign="64"
				uci set network.$1.ip6prefix="$3/64"
				uci commit
				/etc/init.d/network reload

				# update slaac/dhcp prefix
				lua /lib/dhcpd/dhcp6_notify_interface_prefix.sh $1 $3 64
				lua /lib/slaac/slaac_notify_interface_prefix.sh $1 $3 64
			fi
		fi
	fi
}

multilan_reload() {
	config_load network
	config_foreach reload_interface interface "$1" "$2"
}

setup_interface () {
	local remoteip
	local mbit=1
	local i=0

	local dns=`uci get network.$INTERFACE.dns`
	local dns6=""
	[ -n "$dns" ] && {
		for val in $dns; do
			local rslt=`echo $val | grep ":"`
			if [ -n "$rslt" ]; then
				[ -n "$dns6" ] && dns6="$dns6 $val" || dns6="$val"
			fi
		done
	}
	local ip_config=`uci get network.$INTERFACE.ip_config`
	local proto=`uci get network.$INTERFACE.proto`

	proto_init_update "$IFNAME" 1
	proto_set_keep 1

	[ $ip_config != "static" ] && {
		# using SLAAC or DHCPv6 is decided by user
		mbit=`cat /proc/sys/net/ipv6/conf/$IFNAME/ndisc_mbit`
		[ $mbit -eq 0 -o "$ip_config" == "slaac" ] && [ "$ip_config" == "slaac" -o "$ip_config" == "auto" ] && {
			while read line
			do
				[ $IFNAME != ${line##* } ] && continue
				[ "fe80" = ${line:0:4} ] && continue
				element=${line:0:32}
				ip6addr=${element:0:4}:${element:4:4}:${element:8:4}:${element:12:4}
				ip6addr=$ip6addr:${element:16:4}:${element:20:4}:${element:24:4}:${element:28:4}
				new_ip6addr=$ip6addr
			done </proc/net/if_inet6

			[ -z "$new_ip6addr" ] && {
				proto_notify_error "$INTERFACE" "NO ADDRESS"
				sleep 3
				proto_setup_failed "$INTERFACE"
				return
			}
		}

		if [ -n "$new_ip6addr" ]; then
			proto_add_ipv6_address "$new_ip6addr" "64"
		elif [ -z "$new_ip6prefix" ]; then
			deconfig_interface
			return
		fi

		local ip6gw=`cat /proc/sys/net/ipv6/conf/$IFNAME/default_gateway`
		[ ${#ip6gw} -ne 0 ] && proto_add_ipv6_route "::" 0 "$ip6gw"

		[ "$proto" == "pppoev6" -a -f "$ppp_up" ] && {
			while read line
			do
				[ "$i" == "0" -a "$line" != "none" ] && proto_add_ipv4_address "$line" "32"
				[ "$i" == "1" -a "$line" != "none" ] && proto_add_ipv4_route "0.0.0.0" "0" "$line"
				[ "$i" == "2" -a "$line" != "none" ] && proto_add_ipv6_address "$line" "64"
				[ "$i" == "3" -a "$line" != "none" ] && {
					remoteip="$line"
					proto_add_ipv6_route "::" "0" "$line"
				}

				i=`expr $i + 1`
			done < "$ppp_up"
			rm -rf "$ppp_dir"
		}
	}

	[ -n "$dns6" ] && new_domain_name_servers=$dns6
	[ -n "$new_domain_name_servers" ] && {
		DNS2=${new_domain_name_servers##*[, ]}
		DNS1=${new_domain_name_servers%%[, ]*}
		[ -n "$DNS1" ] && {
			proto_add_dns_server "$DNS1"
		}
		[ -n "$DNS1" -a -n "$DNS2" -a "$DNS1" != "$DNS2" ] && {
			proto_add_dns_server "$DNS2"
		}
	}

	if [ -n "$new_ip6prefix" ]; then
		mkdir -p /tmp/dhcp6c/$INTERFACE
		echo "$new_ip6prefix" > /tmp/dhcp6c/$INTERFACE/prefix.info
	fi

	proto_send_update "$INTERFACE"

	# wait interface to setup, and set default gateway
	sleep 3
	local gw_route=`route -A inet6 | grep ::/0`
	[ -z ${gw_route} ] && {
		local gateway=`cat /proc/sys/net/ipv6/conf/$IFNAME/default_gateway`
		[ ${#gateway} -ne 0 ] && route -A inet6 add default gw "$gateway" dev "$IFNAME" metric 1024
	}

	# reload multilan
	if [ -n "$new_ip6prefix" ]; then
		multilan_reload "$INTERFACE" "$new_ip6prefix"
	fi

	# send hotplug
	env -i ACTION=ifup INTERFACE=$INTERFACE  /sbin/hotplug-call iface6
}

deconfig_interface() {
	proto_init_update "$IFNAME" 0
	proto_send_update "$INTERFACE"
}

case "$STATE" in
	"2"|"3")
		# 3 - DHCP6_REQUEST reply, 2 - DHCP6_INFO reply
		setup_interface
	;;

	"6")
		deconfig_interface
	;;
esac

exit 0
