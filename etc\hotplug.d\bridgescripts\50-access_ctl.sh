#!/bin/sh

local configdir="/etc/config"
local file="access_ctl"
local tmpdir="/tmp/access_ctl2"

[ ! -e $tmpdir ] && mkdir -p $tmpdir

cp -f $configdir/$file $tmpdir/$file

case ${ACTION} in
	ADDBR)
		[ -n "${CONFIF2}" -a -n "${BRIF}" ] && {
			cnt=`sed -rn "/list\s+zone\s+'*${CONFIF2}'*/p"  ${tmpdir}/$file | wc -l`
			[ $cnt == '0' ] && return
			sed -ri "s/list\s+zone\s+'*${CONFIF2}'*/list zone '${BRIF}'/g"  ${tmpdir}/$file

			cntx=`uci get network.${CONFIF2}.t_reference`
			cntx=${cntx:-0}
			let cntx=cntx-cnt
			[ ${cntx} -lt 0 ] && cntx=0
			uci set network.${CONFIF2}.t_reference=${cntx}
			uci commit network

			cntx=`uci get network.${BRIF}.t_reference`
			cntx=${cntx:-0}
			let cntx=cnt+cntx
			uci set network.${BRIF}.t_reference=${cntx}
			uci commit network

		}
	;;
	UPDBR)
	;;
	DELBR)
	;;
	*)
	;;
esac

cp -f $tmpdir/$file  $configdir/$file