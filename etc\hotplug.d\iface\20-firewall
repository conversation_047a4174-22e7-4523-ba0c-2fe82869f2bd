#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

[ "$DEVICE" == "lo" ] && exit 0

. /lib/functions.sh
. /lib/firewall/core.sh
. /lib/zone/zone_api.sh
. /lib/firewall/core_tpfirewall.sh

# handle ipv6
case "$ACTION" in
	ifup)
		fw_event_interface6 "$INTERFACE" add "$DEVICE" &
	;;
	ifdown)
		fw_event_interface6 "$INTERFACE" del "$DEVICE"
	;;
esac

# handle ipv4
if [ -z $INTERFACE ]; then
	[ -z $DEVICE ] && exit 0
	INTERFACE=$(zone_get_iface_bydev $DEVICE)
fi

[ -z $INTERFACE ] && exit 0
#echo "INTERFACE=$INTERFACE,DEVICE=$DEVICE" >> /tmp/fw.log

fw_init
#fw_is_loaded || exit 0

case "$ACTION" in
	ifup)
		fw_event_interface "$INTERFACE" add "$DEVICE" &
	;;
	ifdown)
		fw_event_interface "$INTERFACE" del "$DEVICE"

	;;
	ifupdate)
		# do nothing
	;;
esac
