#! /bin/sh
#
# <EMAIL>
# 2022/5/11

OPKG_ERROR_LOCK_FAILED=1
OPKG_ERROR_DEFAULT=255

# purpose: 解压插件包, 作为一个通用接口可以给其他模块使用。
# param:   plugin_bin  插件文件
plugin_unpack()
{
    local plugin_bin="$1"
    local test_count=10
    local ret=0
    while [ "$test_count" -gt 0 ];
    do
        ret_msg=$(opkg install "$plugin_bin" 2>&1 1>/dev/null)
        if [ -z "$ret_msg" ]; then
            break
        elif [ -z "$(echo $ret_msg | grep "Could not lock")" ]; then
            echo "opkg install failed ret:${ret_msg}"
            ret=${OPKG_ERROR_DEFAULT}
            break
        fi

        let test_count=test_count-1
        sleep 3
    done

    if [ "${test_count}" -eq 0 ]; then
        ret=${OPKG_ERROR_LOCK_FAILED}
    fi

    return $ret
}


