#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat.init
# brief    
# author   <PERSON> chen
# version  1.0.0
# date     20Apr15
# histry   arg 1.0.0, 20Apr15, <PERSON> chen, Create the file. 

START=46

NAT_LIBDIR=/lib/nat
nat() {
	. $NAT_LIBDIR/nat_core.sh
	nat_$1 $2
}

start() {
	nat start $1
}

stop() {
	nat stop $1
}

restart() {
	nat restart $1
}

reload() {
	nat reload $1
}
