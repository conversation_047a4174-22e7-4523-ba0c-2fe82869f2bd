#!/bin/sh

[ "$ACTION" == "WANMOD" ] && exit 0

mkdir -p /tmp/.wanhook
touch /tmp/.wanhook/wanhook_finish

echo "WAN HOOK finish" >> /dev/console

if [ -f /tmp/.vpnhook/vpnhook_finish ]; then {

	cfgSave -s

	echo "VPN HOOK finish" >> /dev/console
	echo "Begin to reboot" >> /dev/console
	sleep 3
	reboot
}
else
	if [ -f /tmp/.vpnhook/vpnhook_begin ]; then {
		sleep 2
	}
	else {
		cfgSave -s
		echo "No VPN HOOK Event" >> /dev/console
		echo "Begin to reboot" >> /dev/console
		sleep 3
		reboot
	}
	fi
fi
