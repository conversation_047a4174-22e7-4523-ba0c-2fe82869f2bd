#!/bin/sh

_delete_rule()
{
	local cfg="$1"
	local iface="$2"
	local flag=0
	local interfaces

	slave_iface=$(uci_get line_backup "$cfg" slave_if)
	master_ifaces=`uci get line_backup.$cfg.master_if`

	if [ "$iface" == "$slave_iface" ];then
		flag=1
	fi

	if [ "$flag" -eq 0 ]; then
		for m_iface in $master_ifaces
		do
			if [ "$iface" == "$m_iface" ];then
				flag=1
				break
			fi
		done
	fi


	[ "$flag" -eq 1 ] && {
		uci delete line_backup.$cfg
		uci_commit line_backup
    }
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
		    interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				config_load line_backup
				config_foreach _delete_rule rule $element
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac