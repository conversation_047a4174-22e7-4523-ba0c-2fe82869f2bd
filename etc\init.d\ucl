#!/bin/sh /etc/rc.common
# Copyright (C) 2006 OpenWrt.org

START=99

start() {
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "lua /usr/lib/lua/ucl/ucl_helper.lua ucl_change", "wait_time": 10, "type": "online", "action":"add" }' &> /dev/null

	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo  2","wait_time":0,"type":"feature","action":"add"}' &> /dev/null
}

stop() {
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "lua /usr/lib/lua/ucl/ucl_helper.lua ucl_change", "wait_time": 10, "type": "online", "action":"delete" }' &> /dev/null

	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo  2","wait_time":0,"type":"feature","action":"delete"}' &> /dev/null
}

restart()
{
	stop
	start
}

reload()
{
	restart
}
