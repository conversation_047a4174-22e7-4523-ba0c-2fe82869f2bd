#!/bin/sh
. /lib/firewall/fw.sh
. /lib/zone/zone_api.sh

local config_file="access_ctl"
# options of access_ctl
local zone=""
local service=""
local src=""
local dest=""
local name=""
local policy=""
local position=0

ACL_LOG()
{
  t="`date +"%Y-%M-%d %H:%m:%S"`"
  #echo "$t ---> $1" >> /tmp/acl_rule.log
  echo "access_func $t ---> $1"
}

fw_acl_ipset_check()
{
  #echo "argv1=$1"
  local ret=$(ipset -L|grep $1 | awk '{print $2}')

  #[ "$ret" == "$1" ] && return 0||return 1
  return 0

}

fw_check() {
  local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
  [ -n "$ret" ] && return 0||return 1
}

# reversed-order,have to find the rule before the given rulename,then insert it 
get_other_info(){
	local target_name=$1
	ACL_LOG "start get other info for rule $target_name"
	lua /lib/access_ctl/acl_timeobj.lua $target_name
	local line_number=0
	while read line
	do
		line_number=$(($line_number+1))
		[ $line_number -eq 1 ] && zone=${line}
		[ $line_number -eq 2 ] && service=${line}
		[ $line_number -eq 3 ] && src=${line}
		[ $line_number -eq 4 ] && dest=${line}
		[ $line_number -eq 5 ] && policy=${line}
		[ $line_number -eq 6 ] && position=${line}
	done < /tmp/acl_rule.tmp
	[ "$policy" == "ACCEPT" ] && policy="RETURN"
	ACL_LOG "stop get other info for rule $target_name"
}

delete_rule()
{
  local rulename=$1
  local rulenumber=$(iptables -w -nvL access_ctl --line-numbers | grep "\<${rulename}_1acl\>" | wc -l)
  if [ "x${rulenumber}" == "x0" ]; then
    # no rule exist in iptables
    ACL_LOG "no rule in iptables for $rulename"
    return 
  fi
  local firstposition=$(iptables -w -nvL access_ctl --line-numbers | grep "\<${rulename}_1acl\>" | head -1 | awk '{print $1}')
  ACL_LOG "position in iptables for $rulename is $firstposition"
  for i in `seq 1 ${rulenumber}`; do
    iptables -w -D access_ctl ${firstposition}
  done
}

add_rule(){
	local rulename=$1
	local is_delete=$2

	# according to rulename,get the item of
	# if [ x$is_delete == 'x' -o x$is_delete == 'x0' ];then
	if [ x$is_delete == 'x' ];then
		ACL_LOG "action for rule $rulename to add"
	else
		delete_rule ${rulename}
		ACL_LOG "action for rule $rulename to delete"
		return
	fi
	ACL_LOG "rulename:${rulename},service:${service}"
	get_other_info $rulename
	ACL_LOG "rulename:${rulename},service:${service}"

	local service_name=""
	local service_sport=""
	local service_dport=""
	local service_proto=""
	local service_type=""
	local service_code=""
	local found=0
	local loop=0
	local result=$(uci get service.@service[$loop].name 2>/dev/null)
	while [ x$result != 'x' ];do
		service_name=$(uci get service.@service[$loop].name 2>/dev/null )
		service_sport=$(uci get service.@service[$loop].sport 2>/dev/null)
		service_dport=$(uci get service.@service[$loop].dport 2>/dev/null)
		service_proto=$(uci get service.@service[$loop].proto 2>/dev/null)
		service_type=$(uci get service.@service[$loop].type 2>/dev/null)
		service_code=$(uci get service.@service[$loop].code 2>/dev/null)
		if [ $service_name == $service ];then
			found=1
			break
		fi
		loop=$(($loop+1))
		result=$(uci get service.@service[$loop].name 2>/dev/null)
	done

	if [ x$found != 'x' ];then
		ACL_LOG "uci_info_find:${service_name},${service_sport},${service_dport},${service_proto},${service_type},${service_code}"
	else
		ACL_LOG "service not found"
		return
	fi

	#new added
	local set_src_rule=""
	if [ $src != "IPGROUP_ANY" ];then
		local src_group=$(echo $src |grep "_REV$")
		if [ -n "$src_group" ]; then
			local rule_acl_inner_src=${src%_*}

			fw_acl_ipset_check $rule_acl_inner_src
			[ x$? == x0 ] && {
			  set_src_rule="-m set ! --match-set $rule_acl_inner_src src"
			} || return
		else
			fw_acl_ipset_check $src
			[ x$? == x0 ] && {
			  set_src_rule="-m set --match-set $src src"
			} || return
		fi
	else
		set_src_rule=""   # IPGROUP_ANY
	fi
	ACL_LOG "set_src_rule=$set_src_rule"

	local set_dst_rule=""
	if [ $dest != "IPGROUP_ANY" ];then
		local dst_group=$(echo $dest |grep "_REV$")
		if [ -n "$dst_group" ]; then
			local rule_acl_inner_dest=${dest%_*}
			fw_acl_ipset_check $rule_acl_inner_dest
			[ x$? == x0 ] && {
				set_dst_rule="-m set ! --match-set $rule_acl_inner_dest dst"
			} || return
		else
			fw_acl_ipset_check $dest
			[ x$? == x0 ] && {
				set_dst_rule="-m set --match-set $dest dst"
			} || return
		fi
	else
		set_dst_rule=""
	fi
	ACL_LOG "set_dst_rule=$set_dst_rule"

	local interface=""
	local device=""
	local all_flag=0
	local sdvpn_flag=0
	[ $zone == "ALL" ] && all_flag=1
	[ $zone == "SDVPN_IFACES" ] && sdvpn_flag=1
	for interface in ${zone};do
		local xyy=""
		if [ $all_flag == '1'  -o $sdvpn_flag == '1' ];then
			xyy="eth0"
		else
			xyy=$(zone_get_effect_ifaces $interface)
			xyy=$(zone_get_device_byif $xyy)
		fi
		ACL_LOG "xyy:${xyy}"
		for device in $xyy;do
			[ -z $device ] && continue
			local iface_flag=1
			zone_dev_is_vpn $xyy
			if [ $? -eq 0 ]; then
				iface_flag=0
			fi

			ACL_LOG "service_name:${service_name}"
			if [ "$service_name" == "ALL" ]; then
				local tmpa=""
				if [ $sdvpn_flag == '1' ]; then
					tmpa="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
				elif [ $all_flag != '1' ];then
					tmpa="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
				fi
				local rule="$tmpa"" ""$set_src_rule"" ""$set_dst_rule"" ""-m comment --comment ${rulename}_1acl"

				if [ x$is_delete == "x" ];then
					fw_check "filter" "access_ctl" "$rule -j $policy"
					ACL_LOG "fw_check:$?"
					[ x$? == x0 ] && {
						if [ $position == 0 ];then
							ACL_LOG "iptables -w -A access_ctl $rule -j ${policy}"
							iptables -w -A access_ctl $rule -j ${policy}
						else
							ACL_LOG "iptables -w -I access_ctl $position $rule -j ${policy}"
							iptables -w -I access_ctl $position $rule -j ${policy}
						fi
					}
				else
					fw_check "filter" "access_ctl" "$rule -j ${policy}"
					[ x$? == x1 ] && fw del 4 f access_ctl ${policy} $ { $rule }
				fi
			else
				ACL_LOG "enter into non-all branch"
				service_name=$(echo $service_name | tr 'a-z' 'A-Z')
				if [ "$service_proto" != "icmp" ]; then
					service_proto=$(echo $service_proto | tr 'a-z' 'A-Z')
					if [ $service_proto == "TCP" -o $service_proto == "UDP" ];then
						service_sport=$(echo $service_sport|tr '-' ':')
						service_dport=$(echo $service_dport|tr '-' ':')
						echo "service_proto=$service_proto"
						#for proto in $service_proto; do
						local tmpb=""
						if [ $sdvpn_flag == '1' ]; then
							tmpb="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
						elif [ $all_flag != '1' ];then
							tmpb="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
						fi
						local proto=$(echo $service_proto | tr 'A-Z' 'a-z')
						local rule="$tmpb -p $proto -m $proto --sport $service_sport --dport $service_dport \
									$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl"
						if [ x$is_delete == "x" ];then
							fw_check "filter" "access_ctl" "$rule -j ${policy}"
							[ x$? == x0 ] && {
								local tmpc=""
								if [ $sdvpn_flag == '1' ]; then
									tmpc="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
								elif [ $all_flag != '1' ];then
									tmpc="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
								fi
								if [ $position == 0 ];then
									iptables -w -A access_ctl $tmpc -p $proto -m $proto --sport $service_sport --dport $service_dport \
												$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
								else
									iptables -w -I access_ctl $position $tmpc -p $proto -m $proto --sport $service_sport --dport $service_dport \
												$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
								fi
							}
						else
							fw_check "filter" "access_ctl" "$rule -j ${policy}"
							[ x$? == x1 ] && fw del 4 f access_ctl ${policy} $ { $rule }
						fi
					#done
					else  # proto other
						ACL_LOG "enter into proto other branch"
						if [ $service_proto == "TCP-UDP" ];then #TCP/UDP
							service_sport=$(echo $service_sport|tr '-' ':')
							service_dport=$(echo $service_dport|tr '-' ':')
							echo "service_proto=$service_proto"
							for proto in $service_proto; do
								local tmpd=""
								if [ $sdvpn_flag == '1' ]; then
									tmpd="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
								elif [ $all_flag != '1' ];then
									tmpd="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
								fi
								local tcp_rule="$tmpd -p tcp -m tcp --sport $service_sport --dport $service_dport \
										$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl"
								local udp_rule="$tmpd -p udp -m udp --sport $service_sport --dport $service_dport \
										$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl"

								if [ x$is_delete == "x" ];then
									fw_check "filter" "access_ctl" "$tcp_rule -j $policy"
									[ x$? == x0 ] && {
										local tmpe=""
										if [ $sdvpn_flag == '1' ]; then
											tmpe="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
										elif [ $all_flag != '1' ];then
											tmpe="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
										fi
										if [ $position == 0 ];then
											iptables -w -A access_ctl  $tmpe -p tcp -m tcp --sport $service_sport --dport $service_dport \
												$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
										else
											iptables -w -I access_ctl $position $tmpe -p tcp -m tcp --sport $service_sport --dport $service_dport \
												$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
										fi
									}
									fw_check "filter" "access_ctl" "$udp_rule -j $policy"
									[ x$? == x0 ] && {
										local tmpf=""
										if [ $sdvpn_flag == '1' ]; then
											tmpf="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
										elif [ $all_flag != '1' ];then
											tmpf="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
										fi
										if [ $position == 0 ];then
											iptables -w -A access_ctl  $tmpf -p udp -m udp --sport $service_sport --dport $service_dport \
												$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
										else
											iptables -w -I access_ctl $position $tmpf -p udp -m udp --sport $service_sport --dport $service_dport \
												$set_src_rule  $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
										fi
									}
								else
									fw_check "filter" "access_ctl" "$tcp_rule -j $policy"
									[ x$? == x1 ] && fw del 4 f access_ctl $policy $ { $tcp_rule }
									fw_check "filter" "access_ctl" "$udp_rule -j $policy"
									[ x$? == x1 ] && fw del 4 f access_ctl $policy $ { $udp_rule }
								fi
							done
						else  #other
							for proto in $service_proto;do
								local tmpg=""
									if [ $sdvpn_flag == '1' ]; then
										tmpg="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
									elif [ $all_flag != '1' ];then
										tmpg="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
									fi
								local rule="$tmpg -p $proto $set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl"
								if [ x$is_delete == "x" ];then
									fw_check "filter" "access_ctl" "$rule -j $policy"
									[ x$? == x0 ] && {
										if [ $position == 0 ];then
											iptables -w -A access_ctl $tmpg -p $proto $set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
										else
											iptables -w -I access_ctl $position $tmpg -p $proto $set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
										fi
									}
								else
									fw_check "filter" "access_ctl" "$rule -j $policy"
									[ x$? == x1 ] && fw del 4 f access_ctl $policy $ { $rule }
								fi
							done
						fi
					fi
				else
					if [ "$service_name" == "ICMP_ALL" ]; then
						local tmph=""
						if [ $sdvpn_flag == '1' ]; then
							tmph="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
						elif [ $all_flag != '1' ];then
							tmph="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
						fi
						local rule="$tmph -p $service_proto --icmp-type any $set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl"
						if [ x$is_delete == "x" ]; then
							fw_check "filter" "access_ctl" "$rule -j $policy"
							[ x$? == x0 ] && {
								if [ $position == 0 ];then
									iptables -w -A access_ctl $tmph -p $service_proto --icmp-type any \
										$set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
								else
									iptables -w -I access_ctl $position $tmph -p $service_proto --icmp-type any \
										$set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
								fi
							}
						else
							fw_check "filter" "access_ctl" "$rule -j $policy"
							[ x$? == x1 ] && fw del 4 f access_ctl $policy $ { $rule }
						fi
					else
						local tmpi=""
						if [ $sdvpn_flag == '1' ]; then
							tmpi="-m iface_group --dev_set SDVPN_IFACES --iface_in 0"
						elif [ $all_flag != '1' ];then
							tmpi="-m iface_group --dev_set ${interface}_IFACES --iface_in ${iface_flag}"
						fi
						local rule="$tmpi -p $service_proto -m $service_proto --icmp-type $service_type/$service_code \
							$set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl"
						if [ x$is_delete == "x" ]; then
							fw_check "filter" "access_ctl" "$rule -j $policy"
							[ x$? == x0 ] && {
								if [ $position == 0 ];then
									iptables -w -A access_ctl $tmpi -p $service_proto -m $service_proto --icmp-type $service_type/$service_code \
										$set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
								else
									iptables -w -I access_ctl $position $tmpi -p $service_proto -m $service_proto --icmp-type $service_type/$service_code \
										$set_src_rule $set_dst_rule -m comment --comment ${rulename}_1acl -j ${policy}
								fi
							}
						else
							fw_check "filter" "access_ctl" "$rule -j $policy"
							[ x$? == x1 ] && fw del 4 f access_ctl $policy $ { $rule }
						fi
					fi
				fi
			fi
		done
	done
	ACL_LOG "action for rule $rulename stop "
}

# $1=rulename  $2=is_delete $3==>zone $4==>service_name $5==>src_group $6==>dest_group $7==>policy
# if $2 is empty,means 'add'.
{
  flock 4
  ACL_LOG "start add_rule $1"
  add_rule $1 $2 $3 $4 $5 $6 $7
  ACL_LOG "stop add_rule $1"
} 4<>/tmp/access_ctl_lock