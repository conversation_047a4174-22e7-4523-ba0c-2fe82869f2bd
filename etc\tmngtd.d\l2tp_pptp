#!/bin/sh
#brief:  time to dial, the script runned by tmngtd
#author: wangdechu

. /lib/time_mngt/timeobj_api.sh

[ "$#" -lt 2 ] && exit 1


cmd=$1
iface=$2

case ${cmd} in
	*ACTIVE)
		echo "l2tp/pptp active ${iface}" >/dev/console
		/lib/pppox/pppox-ifup-down.sh ifup ${iface}
		;;

    *EXPIRE)
		echo "l2tp/pptp expire ${iface}" >/dev/console
		/lib/pppox/pppox-ifup-down.sh ifdown ${iface}
		;;

	*RESET)
        echo "RESET event!"
		;;
esac

