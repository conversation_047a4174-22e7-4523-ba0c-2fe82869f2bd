#!/bin/sh /etc/rc.common

# shellcheck disable=SC2034
START=93

. /lib/sdb/sdb_lock.sh
. /lib/sdb/sdb_config.sh

start() {
    (
        #根据当前测试，设置为60s
        local times=60
        local ret=0
        while [ "$times" -gt 0 ];
        do
            is_all_sdb_inited
            ret=$?
            if [ "$ret" -ne 0 ]; then
                break
            fi
            let times=$times-1
            sleep 1
        done

        if [ "$times" -eq 0 ]; then
            set_all_sdb_inited
        fi

        local SUBSHELL_PID
        SUBSHELL_PID=$(sh -c 'echo $PPID')
        sdb_lock_take "sdb_commit" "$SUBSHELL_PID"
        sdb_submit
        sdb_lock_give "sdb_commit" "$SUBSHELL_PID"
    ) &
}

stop() {
    # nothing to do
    true
}
