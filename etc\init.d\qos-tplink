#!/bin/sh /etc/rc.common
# Copyright (C) 2006 OpenWrt.org

START=50

boot() {
    . /lib/qos-tplink/api.sh stop
    . /lib/qos-tplink/api.sh start
}

start() {
	. /lib/qos-tplink/api.sh stop
	. /lib/qos-tplink/api.sh start
}

restart() {
	rm -rf /tmp/.qos/
	. /lib/qos-tplink/api.sh stop
	. /lib/qos-tplink/api.sh start
}

stop() {
	. /lib/qos-tplink/api.sh stop
}

reload() {
	. /lib/qos-tplink/api.sh reload
}

