# General settings

# specify which authentication comes first respectively which
# authentication is used. possible values are: "radius" and "local".
# if you specify "radius,local" then the RADIUS server is asked
# first then the local one. if only one keyword is specified only
# this server is asked.


# dictionary of allowed attributes and values
# just like in the normal RADIUS distributions
dictionary_dir /etc/radius/
root_dictionary 	dictionary
