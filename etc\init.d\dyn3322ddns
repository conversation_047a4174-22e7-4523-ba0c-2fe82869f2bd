#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=99


local DYN3322DDNS_INIT=/tmp/dyn3322ddns.init
DYN3322DDNS_READY=/tmp/dyn3322ddnsFlag

local dyn3322ddns_enabled_rule_num=0

get_rule_num(){
	config_get interface $1 interface
	config_get enable $1 enable
	
	if [ -n "${interface}" ];then
		let dyn3322ddns_enabled_rule_num++
	fi
}

get_dyn3322ddns_num()
{
	config_load dyn3322ddns
	config_foreach  get_rule_num dyn3322ddns
}

start()
{
		get_dyn3322ddns_num
		
		if [ ${dyn3322ddns_enabled_rule_num} -gt 0 ];then
		{
			echo "dyn3322ddns_enabled_rule_num = ${dyn3322ddns_enabled_rule_num},restart dyn3322ddns" > /dev/console
			touch $DYN3322DDNS_INIT
			touch $DYN3322DDNS_READY	
			service_start /usr/sbin/dyn3322ddnsd
			
		}
		else 
		{
			if [ -f $DYN3322DDNS_INIT ];then
			{
				echo "dyn3322ddns_enabled_rule_num = 0,but not init state,restart dyn3322ddns" > /dev/console
				touch $DYN3322DDNS_INIT
				touch $DYN3322DDNS_READY	
				service_start /usr/sbin/dyn3322ddnsd
			}
			fi
		}
		fi
        return 0
}

stop()
{
        service_stop /usr/sbin/dyn3322ddnsd
        return 0
}

restart()
{
        stop
		start
}

reload()
{
        restart   
}

