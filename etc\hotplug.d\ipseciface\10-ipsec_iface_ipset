#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.
. /lib/zone/zone_api.sh

[ ! -f /tmp/iface_setup.ready ] && {
	exit 0
}

{
flock -x 110
case "$ACTION" in
	ifup)
		if [ -n "$XFRMI_IF" ]; then
			ipset add IPSEC_IFACES ${XFRMI_IF} -exist
		fi
	;;
	ifdown)
		if [ -n "$XFRMI_IF" ]; then
			ipset del IPSEC_IFACES ${XFRMI_IF} -exist
		fi
	;;
	ifupdate)
		# do nothing
	;;
esac
flock -u 110
} 110<>/tmp/ipsec_110.lock
