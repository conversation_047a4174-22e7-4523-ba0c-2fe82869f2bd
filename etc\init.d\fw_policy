#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

START=85

start() {
	local allocator_type=$(uci get profile.@cse[0].memory_allocator_type)
	local allow_overflow=$(uci get profile.@cse[0].memory_allocator_allow_overflow)

	[ -z "$allocator_type" ] && allocator_type=1
	[ -z "$allow_overflow" ] && allow_overflow=1

	insmod policytarget cse_allocator=$allocator_type cse_allocator_allow_overflow=$allow_overflow
	lua /lib/fw_policy_manager/fw_policy_handler.lua init
        . /lib/fw_policy_manager/sfe_handler.sh register
}

stop() {
	true
}
