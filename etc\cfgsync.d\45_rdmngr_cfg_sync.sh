#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"
local pub = require "luci.torchlight.uac.rdmngr_public"

local db_rdmngr = {
	name = "rdmngr.db",
	tables = {
		{
			name = "rdmngrTable",
			columns = {
				{
					name = "rf_id",
					stype = "INTEGER",
					db_attr = {"db_index"},
					default_value = nil
				}, {
					name = "rf_name",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "freq_band",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "mode",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "bandwidth",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "channel",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "power_level",
					stype = "INTEGER",
					db_attr = {},
					default_value = function(index, entry)
						if entry.power ~= nil and entry.max_tx_pwr ~= nil and entry.min_tx_pwr ~= nil and entry.channel ~= nil and entry.freq_band ~= nil then
							return pub.get_pwr_level(tonumber(entry.power), tonumber(entry.max_tx_pwr), tonumber(entry.min_tx_pwr), tonumber(entry.channel), tonumber(entry.freq_band))
						end
						return 10
					end
				}, {
					name = "max_usr",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "antenna",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "frag_thr",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "bcn_intvl",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "manage_frame_rate",
					stype = "INTEGER",
					db_attr = {},
					default_value = function(index, entry)
						if entry.freq_band ~= nil then
							return 0 == tonumber(entry.freq_band) and 22 or 48
						end
						return 22
					end
				}, {
					name = "rts_thr",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "dtim_prd",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "wmm",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "brd_probe",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "short_gi",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "rssi_restrict",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "rssi_restrict_val",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "rssi_kick",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "rssi_kick_val",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "chLock",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "pwrLock",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "scanMode",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "scanType",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "scanIntvl",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_state",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "airtime",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_chnl_dcs",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_chnl_reselect",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_online_switch",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_check_circle",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_chnl_occup_threshold",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "rf_chnl_tolerance",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "chErrFrameRatio",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "reject_limit",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "reject_enable",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "reject_diff",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "key",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				},
			},
			datas = {}
		}
	}
}

cfgsync.config_sync(db_rdmngr, "database")
