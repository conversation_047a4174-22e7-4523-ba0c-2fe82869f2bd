#!/usr/bin/lua

local dbg       = require "luci.torchlight.debug"
local uci       = require "luci.model.uci"
local cfgsync   = require "luci.torchlight.config_sync"
local conf_dir = "/tmp/etc/uc_conf"

local module_name = "av_profile"

local function change_enable(old_val)
    if "yes" == old_val then
        return "on"
    else
        return "off"
    end
end

local function change_action(old_val)
    if "warn" == old_val then
        return "alert"
    elseif "accept" == old_val then
        return "allow"
    else
        return old_val
    end
end

local change_tables = {
    ["http_action"] =   {["func"]=change_action},
    ["ftp_action"]  =   {["func"]=change_action},
    ["smtp_action"] =   {["func"]=change_action},
    ["pop3_action"] =   {["func"]=change_action},
    ["imap_action"] =   {["func"]=change_action},
    ["http_up"] =   {["new_item"]="http_up_enable",    ["func"]=change_enable},
    ["ftp_up"]  =   {["new_item"]="ftp_up_enable",     ["func"]=change_enable},
    ["smtp_up"] =   {["new_item"]="smtp_up_enable",    ["func"]=change_enable},
    ["imap_up"] =   {["new_item"]="imap_up_enable",    ["func"]=change_enable},
    ["http_down"] = {["new_item"]="http_down_enable",  ["func"]=change_enable},
    ["ftp_down"]  = {["new_item"]="ftp_down_enable",   ["func"]=change_enable},
    ["pop3_down"] = {["new_item"]="pop3_down_enable",  ["func"]=change_enable},
    ["imap_down"] = {["new_item"]="imap_down_enable",  ["func"]=change_enable},
    ["appid_accept"] = {["new_item"]="exception_appid_allow"},
    ["appid_warn"]   = {["new_item"]="exception_appid_alert"},
    ["appid_block"]  = {["new_item"]="exception_appid_block"}
}

local function config_sync()
    local has_changed = 0
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    uci_r.foreach(module_name, "av_profile", function(conf_section)
        if nil == conf_section["flag"] then
            if "av_profile_default" ~= conf_section[".name"] then
                uci_r:set(module_name, conf_section[".name"], "flag", "user")
                has_changed = 1
            end
        end

        for old_item, change_table in pairs(change_tables) do
            if nil ~= conf_section[old_item] then
                local item_name = change_table.new_item or old_item
                local new_value
                -- 如果是需要替换的值，那么使用新的值进行替换
                if nil ~= change_table.func then
                    new_value = change_table.func(conf_section[old_item])
                else
                    new_value = conf_section[old_item]
                end

                if item_name ~= old_item or new_value ~= conf_section[old_item] then
                    uci_r:delete(module_name, conf_section[".name"], old_item)
                    uci_r:set(module_name, conf_section[".name"], item_name, new_value)
                    has_changed = 1
                end
            end
        end
    end)

    if 1 == has_changed then
        uci_r:commit(module_name)
        cfgsync.set_config_changed()
    end

    return
end

config_sync()
