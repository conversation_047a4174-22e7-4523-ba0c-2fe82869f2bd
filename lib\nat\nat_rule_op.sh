#!/bin/sh /etc/rc.common
. /lib/functions/network.sh

IPT4="iptables -w -t nat"
IPT6="ip6tables -w -t nat"
OP="-A"


# 用于one nat的snat规则
nat_rule_snat_onenat()
{
    local chain_name=$1
    local device=$2
    local internal_ip=$3
    local external_ip=$4
    local rule_name=$5
    local proto=$6
    local operation=$7

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    case $operation in
    add)
        OP="-A"
    ;;
    del)
        OP="-D"
    ;;
    esac

    echo "$IPT ${OP} ${chain_name} -o ${device} -s ${internal_ip} -j SNAT --to-source ${external_ip} -m comment --comment ${rule_name}" > /dev/console
    $IPT ${OP} ${chain_name} -o ${device} -s ${internal_ip} -j SNAT --to-source ${external_ip} -m comment --comment ${rule_name}
}

# 用于onenat中dmz的dnat
nat_rule_dnat_dmz_onenat()
{
    local chain_name=$1
    local device=$2
    local internal_ip=$3
    local external_ip=$4
    local rule_name=$5
    local proto=$6
    local operation=$7

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    case $operation in
    add)
        OP="-A"
    ;;
    del)
        OP="-D"
    ;;
    esac

    echo "$IPT ${OP} ${chain_name} -i ${device} -d ${internal_ip} -j DNAT --to-destination ${external_ip} -m comment --comment ${rule_name}" > /dev/console
    $IPT ${OP} ${chain_name} -i ${device} -d ${internal_ip} -j DNAT --to-destination ${external_ip} -m comment --comment ${rule_name}
}

# 用于dmz中的dnat
nat_rule_dnat_dmz()
{
    local chain_name=$1
    local device=$2
    local external_ip=$3
    local internal_ip=$4
    local rule_name=$5
    local proto=$6
    local operation=$7

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    case $operation in
    add)
        OP="-A"
    ;;
    del)
        OP="-D"
    ;;
    esac

    echo "$IPT ${OP} ${chain_name} -i ${device} -d ${external_ip} -j DNAT --to-destination ${internal_ip} -m comment --comment ${rule_name}" > /dev/console
    $IPT ${OP} ${chain_name} -i ${device} -d ${external_ip} -j DNAT --to-destination ${internal_ip} -m comment --comment ${rule_name}
}


#MASQUERADE
nat_rule_mas()
{
    local chain_name=$1
    local device=$2
    local ipaddr_mask=$3
    local rule_name=$4
    local proto=$5
    local operation=$6
    local IPT=$IPT4

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    case $operation in
    add)
        OP="-A"
    ;;
    del)
        OP="-D"
    ;;
    esac

    echo "$IPT ${OP} ${chain_name} -o ${device} -s ${ipaddr_mask} -j MASQUERADE -m comment --comment ${rule_name}" > /dev/console
    $IPT ${OP} ${chain_name} -o ${device} -s ${ipaddr_mask} -j MASQUERADE -m comment --comment ${rule_name}
}

nat_rule_mas_for_iface_set()
{
    local chain_name=$1
    local device=$2
    local ipaddr_mask=$3
    local rule_name=$4
    local proto=$5
    local operation=$6
    local IPT=$IPT4

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    case $operation in
    add)
        OP="-A"
    ;;
    del)
        OP="-D"
    ;;
    esac

    $IPT ${OP} ${chain_name} -m ${device} -s ${ipaddr_mask} -j MASQUERADE -m comment --comment ${rule_name}

    echo "$IPT ${OP} ${chain_name} -m ${device} -s ${ipaddr_mask} -j MASQUERADE -m comment --comment ${rule_name}" > /dev/console
    $IPT ${OP} ${chain_name} -m ${device} -s ${ipaddr_mask} -j MASQUERADE -m comment --comment ${rule_name}
}

#RETURN
nat_rule_return()
{
    local chain_name=$1
    local proto=$2
    local service_proto=$3
    local port=$4
    local IPT="iptables -t nat -w"

    case $2 in
    IPv4)
        IPT="iptables -t nat -w"
    ;;
    IPv6)
        IPT="ip6tables -t nat -w"
    ;;
    esac

    if [ -n "$3" ]
    then
        service_proto=" -p "$3
    fi

    if [ -n "$4" ]
    then
        port=" -m multiport --dport "$4
    fi

    echo "$IPT -I ${chain_name} ${service_proto} ${port} -j RETURN" > /dev/console
    $IPT -I ${chain_name} ${service_proto} ${port} -j RETURN
}

nat_rule_vs()
{
    local origin_ip=$1
    local dest_ip=$2
    local service_proto=$3
    local src_port=$4
    local dest_port=$5
    local ipset_name=$6
    local proto=$7
    local rule_name=$8
    local operation=$9

    local IPT=$IPT4
    local chain_name_post="postrouting_rule_vs"
    local chain_name_pre="prerouting_rule_vs"

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    case $operation in
    add)
        OP="-A"
    ;;
    del)
        OP="-D"
    ;;
    esac

    local dest_port_form2=${dest_port//-/:}
    local src_port_form2=${src_port//-/:}

    echo "$IPT ${OP} ${chain_name_post} -m set --match-set ${ipset_name} src -d ${dest_ip} -p ${service_proto} -m multiport --dports ${dest_port_form2} -m comment --comment ${rule_name} -j SNAT --to-source ${origin_ip}" > /dev/console
    $IPT ${OP} ${chain_name_post} -m set --match-set ${ipset_name} src -d ${dest_ip} -p ${service_proto} -m multiport --dports ${dest_port_form2} -m comment --comment ${rule_name} -j SNAT --to-source ${origin_ip}

    if [ $proto = "IPv6" ]
    then
        dest_ip="["${dest_ip}"]"
    fi
    echo "$IPT ${OP} ${chain_name_pre} -d ${origin_ip} -p ${service_proto} -m multiport --dports ${src_port_form2} -m comment --comment ${rule_name} -j XDNAT --to-destination ${dest_ip}:${dest_port}:${src_port}" > /dev/console
    $IPT ${OP} ${chain_name_pre} -d ${origin_ip} -p ${service_proto} -m multiport --dports ${src_port_form2} -m comment --comment ${rule_name} -j XDNAT --to-destination ${dest_ip}:${dest_port}:${src_port}
}

nat_rule_del()
{
    local chain_name=$1
    local rule_name=$2
    local proto=$3
    local IPT=$IPT4

    case $proto in
    IPv4)
        IPT=$IPT4
    ;;
    IPv6)
        IPT=$IPT6
    ;;
    esac

    #find the number of the rule
    local rule_num=`$IPT -nv --line-number -L ${chain_name} | grep -w "\/\* ${rule_name} \*\/" | sort -r -n | cut -d " " -f 1`
    echo "$IPT -nv --line-number -L ${chain_name} | grep -w "\/\* ${rule_name} \*\/" | sort -r -n | cut -d " " -f 1" > /dev/console

    for nums in $rule_num
    do
        echo "$IPT -D ${chain_name} ${nums}" > /dev/console
        $IPT -D ${chain_name} ${nums}
    done
}


# get ip from iface
# $1 iface
# $2 proto
extract_IP_from_iface()
{
    case $2 in
    IPv4)
        network_get_ipaddr ipaddr $1
    ;;
    IPv6)
        network_get_ipaddr6 ipaddr $1
    ;;
    esac

    echo $ipaddr
}