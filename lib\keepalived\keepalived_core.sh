#!/bin/sh /etc/rc.common

. /lib/functions.sh

keepalived_name=""

num2ip() {
    local num=$1
    a=$(($num>>24))
    b=$(($num>>16&0xff))
    c=$(($num>>8&0xff))
    d=$(($num&0xff))
    echo "$a.$b.$c.$d"
}

ip2num() {
    local ip=$1
    a=`echo $ip | awk -F'.' '{print $1}'`
    b=`echo $ip | awk -F'.' '{print $2}'`
    c=`echo $ip | awk -F'.' '{print $3}'`
    d=`echo $ip | awk -F'.' '{print $4}'`
    echo "$(((a<<24)+(b<<16)+(c<<8)+d))"
}

keepalived_rule_get() {
	config_get interface $1 interface
	config_get name $1 name
	case $2 in
		interface)
			if [ "$3" == "$interface" ]; then
				keepalived_name=$name
			fi
		;;
	esac
}

keepalived_get_name_by_interface() {
	{
		flock -x 300
		keepalived_name="---"
		config_load keepalived
		config_foreach keepalived_rule_get rule interface $1
		echo "$keepalived_name"
		flock -u 300
	} 300<>/tmp/keepalived.lock
}

keepalived_get_running_state_by_name() {
	local enable=`uci get keepalived.$1.enable`
	if [ "$enable" == "off" ]; then
		echo "---"
		return 1
	fi
	if [ -f "/tmp/keepalived/$1" ]; then
		local state=`cat /tmp/keepalived/$1`
		echo "$state"
		return 1
	fi
	echo "vote"
}

keepalived_get_running_state_by_interface() {
	local name=`keepalived_get_name_by_interface $1`
	if [ "$name" != "---" ]; then
		local running_state=`keepalived_get_running_state_by_name $name`
		echo "$running_state"
	else
		echo "---"
	fi
}

keepalived_get_state_by_name() {
	local state=`uci get keepalived.$1.state`
	if [ "x$state" == "x" ]; then
		echo "---"
	else
		echo "$state"
	fi
}

keepalived_get_state_by_interface() {
	local name=`keepalived_get_name_by_interface $1`
	if [ "$name" != "---" ]; then
		local state=`keepalived_get_state_by_name $name`
		echo "$state"
	else
		echo "---"
	fi
}

keepalived_get_vip_by_name() {
	local vip=`uci get keepalived.$1.virtual_ipaddress`
	if [ "x$vip" == "x" ]; then
		echo "---"
		return 1
	fi
	local interface=`uci get keepalived.$1.interface`
	if [ "x$interface" == "x" ]; then
		echo "---"
		return 1
	fi
	local ip=`uci get network.$interface.ipaddr`
	if [ "x$ip" == "x" ]; then
		echo "---"
		return 1
	fi
	local netmask=`uci get network.$interface.netmask`
	if [ "x$netmask" == "x" ]; then
		echo "---"
		return 1
	fi
	local ipint=`ip2num $ip`
	local netmaskint=`ip2num $netmask`
	local network=$(($ipint & $netmaskint))
	local vipint=`expr $network + $vip`
	local realvip=`num2ip $vipint`
	echo "$realvip"
}

keepalived_get_vip_by_interface() {
	local name=`keepalived_get_name_by_interface $1`
	if [ "$name" != "---" ]; then
		local vip=`keepalived_get_vip_by_name $name`
		echo "$vip"
	else
		echo "---"
	fi
}