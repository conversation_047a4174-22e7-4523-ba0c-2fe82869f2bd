#!/bin/sh /etc/rc.common

START=96

IPT="iptables -t mangle -w"

chain_tables="ISP_CHINA_TELECOM ISP_CERNET ISP_CMCC_CRTC ISP_UNICOM_CNC ISP_CN_OTHERS ISP_CN_ALL"

PR_CUR_ID="/tmp/isp_route/.cur_id"
PR_INFACE_ID="/tmp/isp_route/.iface_id"

start()
{
	. /lib/functions.sh

	. /lib/balance/api.sh

	for i in $chain_tables; do
		tmp=`ipset list -t |grep $i 2>/dev/null`
		if [ -z "$tmp" ]; then
			/usr/sbin/isp_route_db system -b
			break
		fi
	done

	tmp=`ipset list -t |grep ISP_USER_DEFINE 2>/dev/null`
	if [ -z "$tmp" ] ;then
		/usr/sbin/isp_route_db user -b
	fi

	state=`get_balance_global_state`
	[ "$state" == "off" ] && return

	config_load isp_route
	config_get state global state "on"

	if [ "$state" == "on" ]; then
		/usr/sbin/isp_route
	fi

	touch /tmp/isp_route.ready
}

del_interface_rule()
{
	. /lib/balance/api.sh

	config_get interface $1 if

	iface_isp_name=$interface"_isp_route"

	if $IPT -S $iface_isp_name &> /dev/null; then
		$IPT -F $iface_isp_name &> /dev/null
		$IPT -X $iface_isp_name &> /dev/null
	fi

	$IPT -D isp_route -j  $iface_isp_name &> /dev/null
}

stop()
{
	#delete ip rule
	for rule in $(ip rule list | egrep '^[5][0-9]{3}\:' | cut -d ':' -f 1); do
		ip rule del pref $rule &> /dev/null
	done

	for rule in $(ip rule list | egrep '^[6][0-9]{3}\:' | cut -d ':' -f 1); do
		ip rule del pref $rule &> /dev/null
	done

	#delete interface's iptables rule
	config_load isp_route
	config_foreach del_interface_rule rule

	#delete isp_route
	$IPT -D PREROUTING -j isp_route &> /dev/null

	for table in $($IPT -S | awk '{print $2}' | grep isp | sort -u); do
		$IPT -F $table &> /dev/null
	done

	for table in $($IPT -S | awk '{print $2}' | grep isp | sort -u); do
		$IPT -X $table &> /dev/null
	done

	for i in $chain_tables; do
		if $IPT -S $i &> /dev/null; then
			$IPT -F $i &> /dev/null
			$IPT -X $i &> /dev/null
		fi
	done

	rm $PR_CUR_ID -f
	rm $PR_INFACE_ID -f
}

restart() {
	( flock -x 72
		stop
		start
	  flock -u 72
	) 72<>/tmp/.isp_route_lock
}
