#!/bin/sh
# get the ip of ipsec vpn xfrm interface
#

#$1: interface
xfrmi_header="xfrmi_"

get_vpn_ip()
{
	. /lib/zone/zone_api.sh

	mif=$1
	device=`zone_get_device_byif $mif`
	[ -z "${device}" ] && device=$1

	if ! echo ${device} | grep -qE "^(${xfrmi_header})"; then
		exit 1
	fi

	ifconfig ${device}|awk '
		{
			if (match($0, "inet +addr:"))
			{
				gsub(".*inet +addr:", "")
				gsub(" +Mask:.*", "")
				print $0
				exit
			}
		}'
}

get_vpn_ip $1
