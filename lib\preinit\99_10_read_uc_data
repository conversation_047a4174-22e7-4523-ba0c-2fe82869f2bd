#!/bin/sh
# Copyright (C) 2006 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

config_sync() {
	cfg_dir_num=`ls /etc/cfgsync.d/ | wc -l`

	if [ "$cfg_dir_num" != "0" ];then
		get_cfg_sync "/tmp/cfgsync.tmp"

		for CFGSYNC in `cat /tmp/cfgsync.tmp`
		do
			$CFGSYNC
		done

		rm /tmp/cfgsync.tmp
	fi
	
	#清除配置兼容的临时文件&目录
	#配置兼容已完成，可以写uc分区了
	rm -rf /tmp/etc/old_uc_conf
	rm -rf /tmp/etc/def_uc_conf/
	if [ -e /tmp/config_changed_due_to_sync ]; then
		rm /tmp/config_changed_due_to_sync
	fi
}

get_cfg_sync() {
	local file="$1"
	find /etc/cfgsync.d/ -type f | sort -n > "$file"
}

mount_userconfig()
{
	mkdir -p /tmp/etc/userconfig

	if [ -e /dev/ubi2_0 ]; then
		echo "mount ubi2_0 "
		mount "ubi2_0" /tmp/etc/userconfig -t ubifs -o sync> /dev/null 2>&1
		if [ $? -ne 0 ];then
			echo "failed.remount before mtd erase"
			mtd erase $1
			mount "ubi2_0" /tmp/etc/userconfig -t ubifs -o sync
		fi
	else
		part="$(find_mtd_part $1)"
		mount "$part" /tmp/etc/userconfig -t jffs2 > /dev/null 2>&1
		if [ $? -ne 0 ];then
			mtd erase $1
			mount "$part" /tmp/etc/userconfig -t jffs2
		fi
	fi

	mtd -qq unlock $1
}

read_uc() {
	# directory /tmp/.uci will be used as a search path for config change files	
	mkdir -p /tmp/.uci
	chmod 0700 /tmp/.uci
	mkdir -p /var/state
	mkdir -p /var/lock
	# should mkdir this lock path,else can't use custom_lock
	mkdir -p /tmp/custom_lock/

	#mount_userconfig
	part="$(find_mtd_part no_uc_config)"
	if [ -z "$part" ]; then
		mount_userconfig backup
	else
		mount_userconfig no_uc_config
	fi

	#把非uci分区仅作为非uci配置存储
	if [ ! -d "/tmp/etc/userconfig/none_uci_cfg" ];then
		mkdir -p /tmp/tmp_none_uci_cfg
		cp -rf /tmp/etc/userconfig/* /tmp/tmp_none_uci_cfg/
		rm -rf /tmp/etc/userconfig/*
		mkdir -p /tmp/etc/userconfig/none_uci_cfg
		cp -rf /tmp/tmp_none_uci_cfg/* /tmp/etc/userconfig/none_uci_cfg/
		rm rf /tmp/tmp_none_uci_cfg
	fi

	# read user config data from UC zone
	uc_convert -t 0

	# if /tmp/uc/config exist, means that firmware was updated.
	if [ -e /tmp/uc/config ]; then
		# touch tmp file for other program
		touch /tmp/.update_firmware
		rm -rf /tmp/uc/config
	fi

	# 执行各个模块的配置兼容
	config_sync
		
}
      
boot_hook_add preinit_main read_uc
