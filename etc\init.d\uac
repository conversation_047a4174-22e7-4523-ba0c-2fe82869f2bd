#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=80
STOP=80

UAC_BIN="/usr/sbin/uac"

start() {
	# set system parameters
	ulimit -n 10000

	# start uac process
	service_start $UAC_BIN
	(sleep 5;lua /usr/lib/lua/license/notify_uac_reload.lua) &

	return 0
}

stop() {
	rm -rf /tmp/uac_init_done
	rm -rf /tmp/coredump_*

	service_stop $UAC_BIN

	return 0
}

reload() {
	stop
	/etc/init.d/nginx restart
	start
}

restart() {
	reload
}
