#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

# if nat is not loaded, then hotplug does not run
local is_nat_loaded=$(uci_get_state nat core loaded)
is_nat_loaded=$((! ${is_nat_loaded:-0}))
if [ ${is_nat_loaded} = "1" ]
then
	exit 1
fi

case "$ACTION" in
	ifup)
		lua /lib/nat/nat_reload.lua rule_napt $INTERFACE
		lua /lib/nat/nat_reload.lua rule_dmz $INTERFACE
		lua /lib/nat/nat_reload.lua rule_onenat $INTERFACE
		lua /lib/nat/nat_reload.lua rule_vs $INTERFACE
	;;
	ifdown)
	;;
esac
