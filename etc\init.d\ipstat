#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/ipstat.pid

START=95

start()
{
	status=$(uci get system.ip_gbl.status)

	if [ -z "$status" ]; then
		echo "/etc/config/system not found."
		return 0
	fi

	ip=$(uci get system.ip_gbl.ip)
	mask=$(uci get system.ip_gbl.mask)
	interval=$(uci get system.ip_gbl.interval)

	if [ "${status}" == "1" ];then
		service_start /usr/sbin/ipstat start ${ip} ${mask} ${interval}
	fi
}

stop()
{
	service_stop /usr/sbin/ipstat
	if [ -f ${SERVICE_PID_FILE} ];then
		read PID < ${SERVICE_PID_FILE} && kill ${PID}  && rm -f ${SERVICE_PID_FILE}
	fi;
	/usr/sbin/ipstat clean
}

restart()
{
	stop
	start
}

reload()
{
	restart
}

