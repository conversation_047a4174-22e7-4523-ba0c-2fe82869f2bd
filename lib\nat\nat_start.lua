-- 从uci中加载所有nat规则
local err = require("luci.torchlight.error")
local uci = require("luci.model.uci")
local dbg = require ("luci.torchlight.debug")
local nat_event = require("luci.model.nat_event")
local nat_para = require("luci.model.nat_para_parse")
local uci_r = uci.cursor()
local sys = require("luci.sys")

function nat_napt_start(section)
    local data = {}
    data.ip_proto = section["ip_proto"] or "IPv4"
    data.out_if = section["if"]
    data.ip = section["ip"]
    data.enable = section["enable"]
    data.rule_name = section["name"]

    nat_event.nat_napt_add(data, 1)
    return err.ENONE
end

function nat_onenat_start(section)
    local data = {}
    data.ip_proto = section["ip_proto"] or "IPv4"
    data.iface = section["if"]
    data.internal_ip = section["internal_ip"]
    data.external_ip = section["external_ip"]
    data.enable = section["enable"]
    data.dmz = section["dmz"]
    data.rule_name = section["name"]

    nat_event.nat_onenat_add(data, 1)
    return err.ENONE
end

function nat_dmz_start(section)
    local data = {};
    data.iface = section["if"]
    data.ip_proto = section["ip_proto"] or "IPv4"
    data.ip = section["ip"]
    data.enable = section["enable"]
    data.rule_name = section["name"]

    nat_event.nat_dmz_add(data, 1)
    return err.ENONE
end

function nat_vs_start(section)
    local data = {}
    data.ip_proto = section["ip_proto"] or "IPv4"
    data.iface = section["if"]
    data.dest_ip = section["dest_ip"]
    data.enable = section["enable"]
    data.src_port = section["src_dport"]
    data.dest_port = section["dest_port"]
    data.loopback_ipaddr = section["loopback_ipaddr"]
    data.service_proto = section["proto"]
    data.rule_name = section["name"]

    nat_event.nat_vs_add(data, 1)
    return err.ENONE
end

function deal_start_napt()
    uci_r:foreach("nat", "rule_napt",
        function(section)
            nat_napt_start(section)
        end
    )
end

function deal_start_onenat()
    uci_r:foreach("nat", "rule_onenat",
        function(section)
            nat_onenat_start(section)
        end
    )
end

function deal_start_dmz()
    uci_r:foreach("nat", "rule_dmz",
        function(section)
            nat_dmz_start(section)
        end
    )
end

function deal_start_vs()
    uci_r:foreach("firewall", "redirect",
        function(section)
            nat_vs_start(section)
        end
    )
end

if "rule_napt" == arg[1] then
    deal_start_napt()
elseif "rule_dmz" == arg[1] then
    deal_start_dmz()
elseif "rule_onenat" == arg[1] then
    deal_start_onenat()
elseif "rule_vs" == arg[1] then
    deal_start_vs()
elseif "all" == arg[1] then
    deal_start_dmz()
    deal_start_onenat()
    deal_start_vs()
    -- In order to ensure the sequence of chains in nat table,
    -- We deal napt in the end.
    deal_start_napt()
end
