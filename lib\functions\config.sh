#!/bin/sh
. /lib/functions.sh

TMP_PATH_UPPEST="/tmp/cfg_save/"
#flash 目录
CFG_DATA_DIR="/tmp/etc/userconfig/none_uci_cfg/"
CFG_DATA_OLD_DIR="/tmp/etc/userconfig/"
#uci运行时配置目录
CFG_DIR="/tmp/etc/uc_conf/"
#非uci运行时配置目录
NON_UCI_PATH="/etc/nouci_config/"
#恢复出厂是黑名单配置存储目录
RESTORE_FAC_REMAIN_FILE="/tmp/etc/userconfig/none_uci_cfg/restorefac_remain/restorefac_remain"
#保存配置是拷贝运行时的uci临时目录，通常其父目录是/tmp/cfg_save/xxxpid
SAVE_UCI_TMP_DIR="/tmp/etc/uci_save_apart/"
#保存配置是拷贝运行时的非uci临时目录，通常其父目录是/tmp/cfg_save/xxxpid
SAVE_NON_UCI_TMP_DIR="/tmp/etc/non_uci_save_apart/"
#标记配置是否被修改过
MODIFY_FILE_FLAG="/tmp/cfg_save/uc_out_of_date"
#保存配置写flash时，标记写完的标记文件
SAVE_DONE_FLAG="/tmp/etc/userconfig/none_uci_cfg/is_save_done/is_save_done"
#标记save all完成
SAVE_FINISH_FLAG="/tmp/cfg_save/save_finished"

#标记恢复出厂是否完成的文件（存在flash中），恢复出厂置零，完成后置1；默认该文件不存在；
#文件不存在或者被置为1，则表明可以正常加载配置；否则执行一次恢复出厂
RESTORE_DONE_FLAG="/tmp/etc/userconfig/none_uci_cfg/is_restore_done/is_restore_done"
#配置版本在运行时文件路径
RUNNING_CONFIG_ID_PATH="/etc/nouci_config/config_id"
RUNNING_CONFIG_ID_FILE="/etc/nouci_config/config_id/config_id"
#配置加载时一致性检查结果
CHECK_CFG_RESULT_DIR="/tmp/cfg_save/check_result"
#保存配置时，拷贝配置一致性的注册回到命令存储文件
COPY_CFG_TO_TMP_REGISTER_FILE="/tmp/cfg_save/cfg_save_register.cp"
#恢复出厂注册回调命令存储文件
RESTORE_FAC_REG_FILE="/tmp/cfg_save/restore_reg_file"
#导出配置完成文件
IMPORT_PROGRESS=/tmp/cfg_save/import_progress
#不允许配置保存的标记文件
DO_NOT_SAVE_FLAG="/tmp/etc/old_uc_conf"

BACK_RES_TMP_DIRPATH="/tmp/cfg_save/backconf_tmp"                                  # 载入与备份临时文件夹
BACK_TMP_DIRPATH=$BACK_RES_TMP_DIRPATH"/backup"                        # 备份临时文件夹
DOWNLOAD_FILENAME="config-%s.bin"
# 导出配置的md5 sum的文件
MD5_SUM_FILE_NAME="md5_sum_conf"
 # 导出配置的product 相关信息的文件：包含cfgver，hwid，hwver，dev_model等
PRODUCT_NAME="product_name_conf"

#临时文件
TMP_SAVE_PATH="/tmp/restore_backup"
TMP_UC_CONFIG_PATH="/tmp/backup/uc_conf"

#导出配置时，压缩文件，uci配置压缩文件
UC_CONF_TMP_FILE=$BACK_TMP_DIRPATH"/uc_conf.tar.gz"
#导出配置时，压缩文件，非uci配置压缩文件
NON_UCI_TMP_FILE=$BACK_TMP_DIRPATH"/non_uci.tar.gz"
#导出配置时，压缩文件，uci+非uci压缩包再打包成一个整体文件
TMP_FILE=$BACK_RES_TMP_DIRPATH"/tmp_conf.tar.gz"
#导出配置时，配置压缩文件+导出配置头文件，再打包成一个整体压缩文件
BACKUP_FILE="/tmp/backup.tar.gz"
#系统当前最新uc.config
UC_DOT_CONF_FILE_PATH="/etc/tmp_uc_conf/uc.conf"
UC_DOT_CONF_FILE_NAME="uc.conf"
UC_DOT_CONF_EXPORT_PATH="/tmp/cfg_save_export_uc_conf/"

MD5_CONF=$BACK_RES_TMP_DIRPATH"/"$MD5_SUM_FILE_NAME
PRODUCT_CONF=$BACK_RES_TMP_DIRPATH"/"$PRODUCT_NAME

PROGRESS_STATUS_BEGIN=0
PROGRESS_STATUS_ING=1
PROGRESS_STATUS_END=2
#调试开关
DEBUG_SWITCH=`[ -f "/tmp/cfg_save/debug_switch" ] && echo /dev/console || echo  /dev/null`
#支持configid配置uci选项
CFG_ID_GENERATE="cfg_info.config_id.genrate_config_id_enable"

mkdir -p $TMP_PATH_UPPEST
mkdir -p $RUNNING_CONFIG_ID_PATH

#uci命令时，避免污染运行时配置，待操作的uci package增加的文件名后缀
UCI_FILE_SUFFIX="_tmp_cfg"


mutex_running_cfg_lock_take()
{
	if [ -n "$5" ]&&[ "$5" == "sync" ];then
		custom_lock -l "$1" -f running_cfg  -p "$2" -t "$3" -w "$4" -F "1">/dev/null  2>&1
	else
		custom_lock -l "$1" -f running_cfg  -p "$2" -t "$3" -w "$4">/dev/null  2>&1
	fi
}

mutex_running_cfg_lock_give()
{
	custom_lock -n "$1" -f running_cfg  -p "$2" -t "$3" -w "$4">/dev/null  2>&1
}

mutex_flash_protect_lock_take()
{
	custom_lock -l "$1" -f flash_protect  -p "$2" -t "$3" -w "$4">/dev/null  2>&1
}

mutex_flash_protect_lock_give()
{
	custom_lock -n "$1" -f flash_protect  -p "$2" -t "$3" -w "$4">/dev/null  2>&1
}

get_uci_from_flash()
{
	local dst_path="$1"
	local change_suffix="$2"
	local uc_dot_conf_path="$3"
	if [ -n "$1" ]; then
		if [ -n "$uc_dot_conf_path" ]; then
			uc_convert -g $dst_path -e "$uc_dot_conf_path">/dev/console
		else
			uc_convert -g $dst_path >/dev/console
		fi
		if [ ! "$?" == "0" ]; then
			echo "get uci from flash failed">/dev/console
			return 1
		fi
		local file_cnt=`ls $dst_path | wc -l`
		if [ "$file_cnt" == "0" ]; then
			echo "get uci from flash no files">/dev/console
			return 1
		fi
		if [ "change_suffix" ==  "$change_suffix" ]; then
			local files=`ls $dst_path`
			for file in $files
			do
				if [ -f "$dst_path$file" ]; then
					echo "mv $dst_path$file $dst_path$file$UCI_FILE_SUFFIX">$DEBUG_SWITCH
					mv $dst_path$file $dst_path$file$UCI_FILE_SUFFIX
				fi
			done
		fi
	fi
}

write_uci_backup_to_flash()
{
	local src_path="$1"
	local change_suffix="$2"
	local uc_dot_conf_path="$3"

	if [ -n "$1" ]; then
		local file_cnt=`ls $src_path | wc -l`
		if [ "$file_cnt" == "0" ]; then
			echo "no files to write to flash">/dev/console
			return 1
		fi
		if [ "change_suffix" ==  "$change_suffix" ]; then
			local files=`ls $src_path`
			for file in $files
			do
				if [ -f "$src_path$file" ]; then
					local src_file="$src_path$file"
					local dst_file=`echo ${src_file%%$UCI_FILE_SUFFIX*}`
					mv $src_path$file $dst_file
				fi
			done
		fi
		if [ -n "$uc_dot_conf_path" ]; then
			uc_convert -i $src_path -c "1" -e "$uc_dot_conf_path">/dev/console
		else
			uc_convert -i $src_path -c "1">/dev/console
		fi
		if [ ! "$?" == "0" ]; then
			echo "write uci to flash failed">/dev/console
			return 1
		fi
	fi
}

add_pac_suffix_handle()
{
	local change_suffix="$2"
	local suffix_str=""
	if [ "change_suffix" == "$change_suffix" ]; then
		suffix_str="$UCI_FILE_SUFFIX"
	else
		echo "$1"
	fi

	local pacs=$1$suffix_str
	echo $pacs
}

add_optstr_pac_suffix_handle()
{
	local change_suffix="$2"
	local suffix_str=""
	if [ "change_suffix" == "$change_suffix" ]; then
		suffix_str="$UCI_FILE_SUFFIX"
	else
		echo "$1"
	fi

	echo "ops "$1>$DEBUG_SWITCH
	local pacs_tmp=`echo $1 | cut -d "." -f 1`
	local pacs=$pacs_tmp$suffix_str
	local sec_tmp=`echo $1 | cut -d "." -f 2`
	local opts_tmp=`echo $1 | cut -d "." -f 3`
	local opt_str=$pacs"."$sec_tmp"."$opts_tmp
	echo $opt_str
}

# 在升级、导入配置、恢复出厂等操作，需要先把可能会影响flash的操作停止，避免相关操作不符合预期
# 该接口，暂不对外暴露，其中，升级需要使用到该接口，先通过config_api的api调用
stop_flash_reference_op()
{
	ubus call cfg_save cfg_save_stop
	ubus call cfg_save cfg_save_cmd_clean
}

restart_flash_reference_op()
{
	ubus call cfg_save cfg_save_start
}

#0表示不能执行保存；1表示可以
check_can_save()
{
	if [ -e "$DO_NOT_SAVE_FLAG" ]; then
		return 0
	fi
	return 1
}

#某些场景不允许配置保存操作，避免污染flash的配置
forbid_to_save_cfg()
{
	touch $DO_NOT_SAVE_FLAG
	stop_flash_reference_op
}

allow_to_save_cfg()
{
	rm -f $DO_NOT_SAVE_FLAG  >/dev/null  2>&1
	restart_flash_reference_op
}

#输出校验失败的非uci配置文件列表，其中每一行代表一个文件，文件均是运行时路径
#特殊处理的save_done文件，不带文件路径
output_check_result()
{
	if [ -n "$2" ]; then
		local file=`echo $1 | sed 's/\/tmp\/etc\/userconfig/\/etc\/nouci_config/'`
		echo $file >> $2
	fi
}

set_file_md5_info()
{
	if [ -n "$1" ]&&[ -f  "$1" ]; then
		local input_file="$1"
		local file_dir=${input_file%/*}
		local md5_dir=$file_dir".md5"
		local file_name=${input_file##*/}
		mkdir -p $md5_dir
		
		local mem_md5_result=`md5sum $input_file | awk '{print $1}' | head -n 1`
		echo $mem_md5_result > $md5_dir"/"$file_name".md5"
	fi
}

clean_file_and_md5_info()
{
	if [ -n "$1" ]; then
		local input_file="$1"
		if [ -f "$input_file" ]; then
			local file_dir=${input_file%/*}
			local md5_dir=$file_dir".md5"
			local file_name=${input_file##*/}
			rm -f $md5_dir"/"$file_name".md5"  >/dev/null  2>&1
			rm -f "$input_file"  >/dev/null  2>&1
		elif [ -d "$input_file" ]; then
			rm -rf $input_file".md5"  >/dev/null  2>&1
			rm -rf "$input_file"  >/dev/null  2>&1
		fi
	fi
}

check_one_file_md5_info()
{
	if [ ! -n "$1" ]; then
		return 1
	fi
	local input_file="$1"
	local file_dir=${input_file%/*}
	local md5_dir=$file_dir".md5"
	local file_name=${input_file##*/}
	
	local md5_path=$md5_dir"/"$file_name".md5"
	
	if [ ! -d "$md5_dir" ]||[ ! -f "$md5_path" ]; then
		echo "$md5_dir  $md5_path no exist">$DEBUG_SWITCH
		return 1
	fi
	
	local mem_md5_result=`md5sum $input_file | awk '{print $1}' | head -n 1`
	local flash_md5_result=`cat $md5_path | awk '{print $1}' | head -n 1`
	echo  "check_one_file_md5_info" "$md5_path" $mem_md5_result $flash_md5_result>$DEBUG_SWITCH
	if [ "$mem_md5_result" == "$flash_md5_result" ];then
		return 0
	fi
	return 1
}

genrate_config_id()
{
	local support_config_id=`uci get $CFG_ID_GENERATE`
	if [ "on" == "$support_config_id" ];then
		uuidgen >/dev/null  2>&1
		if [ "0" == "$?" ]; then
			local user_modify_env=`envop getenv user_modify_env`
			#user_modify_env 表示是TDCP接口请求，已对运行时加锁保护了
			#故仅非TDCP接口且need_lock才需要加锁保护
			if [ -n "$2" ]&&[ "need_lock" == "$2" ]&&[ ! "true" == "$user_modify_env" ];then
				mutex_running_cfg_lock_take "genrate_config_id" "$$" "$$"  "0"
			fi
			local file_dir=${1%/*}
			if [ ! -d "$file_dir" ]||[ ! -f "$1" ];then
				mkdir -p $file_dir
			fi
			local config_id=`uuidgen -t`
			echo $config_id>$1
			if [ -n "$2" ]&&[ "need_lock" == "$2" ]&&[ ! "true" == "$user_modify_env" ];then
				mutex_running_cfg_lock_give "genrate_config_id" "$$" "$$"  "0"
			fi
		else
			echo "######error uuidgen should support when enable genrate_config_id!!!!!!!!">/dev/console
		fi
	fi
}

set_restore_begin()
{
	local file_dir=${RESTORE_DONE_FLAG%/*}
	mkdir -p $file_dir
	if [ ! -d "$file_dir" ]; then
		mkdir -p $file_dir
	fi

	echo 0 > $RESTORE_DONE_FLAG
	set_file_md5_info $RESTORE_DONE_FLAG
}

set_restore_done()
{
	local file_dir=${RESTORE_DONE_FLAG%/*}
	mkdir -p $file_dir
	if [ ! -d "$file_dir" ]; then
		mkdir -p $file_dir
	fi

	echo 1 > $RESTORE_DONE_FLAG
	set_file_md5_info $RESTORE_DONE_FLAG
}

is_restore_done()
{
	local file_dir=${RESTORE_DONE_FLAG%/*}
	local md5_dir=$file_dir".md5"
	local file_name=${RESTORE_DONE_FLAG##*/}

	local md5_path=$md5_dir"/"$file_name".md5"
	# no restore file
	if [ ! -e "$file_dir" ]&&[ ! -e "$md5_dir" ];then
		return 0
	fi
	if [ ! -f "$RESTORE_DONE_FLAG" ]&&[ ! -f "$md5_path" ];then
		return 0
	fi
	check_one_file_md5_info $RESTORE_DONE_FLAG
	if [ "$?" == "0" ];then
		local is_done=`cat $RESTORE_DONE_FLAG`

		echo  "is_restore_done flag=" $is_done>$DEBUG_SWITCH
		if [ -n "$is_done" ]&&[ "1" == "$is_done" ];then
			return 0
		fi
	fi
	echo  "is_restore_done no check failed">$DEBUG_SWITCH
	return 1
}

set_save_begin()
{
	local file_dir=${SAVE_DONE_FLAG%/*}
	mkdir -p $file_dir
	if [ ! -d "$file_dir" ]; then
		mkdir -p $file_dir
	fi
	
	echo 0 > $SAVE_DONE_FLAG
	set_file_md5_info $SAVE_DONE_FLAG
}

set_save_done()
{
	local file_dir=${SAVE_DONE_FLAG%/*}
	mkdir -p $file_dir
	if [ ! -d "$file_dir" ]; then
		mkdir -p $file_dir
	fi
	echo 1 > $SAVE_DONE_FLAG
	set_file_md5_info $SAVE_DONE_FLAG
}

is_save_done()
{
	local file_dir=${SAVE_DONE_FLAG%/*}
	local md5_dir=$file_dir".md5"
	local file_name=${SAVE_DONE_FLAG##*/}
	
	local md5_path=$md5_dir"/"$file_name".md5"
	if [ ! -e "$file_dir" ]&&[ ! -e "$md5_dir" ];then
		return 0
	fi
	if [ ! -f "$SAVE_DONE_FLAG" ]&&[ ! -f "$md5_path" ]; then
		echo  "is_save_done no done file" >$DEBUG_SWITCH
		return 0
	fi
	check_one_file_md5_info $SAVE_DONE_FLAG
	if [ "$?" == "0" ];then
		local is_done=`cat $SAVE_DONE_FLAG`
		
		echo  "is_save_done flag=" $is_done>$DEBUG_SWITCH
		if [ -n "$is_done" ]&&[ "1" == "$is_done" ];then
			return 0
		fi
	fi
	#save done is not ok then print to check_result
	output_check_result "save_done" $CHECK_CFG_RESULT_DIR
	return 1
}

non_uci_cfg_not_in_check_fail_list()
{
	local target_file="$1"
	if [ -n "$target_file" ]&&[ -f "$CHECK_CFG_RESULT_DIR" ]; then
		cat $CHECK_CFG_RESULT_DIR | while read line
		do
			if [ "$target_file" == "$line" ];then
				return 1
			fi
		done
		if [ "1" == "$?" ];then
			return 1
		fi
	fi
	return 0
}

get_config_id()
{
	local support_config_id=`uci get $CFG_ID_GENERATE -q`
	if [ "on" == "$support_config_id" ];then
		mutex_flash_protect_lock_take "get_config_id" "$$" "$$"  "1"
		if [ -f ${CFG_DATA_DIR}"config_id/config_id" ]; then
			cp -f $CFG_DATA_DIR"config_id/config_id" $1
		else
			cat /dev/null >$1
		fi
		mutex_flash_protect_lock_give "get_config_id" "$$" "$$"  "1"
	fi
	echo ""
}

check_dir_md5_status()
{
	echo "check_dir_md5_status">$DEBUG_SWITCH
	local tmp_path="$1"
	local result_path="$2"
	if [ -e "$tmp_path"  ]&&[ -d "$tmp_path"  ]; then
		for file in `ls $tmp_path`
		do
			local file_name=`echo $tmp_path"/"$file | sed -e 's/\/\//\//g'`
			if [ ! -e "$file_name" ];then
				echo "=======$file_name no exist">$DEBUG_SWITCH
			fi
			
			if [ -d "$file_name" ]; then
				local extension="${file_name##*.}"
				if [ ! "md5" == "$extension" ];then
					local dir_md5_dir=$file_name".md5"
					if [ ! -d "$dir_md5_dir" ]; then
						echo "=======$dir_md5_dir no exist">$DEBUG_SWITCH
						output_check_result $file_name $result_path
					fi
				else
					local file_dir=`echo ${file_name} |  sed -e 's/.md5//g'`
					if [ ! -d "$file_dir" ]; then
						echo "========$file_dir no exist">$DEBUG_SWITCH
						output_check_result $file_dir $result_path
					fi
				fi
				
				check_dir_md5_status $file_name $result_path
				if [ ! "$?" == "0" ]; then 
					echo "==========$check_dir_md5_status $file_name fail">$DEBUG_SWITCH
					output_check_result $file_name $result_path
				fi
			elif [ -f "$file_name" ]; then
				local extension="${file_name##*.}"
				if [ ! "md5" == "$extension" ];then
					check_one_file_md5_info $file_name
					if [ ! "$?" == "0" ];then
						echo "==========$file_name fail">$DEBUG_SWITCH
						output_check_result $file_name $result_path
					fi
				else
					local file_dir=`echo ${file_name%/*} |  sed -e 's/.md5//g'`
					local tmp_file_name=`echo ${file_name##*/} |  sed -e 's/.md5//g'`
					local file_path=`echo $file_dir"/"$tmp_file_name | sed -e 's/\/\//\//g'`
					echo "md5 file==="$file_name $file_dir $tmp_file_name $file_path>$DEBUG_SWITCH
					if [ ! -d "$file_dir" ]||[ ! -f "$file_path" ]; then
						echo "$file_dir  $file_path no exist">$DEBUG_SWITCH
						output_check_result $file_path $result_path
					fi
				fi
			else
				echo "$file_name is no file or dir">$DEBUG_SWITCH
			fi
		done
		return 0
	fi
	return 1
}

#有特殊需求要求一致性的，需要向配置管理模块注册拷贝和拷贝完成回调
cp_specific_cfgs_to_tmp()
{
	let i=0
	local dst_path="$1"
	cat $COPY_CFG_TO_TMP_REGISTER_FILE | while read cbs
	do
		local init_cb=`echo $cbs | cut -d "#" -f 1`
		if [ -n "$init_cb" ]; then
			echo "cp_specific_cfgs_to_tmp init_cb==$init_cb">$DEBUG_SWITCH
			$(eval ${init_cb}) >/dev/null  2>&1
		fi
	done

	while [ $i -lt 800 ]; do
		cat $COPY_CFG_TO_TMP_REGISTER_FILE | while read cbs
		do
			local cp_cb=`echo $cbs | cut -d "#" -f 2`
			if [ -n "$cp_cb" ]; then
				local cp_exe="$cp_cb  $dst_path"
				echo "cp_specific_cfgs_to_tmp cp_exe==$cp_exe">$DEBUG_SWITCH
				$(eval ${cp_exe}) >/dev/null  2>&1
				if [ "$?" == "1" ]; then
					return 1
				fi
			fi
		done
		local ret="$?"
		usleep 25000
		let i+=1
		if [ "$ret" == "0" ];then
			break;
		fi
	done
}

local_cp_non_uci_to_tmp()
{
	echo "local_cp_non_uci_to_tmp"  $1 $2>$DEBUG_SWITCH
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	mkdir -p $tmp_file_name$1

	#保存配置都需要产生一个configid，避免shell操作配置触发保存配置不更新configid的
	genrate_config_id $RUNNING_CONFIG_ID_FILE

	if [ -n "$2" ]&&[ ! "all" == "$2" ];then
		local file_config=`uci get cfg_info.$2.file_config -q`
		local dir_config=`uci get cfg_info.$2.dir_config -q`
		echo "cp file list $file_config $dir_config">$DEBUG_SWITCH
		if [ -n "$file_config" ]; then
			for files in $file_config
			do
				local file_path=`echo $files | sed -e "s/\/etc\/nouci_config//g"`
				local file_dir=${file_path%/*}
				local tmp_file_path=`echo ${tmp_file_name}${1}${file_dir} | sed -e 's/\/\//\//g'`
				mkdir -p $tmp_file_path
				cp -f $files $tmp_file_path
				echo "cp -f $files $tmp_file_path">$DEBUG_SWITCH
			done
		fi
		if [ -n "$dir_config" ]; then
			for dirs in $dir_config
			do
				local dir_path=`echo $dirs | sed -e "s/\/etc\/nouci_config//g"`
				local dir_dir=${dir_path%/*}
				local tmp_dir_path=`echo ${tmp_file_name}${1}${dir_dir} | sed -e 's/\/\//\//g'`
				mkdir -p $tmp_dir_path
				cp -rf $dirs $tmp_dir_path
				echo "cp -rf $dirs $tmp_dir_path">$DEBUG_SWITCH
			done
		fi
	else
		echo "cp all">$DEBUG_SWITCH
		cp -rf $NON_UCI_PATH"." $tmp_file_name$1
	fi
	#step2 拷贝需要支持一致性的配置
	cp_specific_cfgs_to_tmp $tmp_file_name$1
}

local_cp_uci_packags_to_tmp()
{
	configs="$1"
	echo "cp $1 $2 $3">$DEBUG_SWITCH
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	local suffix_str=""
	if [ "change_suffix" == "$3" ]; then
		suffix_str="$UCI_FILE_SUFFIX"
	fi
	
	mkdir -p  $tmp_file_name$SAVE_UCI_TMP_DIR
	for config in $configs
	do
		cp -f $CFG_DIR$config $tmp_file_name$SAVE_UCI_TMP_DIR$config$suffix_str >/dev/null  2>&1
	done
}

#把src中optsrc的内容移动到dst的optdst上，若src没有，需要把dst的optdst删除
move_uci_op_src_to_dst_delete_when_none()
{
	local ops_src="$1"
	local src="$2"
	local ops_dst="$3"
	local dst="$4"
	
	echo "ops "$ops_src $src $ops_dst $dst>$DEBUG_SWITCH
	local pacs_src=`echo $ops | cut -d "." -f 1`
	local pacs_dst=`echo $ops_dst | cut -d "." -f 1`
	
	local src_file=`echo $src"/"$pacs | sed -e 's/\/\//\//g'`
	local dst_file=`echo $dst"/"$pacs_dst | sed -e 's/\/\//\//g'`
	

	if [ -n "$pacs_dst" ]&&[ -f "$dst_file" ]; then
		echo "pacs "$pacs_src $pacs_dst $src_file $dst_file>$DEBUG_SWITCH
		local value=`uci get $ops_src -c $src`
		echo $ops_src"='"$value"'" -c $src>$DEBUG_SWITCH
		if [ -n "$value" ];then
			local cmd="uci set $ops_dst=\"${value}\" -c $dst"
			$(eval ${cmd}) >/dev/null  2>&1
			echo $cmd>$DEBUG_SWITCH
			#uci set $ops="${value}" -c $dst
			uci commit $pacs_dst -c  $dst
		else
			local cmd="uci delete $ops_dst -c $dst"
			$(eval ${cmd}) >/dev/null  2>&1
			echo $cmd>$DEBUG_SWITCH
			uci commit $pacs_dst -c  $dst
		fi
	fi
}

#若果源为空，需要删除目的值
cp_uci_op_src_to_dst_delete_when_none()
{
	local ops="$1"
	local src="$2"
	local dst="$3"
	
	echo "ops "$ops $src $dst>$DEBUG_SWITCH
	local pacs=`echo $ops | cut -d "." -f 1`
	
	local _tmp_file=`echo $src"/"$pacs | sed -e 's/\/\//\//g'`
	
	if [ -n "$pacs" ]&&[ -f "$_tmp_file" ]; then
		echo "pacs "$pacs $src>$DEBUG_SWITCH
		#即使pac sec不存在，不影响value
		local value=`uci get $ops -c $src`
		echo $ops"='"$value"'" -c $dst>$DEBUG_SWITCH
		if [ -n "$value" ];then
			local cmd="uci set $ops=\"${value}\" -c $dst"
			$(eval ${cmd}) >/dev/null  2>&1
			echo $cmd>$DEBUG_SWITCH
			uci commit $pacs -c  $dst
		else
			local cmd="uci delete $ops -c $dst"
			$(eval ${cmd}) >/dev/null  2>&1
			echo $cmd>$DEBUG_SWITCH
			uci commit $pacs -c  $dst
		fi
	fi
}

remian_uci_to_tmp_when_restore_fac()
{
	local running_package_config=`uci get cfg_info.fac_remain_list.running_package_config -q`
	local runing_op_config_list=`uci get cfg_info.fac_remain_list.running_opt_config -q`
	
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
    mkdir -p  $tmp_file_name$SAVE_UCI_TMP_DIR
	get_uci_from_flash $tmp_file_name$SAVE_UCI_TMP_DIR "change_suffix"
	
	if [ -n "$running_package_config" ]; then
		local_cp_uci_packags_to_tmp "$running_package_config" "remian_uci_to_tmp_when_restore_fac" "change_suffix"
	fi
	if [ -n "$runing_op_config_list" ]; then
		local pacs=""
		for ops in $runing_op_config_list
		do
			local pacs_tmp=`echo $ops | cut -d "." -f 1`
			if [ -n "$pacs_tmp" ];then
				local opt_str=`add_optstr_pac_suffix_handle $ops "change_suffix"`
				local pacs_str=`add_pac_suffix_handle $pacs_tmp "change_suffix"`
				if [ ! -f "$tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str" ]; then
					cp $CFG_DIR$pacs_tmp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str
				fi
				#把运行时不带后缀的opt 赋值到带后缀的临时文件中
				move_uci_op_src_to_dst_delete_when_none  $ops $CFG_DIR $opt_str  $tmp_file_name$SAVE_UCI_TMP_DIR
			fi
		done
	fi
}

_remian_one_op_when_restore_fac()
{
	local file_dir=${RESTORE_FAC_REMAIN_FILE%/*}
	local md5_dir=$file_dir".md5"
	local site_file=${RESTORE_FAC_REMAIN_FILE##*/}
	
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	if [ ! -f "$RESTORE_FAC_REMAIN_FILE" ]; then
		mkdir -p $file_dir
		mkdir -p $md5_dir
		touch $RESTORE_FAC_REMAIN_FILE
	fi
	local opt_str=`add_optstr_pac_suffix_handle $1 $2`
	local value=`uci get $opt_str -c $tmp_file_name$SAVE_UCI_TMP_DIR`
	#add to backup file
	echo $1"="$value>$DEBUG_SWITCH
	echo $1"="$value >> $RESTORE_FAC_REMAIN_FILE
}
remain_op_list_when_restore_fac()
{
	local runing_op_config_list=`uci get cfg_info.fac_remain_list.running_opt_config -q`
	local saved_opt_config=`uci get cfg_info.fac_remain_list.saved_opt_config -q`
	echo "remain_op_list_when_restore_fac " $runing_op_config_list $saved_opt_config>$DEBUG_SWITCH
	rm -rf $RESTORE_FAC_REMAIN_FILE

	if [ -n "$runing_op_config_list" ]||[ -n "$saved_opt_config" ]; then
		for ops in $runing_op_config_list
		do
			_remian_one_op_when_restore_fac $ops "$1"
		done
		
		for ops in $saved_opt_config
		do
			_remian_one_op_when_restore_fac $ops "$1"
		done

		
		set_file_md5_info $RESTORE_FAC_REMAIN_FILE
	fi
}

_remian_one_pac_when_restore_fac()
{
	local file_dir=${RESTORE_FAC_REMAIN_FILE%/*}
	
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	echo "_remian_one_pac_when_restore_fac "$1  >$DEBUG_SWITCH
	local pacs=`add_pac_suffix_handle $1 $2`
	local _tmp_file=`echo $tmp_file_name$SAVE_UCI_TMP_DIR"/"$pacs | sed -e 's/\/\//\//g'`
	local _dst_file=`echo $file_dir"/"$1 | sed -e 's/\/\//\//g'`
	mkdir -p $file_dir
	cp -f $_tmp_file $_dst_file
	#add to backup file
	set_file_md5_info $_dst_file
}

remian_one_pacs_when_restore_fac()
{
	local running_package_config=`uci get cfg_info.fac_remain_list.running_package_config -q`
	local saved_package_config=`uci get cfg_info.fac_remain_list.saved_package_config -q`
	if [ -n "$running_package_config" ]||[ -n "$saved_package_config" ]; then
		local file_dir=${RESTORE_FAC_REMAIN_FILE%/*}
		local md5_dir=$file_dir".md5"
		local site_file=${RESTORE_FAC_REMAIN_FILE##*/}
		for pacs in $running_package_config
		do
			_remian_one_pac_when_restore_fac $pacs "$1"
		done
		for pacs in $saved_package_config
		do
			_remian_one_pac_when_restore_fac $pacs "$1"
		done
	fi
	
}

cp_remain_when_restore_fac()
{
	mutex_running_cfg_lock_take "restore_fac" "$$" "$$"  "1" "$1"
	remian_uci_to_tmp_when_restore_fac
	mutex_running_cfg_lock_give "restore_fac" "$$" "$$"  "1"
}

reg_resotre_fac_cmd_cb()
{
	if [ -f "$RESTORE_FAC_REG_FILE" ]; then
		cat $RESTORE_FAC_REG_FILE | while read line
		do
			if [ -n "$line" ];then
				echo $line >$DEBUG_SWITCH
				$(eval ${line}) >/dev/null  2>&1
			fi
		done
	fi
}

#恢复出厂时，删除非uci分区文件，但需要保留记录恢复出厂过程的文件，该文件用于标记恢复出厂是否完成
restore_non_uci_fac()
{
	local retain_cfg_dir=${RESTORE_DONE_FLAG%/*}
	local retain_cfg_md5_dir=${RESTORE_DONE_FLAG%/*}".md5"
	for file in `ls $CFG_DATA_DIR`
	do
		local delete_file=$CFG_DATA_DIR$file
		if [ "$delete_file" == "$retain_cfg_dir" ]||[ "$delete_file" == "$retain_cfg_md5_dir" ];then
			continue
		elif [ -n "$file" ];then
			rm -rf $delete_file
		fi
	done
}

restore_fac()
{
	local is_fast="$2"
	local is_reboot="$1"
	local slpresotre_cmd="-n"
	if [ -n "$is_fast" ]&&[ "f" == "$is_fast" ];then
		slpresotre_cmd="-nf"
	fi
	#恢复出厂前，需要把影响flash操作一些操作停止，否则可能会出现，刚恢复出厂，然后配置又会被更改了
	#场景1：恢复出厂，然后再重启之前，异步延迟保存触发save all，在reboot拿到flash锁，前save all先拿到锁
	#        结果是，恢复出厂执行了，但是最后结果有相关配置被保存了
	forbid_to_save_cfg
	
	mutex_flash_protect_lock_take "restore_fac" "$$" "$$"  "1"

	set_restore_begin
	
	restore_non_uci_fac

	remian_one_pacs_when_restore_fac  "change_suffix"
	remain_op_list_when_restore_fac "change_suffix"
	
	check_dir_md5_status $CFG_DATA_DIR
	if [ ! "$?" == "0" ]; then
		echo "restore non uci fac failed======clean again" > /dev/console
	fi
	
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	rm -rf $tmp_file_name
	
	
	rm -rf /overlay/*
	reg_resotre_fac_cmd_cb
	 
	sleep 1

	/sbin/slprestore "$slpresotre_cmd"
	set_restore_done
	mutex_flash_protect_lock_give "restore_fac" "$$" "$$"  "1"
	if [ "1" == "$is_reboot" ];then
		reboot -f
	else
		#重新开放命令队列
		allow_to_save_cfg
	fi
}

fac_remain_uci_restore_to_run_check()
{
	local file_dir=${RESTORE_FAC_REMAIN_FILE%/*}
	local md5_dir=$file_dir".md5"
	echo "fac_remain_uci_restore_to_run_check  "$file_dir $md5_dir >$DEBUG_SWITCH
	if [ ! -n "$md5_dir" ];then
		echo "md5sum check fail $flash_path $file_dir, going to restore to factory" >$DEBUG_SWITCH
		return 0
	fi
	
	for files in `ls $file_dir`
	do 
		local file_path=`echo $file_dir"/"$files | sed -e 's/\/\//\//g'`
		
		check_one_file_md5_info $file_path
		if [ "$?" -ne "0" ];then
			#md5sum check fail
			echo "md5sum check fail $file_path, going to restore to factory" >$DEBUG_SWITCH
			return 0
		fi
	done
	return 1
}

fac_remain_uci_restore_to_run()
{
	local file_dir=${RESTORE_FAC_REMAIN_FILE%/*}
	local site_file=${RESTORE_FAC_REMAIN_FILE##*/}
	
	echo "fac_remain_uci_restore_to_run $file_dir $site_file">$DEBUG_SWITCH
	
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	
	if [ -e "$file_dir" ]; then
	
		fac_remain_uci_restore_to_run_check
		
		if [ ! "$?" == "0" ]; then
	
			mkdir -p  $tmp_file_name$SAVE_UCI_TMP_DIR
			#由于处于preinit阶段，不会有其他的操作运行时配置，故黑名单恢复可以直接操作在运行时目录
			#restore cfg list
			for files in `ls $file_dir`
			do 
				local _tmp_file=`echo $file_dir"/"$files | sed -e 's/\/\//\//g'`
				
				if [ ! "$RESTORE_FAC_REMAIN_FILE" == "$_tmp_file" ]; then
					local _dst_file_tmp=`echo $CFG_DIR"/"$files | sed -e 's/\/\//\//g'`
					echo "cp =="$_tmp_file  $_dst_file_tmp>$DEBUG_SWITCH
					cp -f $_tmp_file  $_dst_file_tmp
				fi
			done
			#restore op list
			if [ -f "$RESTORE_FAC_REMAIN_FILE" ]; then
				cat $RESTORE_FAC_REMAIN_FILE | while read line
				do
					echo "uci set $line ">$DEBUG_SWITCH
					local str1=`echo $line | cut -d "=" -f 1`
					local pac_name=`echo $str1 | cut -d "." -f 1`
					uci set $line
					uci commit $pac_name
				done
			fi
			write_uci_backup_to_flash $CFG_DIR
			if [ ! "$?" == "0" ]; then
				echo "fac_remain_uci_restore_to_run write_uci_backup_to_flash failed">/dev/console
				return 1
			fi
		fi
	fi
	clean_file_and_md5_info $file_dir
}

#一些特殊配置文件说明，比如save_done和恢复出厂标记文件。
#某些场景需要判断文件是否属于运行时相关的，若非相关的要删除，比如保存配置最后一步，要把flash中不存在于运行时的删除
is_runing_reference_cfg()
{
	local save_done_file_dir=${SAVE_DONE_FLAG%/*}
	local restore_done_file_dir=${RESTORE_DONE_FLAG%/*}

	if [ ! "$save_done_file_dir" == "$1" ]&&[ ! "$restore_done_file_dir" == "$1" ];then
		return 0
	fi
	return 1
}

#when save all cfg, the first should clean cfg not exist in tmp
#this means that, none exist cfg had been delete,so flash should sync to delete
rm_none_exist_files()
{
	local src_file="$1"
	local dst_file="$2"
	echo "rm_none_exist_files" $src_file $dst_file >$DEBUG_SWITCH
	

	#文件所在目录为is_save_done，直接跳过，不属于配置
	is_runing_reference_cfg "$dst_file"
	if [ "0" == "$?" ];then
		if [ -e "$dst_file"  ]&&[ ! -e "$src_file" ]; then
			local extension="${dst_file##*.}"
			if [ ! "md5" == "$extension" ]; then
				echo "===////goning to delete files: $dst_file">$DEBUG_SWITCH
				clean_file_and_md5_info $dst_file
			fi
		fi
		
		if [ -e "$dst_file"  ]&&[ -e "$src_file" ]; then
			for file in `ls $dst_file`
			do
				local extension="${file##*.}"
				echo "test rm  files =="$file "in "$dst_file>$DEBUG_SWITCH
				if [ ! "md5" == "$extension" ]; then
					local _tmp_src_file=`echo $src_file"/"$file | sed -e 's/\/\//\//g'`
					local _tmp_dst_file=`echo $dst_file"/"$file | sed -e 's/\/\//\//g'`
					if [ -d "$_tmp_dst_file" ]; then
						#目录为is_save_done，直接跳过，不属于配置

						is_runing_reference_cfg "$_tmp_dst_file"
						if [ "0" == "$?" ];then
							if [ ! -d  "$_tmp_src_file" ]; then
								echo "3goning to delete files: $_tmp_dst_file">$DEBUG_SWITCH
								clean_file_and_md5_info $_tmp_dst_file
							else
								rm_none_exist_files $_tmp_src_file $_tmp_dst_file
							fi
						fi
						
					elif [ -f "$_tmp_dst_file" ];then
						if [ ! -f "$_tmp_src_file" ]; then
							echo "4goning to delete files: $_tmp_dst_file">$DEBUG_SWITCH
							clean_file_and_md5_info $_tmp_dst_file
						fi
					fi
				fi
			done
		fi
	fi
}

_save_non_uci()
{
	echo "_save_non_uci" $1 $2 $3 >$DEBUG_SWITCH
	local tmp_path="$1"
	local flash_path="$2"
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	local file_config=""
	local dir_config=""
	if [ -n "$3" ]&&[ ! "all" == "$3" ];then
		file_config=`uci get cfg_info.$3.file_config -q`
		dir_config=`uci get cfg_info.$3.dir_config -q`
	fi
	
	if [ -e "$tmp_path"  ]; then
		mkdir -p $flash_path
		for file in `ls $tmp_path`
		do
			local mem_file=`echo $tmp_path"/"$file | sed -e 's/\/\//\//g'`
			if [ -d "$mem_file" ]; then
				local _tmp_path=`echo $1"/"$file | sed -e 's/\/\//\//g'`
				local _flash_path=`echo $2"/"$file | sed -e 's/\/\//\//g'`
				local save_type="$3"
				if [ -n "$dir_config" ]; then
					for dirs in $dir_config
					do
						local file_dir=`echo $dirs | sed -e "s/\/etc\/nouci_config//g"`
						local tmp_file_dir=`echo $tmp_file_name${SAVE_NON_UCI_TMP_DIR}${file_dir} | sed -e 's/\/\//\//g'`
						
						if [ "$mem_file" == "$tmp_file_dir" ];then
							save_type="all"
							echo "============save dir"$dirs>$DEBUG_SWITCH
							break;
						fi 
					done
				fi
				_save_non_uci "$_tmp_path" "$_flash_path" "$save_type"
				if [ "$?" -eq "0" ]; then
					echo "save $_flash_path $?">$DEBUG_SWITCH
					mkdir -p $_flash_path".md5"
				fi
				
			elif [ -f "$mem_file" ]; then
				local can_save="on"
				if [ -n "$file_config" ]; then
					can_save="off"
					for save_files in $file_config
					do
						local file_name=${save_files##*/}
						local file_dir=${save_files%/*}
						file_dir=`echo $file_dir | sed -e "s/\/etc\/nouci_config//g"`
						local tmp_file_dir=`echo $tmp_file_name${SAVE_NON_UCI_TMP_DIR}${file_dir}"/"$file_name | sed -e 's/\/\//\//g'`

						if [ "$mem_file" == "$tmp_file_dir" ];then
							can_save="on"
							break;
						fi
					done
				fi

				if [ "$can_save" == "on" ]; then
					local tmp_file=`echo $flash_path"/"$file | sed -e 's/\/\//\//g'`
					local md5_path=${tmp_file%/*}".md5"
					if [ ! -d "$md5_path" ]; then
						mkdir -p  $flash_path"/"
						cp $tmp_path"/"$file $flash_path"/"$file
						set_file_md5_info $flash_path"/"$file
					else
						if [ ! -f ${md5_path}"/"${file}".md5" ]; then
							
							mkdir -p  $flash_path"/"
							cp $tmp_path"/"$file $flash_path"/"$file
							set_file_md5_info $flash_path"/"$file
						else
							local mem_md5_result=`md5sum $tmp_path"/"$file | awk '{print $1}' | head -n 1`
							flash_md5_result=`cat $md5_path"/"$file".md5" | awk '{print $1}' | head -n 1`
							if [ ! "${mem_md5_result}" == "${flash_md5_result}" ]; then
								mkdir -p  $flash_path"/"
								cp $tmp_path"/"$file $flash_path"/"$file
								set_file_md5_info $flash_path"/"$file
							fi
						fi
					fi
				fi
			fi
		done		
	fi
}

non_uci_save_handle()
{
	_save_non_uci  "$1"  "$2" "$3"
	if [ "$?" -ne "0" ]; then
		echo "save non uci fail, going to restore to factory" >/dev/console
	fi
	rm_none_exist_files "$1"  "$2"
}

_save_all_non_uci()
{
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	non_uci_save_handle $tmp_file_name$1 $2 "$3"
	rm -rf $tmp_file_name$1
}

local_all_uci_cp_to_tmp()
{
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	mkdir -p $tmp_file_name$1
	echo $CFG_DIR $tmp_file_name$1>$DEBUG_SWITCH
	cp -rf $CFG_DIR"." $tmp_file_name$1
}

_save_all_uci()
{
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	write_uci_backup_to_flash $tmp_file_name$1
	if [ ! "$?" == "0" ]; then
		echo "_save_all_uci write_uci_backup_to_flash failed">/dev/console
		return 1
	fi
	echo $tmp_file_name$1>$DEBUG_SWITCH
	rm -rf $tmp_file_name$1
}

cp_all_cfg_to_tmp()
{
	mutex_running_cfg_lock_take "cp_all_cfg_to_tmp" "$$" "$$"  "1" "$1"
	local_cp_non_uci_to_tmp $SAVE_NON_UCI_TMP_DIR
	local_all_uci_cp_to_tmp $SAVE_UCI_TMP_DIR
	
	mutex_running_cfg_lock_give "cp_all_cfg_to_tmp" "$$" "$$"  "1"
}

_save_all_config()
{
	echo "_save_all_config" > $DEBUG_SWITCH

	mutex_flash_protect_lock_take "_save_all_config" "$$" "$$"  "1"
	# means save begin
	set_save_begin
	_save_all_non_uci $SAVE_NON_UCI_TMP_DIR $CFG_DATA_DIR "all"

	_save_all_uci $SAVE_UCI_TMP_DIR
	# means save end
	set_save_done
	mutex_flash_protect_lock_give "_save_all_config" "$$" "$$"  "1"
	rm -rf $tmp_file_name
	
}

get_progress_file_flag()
{
	if [ -f "$1" ]; then
		return $PROGRESS_STATUS_END
	fi
	return $PROGRESS_STATUS_BEGIN
}

save_all_config()
{
	_save_all_config

	rm -rf $MODIFY_FILE_FLAG
	#与cfgSave入口请求 save all命令成对使用。其中入口是删除该文件
	touch $SAVE_FINISH_FLAG
	echo "save_all_config finish">$DEBUG_SWITCH
}

#$1 srcpath
#$2 dstpath
#$3 filter表征待次后缀的文件不拷贝，目前主要应用场景是flash中的md5文件不拷贝到运行时目录
cp_files_with_filter()
{
	echo "cp_files_with_filter" $1 $2>$DEBUG_SWITCH
	local mem_path=$1
	local flash_path=$2
	
	if [ -e "$flash_path" ]; then
		for file in `ls $flash_path`
		do
			local check_file=`echo  $flash_path"/"$file | sed -e 's/\/\//\//g'`
			local mem_file=`echo  $mem_path"/"$file | sed -e 's/\/\//\//g'`
			if [ -d "$check_file" ]; then
				is_runing_reference_cfg "$check_file"
				if [ "0" == "$?" ];then
					local extension="${check_file##*.}"
					if [ ! -n "$3" ]||[ ! "$3" == "$extension" ]; then
						cp_files_with_filter $mem_file $check_file "$3"
					fi
				fi
			elif [ -f "$check_file" ];then
				local extension="${check_file##*.}"
				if [ ! -n "$3" ]||[ ! "$3" == "$extension" ];then
					mkdir -p $mem_path
					
					echo "cp -f "$check_file $mem_file>$DEBUG_SWITCH
					cp -f $check_file $mem_file
				fi
			else
				echo "invalid file path $flash_path $file" >$DEBUG_SWITCH
			fi
		done
	fi
}

config_restore_non_uci()
{
	
	check_dir_md5_status $CFG_DATA_DIR  $CHECK_CFG_RESULT_DIR
	cp_files_with_filter  $NON_UCI_PATH $CFG_DATA_DIR "md5"
}

check_cfg_status()
{
	case "$1" in
        "non_uci")
            is_restore_done
			return $?
            ;;
		"file")
			non_uci_cfg_not_in_check_fail_list "$2"
			return $?
			;;
        *)
            exit 0;;

    esac
	
	return 0
}


#read non uci cfg form flash
config_read()
{
	echo "config_read" >$DEBUG_SWITCH

	is_save_done
	if [ "$?" -ne "0" ];then
		echo "is_save_done not ok===" > /dev/console
	fi

	# restore site
	fac_remain_uci_restore_to_run >/dev/null  2>&1
	if [ "$?" -ne "0" ];then
		echo "fac_remain_uci_restore_to_run not ok===" > /dev/console
	fi
	config_restore_non_uci >/dev/null  2>&1
	 
}

check_async_trigger()
{
	local async_save_env=`envop getenv async_save_env`
	if [ "$async_save_env" == "true" ];then
		return 0
	fi
	return 1
}

# 用户修改（TDCP接口）的配置才涉及保存相关操作
cfg_modify() {
	local is_outside_modify="$1"
	local user_modify_env=`envop getenv user_modify_env`
	echo "cfg_modify $user_modify_env">$DEBUG_SWITCH
	check_async_trigger
	if [ "$?" == "0" ]&&[ "true" == "$user_modify_env" ]; then
		cfgSave -d 0
	else
		local save_immediate=`uci get cfg_info.settings.save_immediate`
		if [ "on" == "$save_immediate" ]&&[ "true" == "$user_modify_env" ];then
			cfgSave
		elif [ "on" == "$save_immediate" ]&&[ "save" == "$is_outside_modify" ];then
			cfgSave
		else
			touch $MODIFY_FILE_FLAG
		fi
	fi
	genrate_config_id $RUNNING_CONFIG_ID_FILE "need_lock"
}

#1: modify 0:not modify
get_cfg_modify() 
{

	if [ -f "$MODIFY_FILE_FLAG" ]; then
		return 1
	fi
	return 0
}

# reutrn 0:OK
#1:md5 fail
#2:product null
#3:hw_id fail
#4:cfg ver  fail
#5：model、hwver fail
import_file_check()
{
	echo "import_file_check  "$1>$DEBUG_SWITCH
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	#恢复配置文件信息
	tar -zxvf $1 -C $tmp_file_name  >/dev/null  2>&1
	#md5 check
	cat $MD5_CONF>$DEBUG_SWITCH
	md5sum $tmp_file_name$TMP_FILE>$DEBUG_SWITCH
	local mem_md5_result=`md5sum $tmp_file_name$TMP_FILE | awk '{print $1}' | head -n 1`
	local flash_md5_result=`cat $tmp_file_name$MD5_CONF | awk '{print $1}' | head -n 1`
	if [ "$mem_md5_result" != "$flash_md5_result" ];then
		echo "md5 check fail for import" $mem_md5_result $flash_md5_result>/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 1
	fi
	#PRODUCT_CONF check
	local hw_id=`ubus call tddpServer getInfo '{"infoMask":1023}' | grep hw_id | awk '{print $2}' | head -n 1 | sed -e 's/\"//g'| sed -e 's/,//g'`

	local product_info=`cat $tmp_file_name$PRODUCT_CONF`
	if [ ! -n "$product_info" ];then
		echo "product_info check fail for import $tmp_file_name$PRODUCT_CONF $product_info">/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 2
	fi
	local tmp_hw_id=`echo $product_info | cut -d " " -f 1`
	if [ ! "$hw_id" == "$tmp_hw_id" ];then
		echo "hw_id check fail for import""$hw_id"  $tmp_hw_id>/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 3
	fi
	local cfg_ver=`uci get cfg_info.settings.version`
	local cfg_ver_major=`echo $cfg_ver | cut -d "." -f 1`
	cfg_ver_major=`printf "%d\n" $cfg_ver_major`
	local cfg_ver_minor=`echo $cfg_ver | cut -d "." -f 2`
	cfg_ver_minor=`printf "%d\n" $cfg_ver_minor`
	local cfg_ver_minor1=`echo $cfg_ver | cut -d "." -f 3`
	cfg_ver_minor1=`printf "%d\n" $cfg_ver_minor1`
	
	echo $cfg_ver>$DEBUG_SWITCH
	echo $cfg_ver_major>$DEBUG_SWITCH
	echo $cfg_ver_minor>$DEBUG_SWITCH
	echo $cfg_ver_minor1>$DEBUG_SWITCH
	
	local tmp_cfg_ver=`echo $product_info | cut -d " " -f 2`
	local tmp_cfg_ver_major=`echo $tmp_cfg_ver | cut -d "." -f 1`
	tmp_cfg_ver_major=`printf "%d\n" $tmp_cfg_ver_major`
	local tmp_cfg_ver_minor=`echo $tmp_cfg_ver | cut -d "." -f 2`
	tmp_cfg_ver_minor=`printf "%d\n" $tmp_cfg_ver_minor`
	local tmp_cfg_ver_minor1=`echo $tmp_cfg_ver | cut -d "." -f 3`
	tmp_cfg_ver_minor1=`printf "%d\n" $tmp_cfg_ver_minor1`
	
	echo $tmp_cfg_ver>$DEBUG_SWITCH
	echo $tmp_cfg_ver_major>$DEBUG_SWITCH
	echo $tmp_cfg_ver_minor>$DEBUG_SWITCH
	echo $tmp_cfg_ver_minor1>$DEBUG_SWITCH
	
	if [ "$cfg_ver_major" -lt "$tmp_cfg_ver_major" ];then
		echo "cfg_ver_major check fail for import">/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 4
	fi
	
	if [ "$cfg_ver_major" -eq "$tmp_cfg_ver_major" ]&&[ "$cfg_ver_minor" -lt "$tmp_cfg_ver_minor" ];then
		echo "cfg_ver_minor check fail for import">/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 4
	fi
	
	if [ "$cfg_ver_major" -eq "$tmp_cfg_ver_major" ]&&[ "$cfg_ver_minor" -eq "$tmp_cfg_ver_minor" ]&&[ "$cfg_ver_minor1" -lt "$tmp_cfg_ver_minor1" ];then
		echo "cfg_ver_minor1 check fail for import">/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 4
	fi
	
	local device_model=`echo $product_info | cut -d " " -f 3`
	local hw_ver=`echo $product_info | cut -d " " -f 4`
	if [ -n "$device_model" ]&&[ -n "$hw_ver" ];then
		local tmp_device_model=`uci get device_info.info.device_model`
		local tmp_hw_ver=`uci get device_info.info.hw_version`
		if [ ! "$tmp_device_model" == "$device_model" ]||[ ! "$tmp_hw_ver" == "$hw_ver" ];then
			echo "device_model or hw_ver check fail for import"  $device_model  $tmp_device_model  $hw_ver $tmp_hw_ver>/dev/console
			rm -rf $tmp_file_name  >/dev/null  2>&1
			rm -f $1  >/dev/null  2>&1
			exit 5
		fi
	fi

	echo "check successfull">$DEBUG_SWITCH
	return 0
}

cfg_handle_progress_get()
{
	echo $1 $2>$DEBUG_SWITCH
	case "$1" in
        "import")
			get_progress_file_flag $IMPORT_PROGRESS
			local ret=$?
			if [ -f "$2" ]; then
				echo $ret>$2
			fi
			return $ret
            ;;
        "save_cfg")
			get_progress_file_flag $SAVE_FINISH_FLAG
			local ret=$?
			if [ -f "$2" ]; then
				echo $ret>$2
			fi
			return $ret
            ;;
        "config_id")
			get_config_id $2
			return $?
            ;;
        *)
            return 127
			;;
    esac
	return 0
}

cfg_handle_progress_clean()
{
	echo $1>$DEBUG_SWITCH
	case "$1" in
        "import")
			rm -f $IMPORT_PROGRESS  >/dev/null  2>&1
            ;;
        "save_cfg")
			rm -f $SAVE_FINISH_FLAG  >/dev/null  2>&1
            ;;
        *)
            echo "params invalid">/dev/console
			;;
    esac
}

cfg_handle_progress()
{
	if [ -n "$2" ]&&[ "delete" == "$2" ];then
		cfg_handle_progress_clean "$1"
	else
		cfg_handle_progress_get "$1" "$2"
		return $?
	fi
}

import_cfg_handle_cp_runing_blacklist_step()
{
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	local src_cfg_path="$tmp_file_name""$SAVE_UCI_TMP_DIR"
	
	#black list handle for uci
	local opt_list=`uci get cfg_info.import_black_list.running_opt_config -q`
	local pak_list=`uci get cfg_info.import_black_list.running_package_config -q`
	#把黑名单中运行时列表覆盖到解压后配置
	for pacs in $pak_list
	do
		if [ -f  "$src_cfg_path$pacs" ];then
			cp -f  $src_cfg_path$pacs  $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"  >/dev/null  2>&1
		else
			rm -f  $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs   >/dev/null  2>&1
		fi
	done

	for ops in $opt_list 
	do
		local pacs_tmp=`echo $ops | cut -d "." -f 1`
		local opt_str=`add_optstr_pac_suffix_handle $ops "change_suffix"`
		local pacs_str=`add_pac_suffix_handle $pacs_tmp "change_suffix"`
		if [ ! -f "$src_cfg_path$pacs_str" ]; then
			cp $src_cfg_path$pacs_tmp $src_cfg_path$pacs_str
		fi
		if [ ! -f ${tmp_file_name}${TMP_SAVE_PATH}${TMP_UC_CONFIG_PATH}"/"${pacs_str} ]; then
			mv $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str   >/dev/null  2>&1
		fi

		cp_uci_op_src_to_dst_delete_when_none $opt_str  $src_cfg_path $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"
		mv $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp   >/dev/null  2>&1
		rm -rf $src_cfg_path$pacs_str  >/dev/null  2>&1
	done
}

import_cfg_handle_cp_saved_blacklist_step()
{
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	local bind_status=`uci get cloud_config.bind_info.bind_status -q`
	#black list handle for uci
	local saved_opt_list=`uci get cfg_info.import_black_list.saved_opt_config -q`
	local saved_opt_custom_list=`uci get cfg_info.import_black_list.saved_opt_custom_config -q`
	local saved_pak_list=`uci get cfg_info.import_black_list.saved_package_config -q`
	#把黑名单中运行时列表覆盖到解压后配置
	if [ -n "$saved_opt_list" ]||[ -n "$saved_pak_list" ];then
		mkdir -p  $tmp_file_name$SAVE_UCI_TMP_DIR
		#uc分区内容加载到临时目录，用于黑名单拷贝，暂称该目录为旧uc目录
		get_uci_from_flash $tmp_file_name$SAVE_UCI_TMP_DIR
		if [ ! "$?" == "0" ]; then
			echo "get_uci_from_flash file"> /dev/console
			return 1
		fi
		
		for pacs in $saved_pak_list
		do
			if [ -f  "$tmp_file_name$SAVE_UCI_TMP_DIR$pacs" ];then
				cp -f  $tmp_file_name$SAVE_UCI_TMP_DIR$pacs  $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"  >/dev/null  2>&1
			else
				rm -f   $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs   >/dev/null  2>&1
			fi
		done

		for ops in $saved_opt_list
		do
			local pacs_tmp=`echo $ops | cut -d "." -f 1`
			local opt_str=`add_optstr_pac_suffix_handle $ops "change_suffix"`
			local pacs_str=`add_pac_suffix_handle $pacs_tmp "change_suffix"`
			#若旧uc目录无带后缀pac，需要拷贝一份否则无法操作
			if [ ! -f "$CFG_DIR$pacs_str" ]; then
				cp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_tmp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str  >/dev/null  2>&1
			fi
			#若待导入目录无待后缀pac，也需要拷贝一份
			if [ ! -f ${tmp_file_name}${TMP_SAVE_PATH}${TMP_UC_CONFIG_PATH}"/"${pacs_str} ]; then
				mv $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str   >/dev/null  2>&1
			fi
			#把旧uc目录相关的optionsset设置到待导入目录待后缀文件
			cp_uci_op_src_to_dst_delete_when_none $opt_str  $tmp_file_name$SAVE_UCI_TMP_DIR $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"
			#把带后缀即处理过的pac覆盖原pac
			mv  $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp   >/dev/null  2>&1
			rm -rf $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str  >/dev/null  2>&1
		done

		if [ -n "$saved_opt_custom_list" ]&&[ "$bind_status" == "1" ];then
			for ops in $saved_opt_custom_list
			do
				local pacs_tmp=`echo $ops | cut -d "." -f 1`
				local opt_str=`add_optstr_pac_suffix_handle $ops "change_suffix"`
				local pacs_str=`add_pac_suffix_handle $pacs_tmp "change_suffix"`
				#若旧uc目录无带后缀pac，需要拷贝一份否则无法操作
				if [ ! -f "$CFG_DIR$pacs_str" ]; then
					cp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_tmp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str  >/dev/null  2>&1
				fi
				#若待导入目录无待后缀pac，也需要拷贝一份
				if [ ! -f "${tmp_file_name}${TMP_SAVE_PATH}${TMP_UC_CONFIG_PATH}/${pacs_str}" ]; then
					mv $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str   >/dev/null  2>&1
				fi
				#把旧uc目录相关的optionsset设置到待导入目录待后缀文件
				cp_uci_op_src_to_dst_delete_when_none $opt_str  $tmp_file_name$SAVE_UCI_TMP_DIR $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"
				#把带后缀即处理过的pac覆盖原pac
				mv  $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp   >/dev/null  2>&1
				rm -rf $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str  >/dev/null  2>&1
			done
        fi
	fi
}

import_cfg_handle_cp()
{
	local ret=""
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	tar -zxvf $1 -C  $tmp_file_name  >/dev/null  2>&1
	if [ ! "$?" == "0" ]; then
		return  1
	fi
	
	mkdir -p  $tmp_file_name$TMP_SAVE_PATH  >/dev/null  2>&1
	tar -zxvf  $tmp_file_name$TMP_FILE -C  $tmp_file_name   >/dev/null  2>&1
	if [ ! "$?" == "0" ]; then
		return  1
	fi
	#decompress non-uci
	tar -zxvf  $tmp_file_name$NON_UCI_TMP_FILE  -C  $tmp_file_name$TMP_SAVE_PATH  >/dev/null  2>&1
	if [ ! "$?" == "0" ]; then
		return  1
	fi
	if [ -d "$CFG_DATA_DIR" ];then
		if [ ! -d "$tmp_file_name$TMP_SAVE_PATH$CFG_DATA_DIR" ]&&[ -d "$tmp_file_name$TMP_SAVE_PATH$CFG_DATA_OLD_DIR"  ];then
			mkdir -p $tmp_file_name$TMP_SAVE_PATH"/tmp_non_uci_cfg/"
			cp -rf  $tmp_file_name$TMP_SAVE_PATH$CFG_DATA_OLD_DIR"." $tmp_file_name$TMP_SAVE_PATH"/tmp_non_uci_cfg/"
			rm -f $tmp_file_name$TMP_SAVE_PATH$CFG_DATA_OLD_DIR"*"
			mkdir -p $tmp_file_name$TMP_SAVE_PATH$CFG_DATA_DIR
			cp -rf  $tmp_file_name$TMP_SAVE_PATH"/tmp_non_uci_cfg/." $tmp_file_name$TMP_SAVE_PATH$CFG_DATA_DIR
			rm -rf  $tmp_file_name$TMP_SAVE_PATH"/tmp_non_uci_cfg/"
		fi
	fi

	#decompress /etc/config
	tar -zxvf  $tmp_file_name$UC_CONF_TMP_FILE  -C  $tmp_file_name$TMP_SAVE_PATH  >/dev/null  2>&1
	if [ ! "$?" == "0" ]; then
		return  1
	fi
	cp -f $BACK_TMP_DIRPATH"/"$UC_DOT_CONF_FILE_NAME  $tmp_file_name >/dev/null  2>&1
	#先把导入配置解压到临时目录，然后根据黑名单处理，把相关配置重运行时或者flash中覆盖到解压的临时目录
	#暂时无非uci的黑名单
	mutex_running_cfg_lock_take "import_cfg_handle" "$$" "$$"  "1" "$2"

	local_all_uci_cp_to_tmp $SAVE_UCI_TMP_DIR

	#要在黑名单处理前处理，黑名单优先级最高最后覆盖；另，过先处理黑名单，导如配置的解压出来的bind_status就变成运行时了，非导入配置的了
	import_cfg_handle_cp_runing_blacklist_step
	ret="$?"
	mutex_running_cfg_lock_give "import_cfg_handle" "$$" "$$"  "1"
	
	if [ ! "$ret" == "0" ]; then
		return  1
	fi

	cat $tmp_file_name$SAVE_UCI_TMP_DIR"cloud_config">$DEBUG_SWITCH
	return 0
}
#导入配置打上特殊标记，用户导入配置首次启动的特殊处理
flag_import_cfg()
{
	local ops="cloud_sync.config_id.is_first_reboot_after_import"
	local pacs_tmp=`echo $ops | cut -d "." -f 1`
	local opt_str=`add_optstr_pac_suffix_handle $ops "change_suffix"`
	local pacs_str=`add_pac_suffix_handle $pacs_tmp "change_suffix"`
	#若旧uc目录无带后缀pac，需要拷贝一份否则无法操作
	if [ ! -f "$CFG_DIR$pacs_str" ]; then
		cp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_tmp $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str  >/dev/null  2>&1
	fi
	local cmd="uci set $opt_str='1' -c $tmp_file_name$SAVE_UCI_TMP_DIR"
	$(eval ${cmd}) >/dev/null  2>&1
	echo $cmd>$DEBUG_SWITCH
	uci commit $pacs_str -c  $tmp_file_name$SAVE_UCI_TMP_DIR
	#若待导入目录无待后缀pac，也需要拷贝一份
	if [ ! -f ${tmp_file_name}${TMP_SAVE_PATH}${TMP_UC_CONFIG_PATH}"/"${pacs_str} ]; then
		mv $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str   >/dev/null  2>&1
	fi
	#把旧uc目录相关的optionsset设置到待导入目录待后缀文件
	cp_uci_op_src_to_dst_delete_when_none $opt_str  $tmp_file_name$SAVE_UCI_TMP_DIR $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"
	#把带后缀即处理过的pac覆盖原pac
	mv  $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_str $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"$pacs_tmp   >/dev/null  2>&1
	rm -rf $tmp_file_name$SAVE_UCI_TMP_DIR$pacs_str  >/dev/null  2>&1

}

import_cfg_handle()
{
	echo "import_cfg_handle==$1 $2 $3">$DEBUG_SWITCH
	local reboot_after_success="$2"
	echo "import_cfg_handle  "$1>$DEBUG_SWITCH
	
	local tmp_file_name=$TMP_PATH_UPPEST"$$"
	mkdir -p $tmp_file_name
	
	import_cfg_handle_cp "$1" "$3"
	if [ ! "$?" == "0" ]; then 
		echo "cp import cfg  fail">/dev/console
		rm -rf $tmp_file_name  >/dev/null  2>&1
		rm -f $1  >/dev/null  2>&1
		exit 1
	fi

	#检查通过后，需要把涉及flash操作停止，否则会有配置被覆盖的可能性。
	forbid_to_save_cfg
	mutex_flash_protect_lock_take "import_cfg_handle" "$$" "$$"  "1"
	#若导入后需要立马重启的，拿到flash锁后，不允许配置保存
	if [ -n "$reboot_after_success" ]&&[ "reboot" == "$reboot_after_success" ];then
		forbid_to_save
	fi
	#处理saved_blacklist即，导入需要保持为flash中配置，不能被导入配置覆盖的
	#目前非uci无此类配置
	import_cfg_handle_cp_saved_blacklist_step
	#导入配置在system.sys.is_import_first_reboot=1打上标记，用户启动后首次特殊处理导入配置
	flag_import_cfg

	set_save_begin
	#cp backup to flash,need to save the import uc.conf
	write_uci_backup_to_flash $tmp_file_name$TMP_SAVE_PATH$TMP_UC_CONFIG_PATH"/"  ""  $tmp_file_name$BACK_TMP_DIRPATH"/"
	if [ ! "$?" == "0" ]; then
		echo "save_section write_uci_backup_to_flash failed">/dev/console
		set_save_done
		allow_to_save_cfg
		mutex_flash_protect_lock_give "import_cfg_handle" "$$" "$$"  "1"
		rm -rf  $tmp_file_name  >/dev/null  2>&1
		return 1
	fi
	local cmd="rm -rf $CFG_DATA_DIR*"
	$(eval ${cmd}) >/dev/null  2>&1

	echo "cp =="$tmp_file_name$TMP_SAVE_PATH$CFG_DATA_DIR"." $CFG_DATA_DIR>$DEBUG_SWITCH
	cp_result=`cp -rf  $tmp_file_name$TMP_SAVE_PATH$CFG_DATA_DIR"." $CFG_DATA_DIR  >/dev/null  2>&1`
	
	
	set_save_done

	mutex_flash_protect_lock_give "import_cfg_handle" "$$" "$$"  "1"
	#clean tmp file
	rm -rf  $tmp_file_name  >/dev/null  2>&1
	
	touch $IMPORT_PROGRESS
	rm -rf $1
	if [ -n "$reboot_after_success" ]&&[ "reboot" == "$reboot_after_success" ];then
		sleep 1; reboot; sleep 5; reboot -f  >/dev/null  2>&1;
	else
		allow_to_save_cfg
	fi
}

# reutrn 0:OK
#2:hwid null
#3:cfg_ver fail
#4:devcie_model ver  fail
#5：hwver fail
backup_cfg_handle()
{
	echo "backup_cfg_handle   "$1>$DEBUG_SWITCH
	mkdir -p $BACK_RES_TMP_DIRPATH
	echo "backup_cfg_handle  "$MD5_CONF  $PRODUCT_CONF>$DEBUG_SWITCH
	
	#product info
	local hw_id=`ubus call tddpServer getInfo '{"infoMask":1023}' | grep hw_id | awk '{print $2}' | head -n 1 | sed -e 's/\"//g'| sed -e 's/,//g'`
	if [ ! -n "$hw_id" ];then
		echo "hw_id null for backup file"> /dev/console
		exit 2
	fi
	
	local cfg_ver=`uci get cfg_info.settings.version`
	if [ ! -n "$cfg_ver" ];then
		echo "cfg ver null for backup file"> /dev/console
		exit 3
	fi
	local device_model=`uci get device_info.info.device_model`
	if [ ! -n "$device_model" ];then
		echo "device_model null for backup file"> /dev/console
		exit 4
	fi
	local hw_ver=`uci get device_info.info.hw_version`
	if [ ! -n "$hw_ver" ];then
		echo "hw_ver null for backup file"> /dev/console
		exit 5
	fi
	
	mkdir -p $BACK_TMP_DIRPATH
	mutex_flash_protect_lock_take "backup_cfg_handle" "$$" "$$"  "1"
	mkdir -p $UC_DOT_CONF_EXPORT_PATH
	rm -rf $UC_DOT_CONF_EXPORT_PATH$UC_DOT_CONF_FILE_NAME >/dev/null
	get_uci_from_flash $TMP_UC_CONFIG_PATH"/" "" $UC_DOT_CONF_EXPORT_PATH
	if [ ! "$?" == "0" ]; then
		echo "get_uci_from_flash file"> /dev/console
		mutex_flash_protect_lock_give "backup_cfg_handle" "$$" "$$"  "1"
		exit 1
	fi
	
	#compress non-uci
	tar -zvcf $NON_UCI_TMP_FILE $CFG_DATA_DIR	 >/dev/null  2>&1
	#compress uci
	tar -zvcf $UC_CONF_TMP_FILE $TMP_UC_CONFIG_PATH   >/dev/null  2>&1
	
	mutex_flash_protect_lock_give "backup_cfg_handle" "$$" "$$"  "1"
	#把uc.conf文件也备份
	cp -f $UC_DOT_CONF_EXPORT_PATH$UC_DOT_CONF_FILE_NAME $BACK_TMP_DIRPATH  >/dev/null  2>&1
	tar -zvcf $TMP_FILE $BACK_TMP_DIRPATH   >/dev/null  2>&1
	
	#md5 calc
	local mem_md5_result=`md5sum $TMP_FILE | awk '{print $1}' | head -n 1`
	echo $mem_md5_result>$MD5_CONF
	cat $MD5_CONF>$DEBUG_SWITCH
	
	#produc info input
	echo $hw_id" "$cfg_ver" "$device_model" "$hw_ver>$PRODUCT_CONF
	cat $PRODUCT_CONF>$DEBUG_SWITCH
	
	
	tar -zvcf $1 $BACK_RES_TMP_DIRPATH  >/dev/null  2>&1
}
