#!/bin/sh

[ "$DEVICE" == "lo" ] && exit 0

get_inface_name()
{
	config_get interface $1 if
	append interfaces $interface
}

check_inface_reset_static_route()
{
	local interfaces

	config_load network

	config_foreach get_inface_name user_route
	config_foreach get_inface_name user_route_ipv6

	[ -n "$interfaces" ] || return

	for inface in $interfaces;do
		if [ "$inface" == "$USERIF" ] || [ "${inface}6" == "$USERIF" ];then
			/etc/init.d/static_route restart
			return
		fi
	done
}

. /lib/zone/zone_api.sh

#iface转化为userif，转换失败使用原来的
USERIF=`zone_iface_to_userif $INTERFACE`
if [ "$USERIF" == "" ]; then
	USERIF=$INTERFACE
fi

case "$ACTION" in
		ifup)
			if [ -f /tmp/static_route.ready ];then
				[ -n "$USERIF" ] && check_inface_reset_static_route

				if [ $USERIF == "LAN" ]; then
					/etc/init.d/static_route restart
				fi
			fi
		;;
		ifdown)
			if [ -f /tmp/static_route.ready ];then
				[ -n "$USERIF" ] && check_inface_reset_static_route
			fi
		;;
		ifupdate)
			if [ -f /tmp/static_route.ready ] && [ "$IFUPDATE_ADDRESSES" == "1" ];then
				[ -n "$USERIF" ] && check_inface_reset_static_route

				if [ $USERIF == "LAN" ]; then
					/etc/init.d/static_route restart
				fi
			fi

		;;
esac



