--[[
Copyright(c) 2008-2014 Shenzhen TP-LINK Technologies Co.Ltd.

File    :  fw_policy_handler.lua
Details :  handler for policy function
Author  :  chenxing_w8217
Version :  1.0.0
Date    :  13 June, 2020
]]--
local uci = require "luci.model.uci"
local sys = require "luci.sys"
local dbg = require "luci.tools.debug"
local err = require("luci.torchlight.error")

-- ipt executor
IPT_EXECUTOR = "/lib/fw_policy_manager/ipt_rule_executor.sh"
POLICY_TARGET = "policytarget"

--[[
function:
	用于防火墙策略规则的添加
param：
	arg[1]: init
--]]--
local operation = arg[1]

if nil == operation or "" == operation then
	dbg("params error!")
	return false
end

if operation == "init" then
	--init the iptables rule
	cmd = string.format([[%s add_rule mangle INPUT "" %s 2]], IPT_EXECUTOR, POLICY_TARGET)
	sys.fork_call(cmd)

	cmd = string.format([[%s add_rule mangle OUTPUT "" %s 2]], IPT_EXECUTOR, POLICY_TARGET)
	sys.fork_call(cmd)

	cmd = string.format([[%s add_rule mangle FORWARD "" %s 1]], IPT_EXECUTOR, POLICY_TARGET)
	sys.fork_call(cmd)

	return true
end

return ret
