#!/bin/sh
#/etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file	  ipsec_check_domain.sh
# brief	  called by /usr/sbin/tmngtd periodically, and check if domain lists in ipsec_check_dns renew.
#			Cp /etc/config/ipsec_check_dns -> /tmp/ipsec_check_dns_tmp, to change domain to ip in a tmp file.
# author   <PERSON>
# version  1.0.0
# date	   08June15
# histry   arg 1.0.0, 14July15, <PERSON>, Create the file.


. /lib/functions.sh
. /lib/functions/network.sh
. /lib/zone/zone_api.sh

include /lib/network

UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE=/tmp/ipsec_check_dns_tmp
UCI_IPSEC_DOMAIN_MAIN_FILE=ipsec_check_dns
UCI_DEFAULT_SEARCH_DIR=/etc/config
IPSEC_WEB_LOCK=/tmp/lock/ipsec_web
IPSEC_DOMAIN=/tmp/ipsec_domain_checklist
IPSEC_DOMAIN_TO_BE_CHECK_SECTION=domain
IPSEC_DNSQ_COUNT=/tmp/lock/ipsec_dnsq_count

local debug="off"
local re_transfer="false"

local main_file
local backup

MY_ECHO(){
	if [ ${debug} = "on" ]
	then
		date=$(date)
		echo  "$date ${1}" >> /var/log/ipsec_check_domain.log
	fi
}


#本脚本主要是解析与域名有关的三个地方：VPN config ipsec_connection的bindif和remote_peer
#在做任何修改之后，都会调用ipsec stroke down connection[*]来断开所有的已建立的连接。
#本脚本起作用的前提是：/etc/config目录中存在一个ipsec_check_dns文件，该uci文件的格式如下：
#config domain
#	option domain_name			'www.qq.com'
#	1: DOMAIN_NORMAL,2: DOMAIN_SPECIAL
#	option domain_type			'1'
#	vpn 对应/etc/config/vpn
#	option binding_file			'vpn'
#	option binding_section_type	'ipsec_connection'
#	option binding_item			'test1'
#	bindif 或者remote_peer
#	option binding_field		'remote_peer'
#		Initialize with xx.xx.xx.xx. As the domain name must be DNSed once at least.
#		Then we only DNS the domains in the domain_checklist generated by tunnel monitor
#	option old_ipaddr			'*******'
#	the interface will be used
#	option tunnel_local_binding 'GE1'

check_domain(){

	config_get domain_name			${1}	domain_name
	config_get domain_type			${1}	domain_type
	config_get binding_file			${1}	binding_file
	config_get binding_section_type	${1}	binding_section_type
	config_get binding_item			${1}	binding_item
	config_get binding_field		${1}	binding_field
	config_get old_ipaddr			${1}	old_ipaddr
	config_get tunnel_local_binding	${1}	tunnel_local_binding


	local ipaddr=""
	if [ ${domain_type} -eq 1 ]
	then
		#MY_ECHO "${domain_name} is a normal domain , let 'dnsq' program do it. "
		if [  ${old_ipaddr} = "xx.xx.xx.xx" ]
		then
			ipaddr=$(dnsq ${tunnel_local_binding} ${domain_name} )
		elif [ -n "${domain_checklist}" ]
		then
			if [ "${domain_checklist}" == "no_domain_need_check" ]
			then
				#echo "no domain need check" > /dev/console
				return
			fi
			local check_domain_name="{${domain_name}}"
			local exist=`echo ${domain_checklist} | grep ${check_domain_name}`

			if [ -n "${exist}" ]
			then
				#echo "check ${domain_name}, because the tunnel is not OK" > /dev/console
				ipaddr=$(dnsq ${tunnel_local_binding} ${domain_name} )
			else
				#echo "the tunnel is ok, don't need to check it!" > /dev/console
				return
			fi
		fi
	elif [ ${domain_type} -eq 2 ]
	then
		local effect_iface=$(zone_userif_to_effective_iface ${domain_name})
		network_get_ipaddr ipaddr ${effect_iface}
		#echo "domain_name:${domain_name} -> iface:$effect_iface -> ip:$ipaddr ,${old_ipaddr}" > /dev/console
	fi

	test "${ipaddr}"
	if [ $? -eq 1 ]
	then
		#MY_ECHO "${domain_name} resolved ip fail,let ${1} pass."
		ipaddr="xx.xx.xx.xx"
	fi

	if [ ${ipaddr} = ${old_ipaddr} -o ${ipaddr} = "0.0.0.0" ]
	then
		#echo "the ip address with ${domain_name} has no changed ,let ${1} go." > /dev/console
		return
	fi

	#echo "now find the IP of ${domain_name} changde, will down the connection and reload" > /dev/console
	#注意：config_set不生效，原因未知.2015.7.15

	#普通接口，一旦检测出地址有变立刻修改old_addr,并重新转换

	MY_ECHO "OK, let's record the new value in the file ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}"
	uci_set ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE} ${1} old_ipaddr ${ipaddr}
	#MY_ECHO "re_transfer=${re_transfer}"
	re_transfer="true"
	#uci_commit ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}

	#uci -c ${UCI_DEFAULT_SEARCH_DIR}
	#config_load ${binding_file}
	MY_ECHO "the binding information of ${domain_name} is as follows:"
	MY_ECHO "	binding_file=${binding_file}"
	MY_ECHO "	binding_section_type=${binding_section_type} "
	MY_ECHO "	binding_item=${binding_item} "
	MY_ECHO "	binding_field=${binding_field} "
	MY_ECHO "	tunnel_local_binding=${tunnel_local_binding} "
	MY_ECHO "	old value=${old_ipaddr} "
	MY_ECHO "	new value=${ipaddr} "

	#stroke down
	ipsec stroke down-nb ${binding_item}
	ipsec stroke down-nb ${binding_item}[*]
	ipsec stroke down-nb ${binding_item}{*}

	return
}



	test ${1}
	if [ $? -eq 0 ]
	then
		debug=${1}
	fi


{
	flock -x -n 30
	[ $? -eq 1 ] && { MY_ECHO "ipsec_dnsq.sh get lock fail."; exit 1;}


	if [ -e ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE} ]
	then
		#MY_ECHO "${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE} exist"
		MY_ECHO ""
	else
		#MY_ECHO "${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE} doesn't exist"
		touch ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}
	fi

	#MY_ECHO "ipsec_check_domain get lock successfully."
	main_file=$(cat ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_MAIN_FILE})
	backup=$(cat ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE})

	if [ "${main_file}" != "${backup}" ]
	then
		#MY_ECHO "main_file and backup is not equal!"
		rm -rf ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}
		cp ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_MAIN_FILE}  ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}
	#else
		#MY_ECHO "main_file and backup is equal!"

	fi

	flock -u 30
	#MY_ECHO "ipsec_check_domain release lock successfully."
} 30>>/var/lock/ipsec_dnsq


#获取域名的检查列表
# 调用lua重写的接口/域名解析检查脚本
lua /usr/lib/lua/ipsec/ipsec_check_domain.lua

if [ $? -eq 1 ]
#重新生成/etc/ipsec.conf 和/etc/ipsec.secrets.
then
	MY_ECHO "have committed the change , re-transfer the uci config "
	{
		flock -x -n 30
		[ $? -eq 1 ] && { MY_ECHO "ipsec_dnsq.sh get lock fail."; exit 1;}

		#MY_ECHO "ipsec_check_domain get lock successfully."
		#backup =$(cat ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_MAIN_FILE})
		backup=$(cat ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_MAIN_FILE})
		ipsec_web_lock=$(cat ${IPSEC_WEB_LOCK})
		#此处main_file已经存储有第一次读入的文件内容，相当于UCI_IPSEC_DOMAIN_MAIN_FILE的原始内容
		#再次读取UCI_IPSEC_DOMAIN_MAIN_FILE文件，确认在定时器查询期间没有进程改变了UCI_IPSEC_DOMAIN_MAIN_FILE的内容
		if [ "${main_file}" != "${backup}" -o "${ipsec_web_lock}" = "lock" ]
		then
			#主文件有改变或者有web操作在锁定lock,为了不冲突，此时不需要做任何事情，放弃本次查询结果.
			MY_ECHO "${UCI_IPSEC_DOMAIN_MAIN_FILE} has changed,quit!"
		else
			#MY_ECHO "${UCI_IPSEC_DOMAIN_MAIN_FILE} has not changed."
			rm -rf ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_MAIN_FILE}
			cp ${UCI_IPSEC_DOMAIN_TO_BE_CHECKED_FILE}	 ${UCI_DEFAULT_SEARCH_DIR}/${UCI_IPSEC_DOMAIN_MAIN_FILE}
			lua	 /usr/lib/lua/ipsec/ipsec_reload.lua && swanctl --load-all
			MY_ECHO "Going to update strongswan!"
		fi
		flock -u 30
		#MY_ECHO "ipsec_check_domain release lock successfully."
	} 30>>/var/lock/ipsec_dnsq
fi

