#!/bin/sh

[ ! -f "/etc/keepalived/keepalived.conf" ] && exit
[ "$FIRST_EVENT" == "1" ] && exit

. /lib/functions.sh
. /lib/zone/zone_api.sh
. /lib/functions/network.sh
. /lib/zone/zone_conf.sh

local flag=0

rule_get() {
	if [ $flag == 0 ];then
		config_get enable $1 enable
		if [ $enable == "on" ];then
			config_get interface $1 interface
			local iface=$(zone_get_effect_ifaces ${interface})
			local service_interface=$2
			if [ "$iface" == "$service_interface" ]; then
				flag=1
			fi
		fi
	fi
}

[ "$ACTION" == "ifup" ] && {
	config_load keepalived
	config_foreach rule_get rule $INTERFACE
	echo "keepalived:interface=$INTERFACE flag=$flag" > /dev/console
	if [ $flag == 1 ];then
		echo "restart keepalived" > /dev/console
		/etc/init.d/keepalived restart
	fi
}