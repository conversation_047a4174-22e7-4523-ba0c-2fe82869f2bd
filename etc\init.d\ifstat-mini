#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/ifstat-mini.pid

START=95

start()
{
    status=$(uci get system.if_status_global.status)

    if [ -z "$status" ]; then
        echo "/etc/config/system not found."
        return 0
    fi

    interval=$(uci get system.if_status_global.interval)

    if [ "${status}" == "1" ];then
        service_start /usr/sbin/ifstat-mini start ${interval}
    fi
}

stop()
{
	service_stop /usr/sbin/ifstat-mini
	if [ -f ${SERVICE_PID_FILE} ];then
		read PID < ${SERVICE_PID_FILE} && kill ${PID}  && rm -f ${SERVICE_PID_FILE}
	fi

	/usr/sbin/ifstat-mini clean
}

restart()
{
	stop
	start
}

reload()
{
	restart   
}

