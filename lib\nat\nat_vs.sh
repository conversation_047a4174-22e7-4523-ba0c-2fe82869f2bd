#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat_vs.sh
# brief
# author   <PERSON><PERSON>
# version  1.0.0
# date     23Apr15
# histry   arg 1.0.0, 23Apr15, <PERSON><PERSON>, Create the file.

. /lib/zone/zone_api.sh
. /lib/firewall/fw.sh
. /lib/functions/network.sh

prerouting_chain="prerouting_rule"
postrouting_chain="postrouting_rule"
vs_postrouting_chain="postrouting_rule_vs"
vs_prerouting_chain="prerouting_rule_vs"
nat_vs_log="/tmp/.nat/nat_vs.log"
NAT_ZONE_LAN="LAN"
IPT="iptables -w -t nat "
vs_ipset_name_file="/tmp/vs_ipset.name"

vs_nat_chain_add()
{
	local prerouting_chain_main=`iptables -w -t nat -nvL | grep -w ${prerouting_chain}`
	local postrouting_chain_vs=`iptables -w -t nat -nvL | grep -w ${vs_postrouting_chain}`
	local prerouting_chain_vs=`iptables -w -t nat -nvL | grep -w ${vs_prerouting_chain}`

	if [ -z "$prerouting_chain_main" ];then
		$IPT -N ${prerouting_chain}
		$IPT -I PREROUTING -j ${prerouting_chain}
	fi

	if [ -z "$postrouting_chain_vs" ];then
		$IPT -N ${vs_postrouting_chain}
		$IPT -A ${postrouting_chain} -j ${vs_postrouting_chain}
	fi

	if [ -z "$prerouting_chain_vs" ];then
		$IPT -N ${vs_prerouting_chain}
		$IPT -I ${prerouting_chain} -j ${vs_prerouting_chain}
	fi
}

vs_nat_chain_del()
{
	local postrouting_chain_vs_rule_num=`iptables -t nat -S ${vs_postrouting_chain} | wc -l`
	local prerouting_chain_vs_rule_num=`iptables -t nat -S ${vs_prerouting_chain} | wc -l`

	if [ -z "$postrouting_chain_vs_rule_num" -o $postrouting_chain_vs_rule_num -eq 1 ];then
		$IPT -D  ${postrouting_chain} -j ${vs_postrouting_chain}
		$IPT -X ${vs_postrouting_chain}
	fi

	if [ -z "$prerouting_chain_vs_rule_num" -o $prerouting_chain_vs_rule_num -eq 1 ];then
		$IPT -D  ${prerouting_chain} -j ${vs_prerouting_chain}
		$IPT -X ${vs_prerouting_chain}
	fi

	local prerouting_chain_rule_num=`iptables -t nat -S ${prerouting_chain} | wc -l`
	if [ -z "$prerouting_chain_rule_num" -o $prerouting_chain_rule_num -eq 1 ];then
		$IPT -D  PREROUTING -j ${prerouting_chain}
		$IPT -X ${prerouting_chain}
	fi
}

ebtables_chain_add()
{
	if [ -d /sys/module/ecm ]; then
		local ebcount=`ebtables -t broute -L | grep mark_vs|wc -l`
		if [ $ebcount -eq 0 ]; then
			ebtables -t broute -N mark_vs
			ebtables -t broute -A BROUTING -j mark_vs
		fi
	fi
}

ebtables_chain_del()
{
	if [ -d /sys/module/ecm ]; then
		local ebcount=`ebtables -t broute -L mark_vs |wc -l`
		if [ $ebcount -eq 3 ]; then
			ebtables -t broute -D BROUTING -j mark_vs
			ebtables -t broute -X mark_vs
		fi
	fi
}

get_vs_rule_ipaddr_by_name()
{
	local rule_vs_name=$1
	local proto=$2
	local vs_rule_ipaddr=`$IPT -n --line-number -L ${vs_prerouting_chain} | grep -w "\/\* ${rule_vs_name} \*\/" | awk '$3~/'"${proto}"'/ {print $6}'`
	echo $vs_rule_ipaddr
}

add_mul_lan_snat()
{
	local ipset_name=$1
	#multi lan
	local lan_ifaces=$(zone_get_normal_ifaces)
	append lan_ifaces "lan"
	local lan_iface
	local lan_ipaddr
	local lan_netmask
	local prefix
	local subnet
	for lan_iface in $lan_ifaces
	do
		lan_ipaddr=$(uci get network.$lan_iface.ipaddr)
		lan_netmask=$(uci get network.$lan_iface.netmask)
		if [ "$lan_ipaddr" != "" -a "$lan_netmask" != "" ]; then
			prefix=$(ipcalc -p $lan_ipaddr $lan_netmask)
			prefix=${prefix#*=}
			subnet=$(ipcalc -n $lan_ipaddr $lan_netmask)
			subnet=${subnet#*=}
			ipset add ${ipset_name} ${subnet}/${prefix} -exist
		fi
	done
}

add_loopback_snat()
{
	local ipset_name=$1
	local loopback_ipaddr_slots=$2
	if [ "${loopback_ipaddr_slots}" == "---" ]; then
		return
	fi
	local array=${loopback_ipaddr_slots//,/ }
	local var
	for var in ${array}
	do
		ipset add ${ipset_name} ${var} -exist
	done
}

vs_rule_ipset_name_malloc()
{
	local name=$1
	if [ ! -f  "${vs_ipset_name_file}" ]; then
		touch ${vs_ipset_name_file}
	fi
	local ipset_name=$(vs_rule_ipset_name_get ${name})
	if [ ${#ipset_name} -eq 0 ]; then
		local item=""
		local line_num=`wc -l ${vs_ipset_name_file} | awk '{print $1}'`
		local old=`awk '{if(NF==1) {print $1}}' ${vs_ipset_name_file} | awk 'NR==1 {printf $1}'`
		if [ ${#old} -ne 0 ]; then
			item="${old} ${name}"
			sed "s/^${old}[ ]/${item}/" -i ${vs_ipset_name_file}
			ipset_name=$old
		else
			ipset_name="__virtual_server_${line_num}"
			item="${ipset_name} ${name}"
			echo "$item" >> ${vs_ipset_name_file} 2>/dev/null
		fi
		if [ -n "${ipset_name}" ]; then
			# create ipset
			ipset create ${ipset_name} hash:net -exist
			ipset flush ${ipset_name}
		fi
	fi
	echo ${ipset_name}
}

vs_rule_ipset_name_get()
{
	local ipset_name=""
	[ ! -f  "${vs_ipset_name_file}" ] && return ${ipset_name}

	local name="$1"
	ipset_name=`grep -w "${name}$" ${vs_ipset_name_file} | awk '{print $1}'`
	echo ${ipset_name}
}

vs_rule_ipset_name_free()
{
	[ ! -f  "${vs_ipset_name_file}" ] && return

	local name="$1"
	[ ${#name} -eq 0 ] && return

	# flush ipset
	local ipset_name=$(vs_rule_ipset_name_get ${name})
	[ -n "${ipset_name}" ] && ipset destroy ${ipset_name}

	local count=`grep -c -w "${name}$" ${vs_ipset_name_file}`
	[ $count -gt 0 ] && sed "s/\<${name}\>$//g" -i ${vs_ipset_name_file}
}

add_ebtables_rule()
{
	local if_ip=$1
	local proto=$2
	local ipaddr=$3
	local external_port=$4
	local internal_port=$5

	local export=${external_port//-/:}
	local inport=${internal_port//-/:}
	if [ -d "/sys/module/ecm" ]; then
		ebtables -t broute -A mark_vs -p ipv4 --ip-dst $if_ip --ip-proto $proto --dmport $export -j mark --mark-or 0x80000000
		ebtables -t broute -A mark_vs -p ipv4 --ip-src $ipaddr --ip-proto $proto --smport $inport -j mark --mark-or 0x80000000
	fi
}

del_ebtables_rule()
{
	local if_ip=$1
	local protocol=$2
	local ipaddr=$3
	local external_port=$4
	local internal_port=$5

	local export=${external_port//-/:}
	local inport=${internal_port//-/:}
	if [ -d "/sys/module/ecm" ]; then
		local ex_num=`ebtables -t broute -L mark_vs --Ln | grep -w "${protocol}" | grep -w "dmport" | grep -w "${export}" | cut -d "." -f 1`
		if [ -n "$ex_num" ];then
			ebtables -t broute -D mark_vs ${ex_num}
		fi
		local in_num=`ebtables -t broute -L mark_vs --Ln | grep -w "${protocol}" | grep -w "smport" | grep -w "${inport}" | cut -d "." -f 1`
		if [ -n "$in_num" ];then
			ebtables -t broute -D mark_vs ${in_num}
		fi
	fi
}

add_vs_rule()
{
	local name=$1
	local if_ip=$2
	local dest_ip=$3
	local src_dport_start=$4
	local src_dport_end=$5
	local dest_port_start=$6
	local dest_port_end=$7
	local proto=$8
	local enable=$9
	local src_dport=${10}
	local dest_port=${11}
	local loopback_ipaddr=${12}

	if [ "$enable" == "on" ]; then
		#create chain
		vs_nat_chain_add
		ebtables_chain_add

		#format port
		local ex_port=""
		local in_port=""
		local internal_port=""
		local external_port=""

		external_port=${src_dport}
		ex_port=${src_dport//-/:}
		local ex_port_left=${ex_port%:*}
		local ex_port_right=${ex_port#*:}
		if [ ${ex_port_left} -eq ${ex_port_right} ]; then
			ex_port=${ex_port_left}
			external_port=${ex_port_left}
		fi
		internal_port=${dest_port}
		in_port=${dest_port//-/:}
		local in_port_left=${in_port%:*}
		local in_port_right=${in_port#*:}
		if [ ${in_port_left} -eq ${in_port_right} ]; then
			in_port=${in_port_left}
			internal_port=${in_port_left}
		fi

		#ipset create
		local ipset_ext=`$IPT -n --line-number -L ${vs_prerouting_chain} | grep -w "\/\* ${name} \*\/" | wc -l`
		local ipset_name=$(vs_rule_ipset_name_malloc ${name})
		if [ "${ipset_ext}" -eq "0" ]; then
			#multi lan add to ipset
			#add_mul_lan_snat ${ipset_name}
			#loopback add to ipset
			add_loopback_snat ${ipset_name} ${loopback_ipaddr}
		fi

		#wan dnat
		$IPT -A ${vs_prerouting_chain} -d ${if_ip} -p ${proto} -m multiport --dports ${ex_port} -m comment --comment ${name} -j XDNAT --to-destination ${dest_ip}:${internal_port}:${external_port}

		#lan snat
		$IPT -A ${vs_postrouting_chain} -m set --match-set ${ipset_name} src -d ${dest_ip} -p ${proto} -m multiport --dports ${in_port} -m comment --comment ${name} -j SNAT --to-source ${if_ip}

		#ipq ecm
		add_ebtables_rule ${if_ip} ${proto} ${dest_ip} ${external_port} ${internal_port}
	fi
}

del_vs_rule()
{
	local name=$1
	local if_ip=$2
	local dest_ip=$3
	local src_dport_start=$4
	local src_dport_end=$5
	local dest_port_start=$6
	local dest_port_end=$7
	local proto=$8
	local enable=$9
	local src_dport=${10}
	local dest_port=${11}
	local loopback_ipaddr=${12}

	local external_port="${src_dport}"
	local ex_port=${external_port//-/:}
	local ex_port_left=${ex_port%:*}
	local ex_port_right=${ex_port#*:}
	if [ ${ex_port_left} -eq ${ex_port_right} ]; then
		external_port=${ex_port_left}
	fi
	local internal_port="${dest_port}"
	local in_port=${internal_port//-/:}
	local in_port_left=${in_port%:*}
	local in_port_right=${in_port#*:}
	if [ ${in_port_left} -eq ${in_port_right} ]; then
		internal_port=${in_port_left}
	fi

	#del ebtables rule
	del_ebtables_rule ${if_ip} ${proto} ${dest_ip} ${external_port} ${internal_port}

	#del enbtables chain
	ebtables_chain_del

	#del dnat rule
	local vs_rule_pre_num=`$IPT -n --line-number -L ${vs_prerouting_chain} | grep -w "\/\* ${name} \*\/"  | awk '$3~/'"${proto}"'/' | cut -d " " -f 1`
	if [ -n "$vs_rule_pre_num" ];then
		$IPT -D ${vs_prerouting_chain} $vs_rule_pre_num
	fi

	#del snat rule
	local vs_rule_post_num=`$IPT -n --line-number -L ${vs_postrouting_chain} | grep -w "\/\* ${name} \*\/"  | awk '$3~/'"${proto}"'/' | cut -d " " -f 1`
	if [ -n "$vs_rule_post_num" ];then
		$IPT -D ${vs_postrouting_chain} $vs_rule_post_num
	fi

	#destroy ipset
	local ipset_ref=`$IPT -n --line-number -L ${vs_prerouting_chain} | grep -w "\/\* ${name} \*\/" | wc -l`
	if [ "${ipset_ref}" -eq "0" ]; then
		vs_rule_ipset_name_free ${name}
	fi

	#del vs chain
	vs_nat_chain_del
}

nat_do_vs_rule()
{
	local ops=$1
	local name=$2
	local if_ip=$3
	local dest_ip=$4
	local src_dport_start=$5
	local src_dport_end=$6
	local dest_port_start=$7
	local dest_port_end=$8
	local proto=$9
	local enable=${10}
	local src_dport=${11}
	local dest_port=${12}
	local loopback_ipaddr=${13}

	[ -z "${name}" ] && {
		echo "name is null" > /dev/console
		return
	}

	[ -z "${if_ip}" ] && {
		echo "if_ip is null" > /dev/console
		return
	}

	[ -z "${dest_ip}" ] && {
		echo "dest_ip is null" > /dev/console
		return
	}

	[ -z "${src_dport_start}" ] && {
		echo "src_dport_start is null" > /dev/console
		return
	}

	[ -z "${src_dport_end}" ] && {
		echo "src_dport_end is null" > /dev/console
		return
	}

	[ -z "${dest_port_start}" ] && {
		echo "dest_port_start is null" > /dev/console
		return
	}

	[ -z "${dest_port_end}" ] && {
		echo "dest_port_end is null" > /dev/console
		return
	}

	[ -z "${proto}" ] && {
		echo "proto is null" > /dev/console
		return
	}

	[ -z "${enable}" ] && {
		echo "enable is null" > /dev/console
		return
	}

	[ -z "${src_dport}" ] && {
		echo "src_dport is null" > /dev/console
		return
	}

	[ -z "${dest_port}" ] && {
		echo "dest_port is null" > /dev/console
		return
	}

	[ -z "${loopback_ipaddr}" ] && {
		echo "loopback_ipaddr is null" > /dev/console
		return
	}

	case $ops in
		add)
			if [ "${if_ip}" != "---" ]; then
				add_vs_rule $name $if_ip $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end $proto $enable $src_dport $dest_port $loopback_ipaddr
			fi
		;;
		del)
			del_vs_rule $name $if_ip $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end $proto $enable $src_dport $dest_port $loopback_ipaddr
		;;
	esac
}

vs_rule_operation()
{
	flock -x 90

	local ops=$1
	local name=$2
	local if=$3
	local dest_ip=$4
	local src_dport_start=$5
	local src_dport_end=$6
	local dest_port_start=$7
	local dest_port_end=$8
	local proto=$9
	local enable=${10}
	local src_dport=${11}
	local dest_port=${12}
	local loopback_ipaddr=${13}

	local ifaces=$(zone_get_effect_ifaces "${if}")
	local iface
	local device
	local if_ip
	local protocol
	for iface in $ifaces
	do
		device=$(zone_get_effect_devices "${iface}")
		[ -n "$iface" -a -n "$device" ] && {
			network_get_ipaddr if_ip ${iface}
			if [ -z "$if_ip" ]; then
				if_ip="---"
			fi
			protocol=$(echo $proto|tr '[A-Z]' '[a-z]')
			case $protocol in
				tcp)
					nat_do_vs_rule $ops $name $if_ip $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end tcp $enable $src_dport $dest_port $loopback_ipaddr
				;;
				udp)
					nat_do_vs_rule $ops $name $if_ip $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end udp $enable $src_dport $dest_port $loopback_ipaddr
				;;
				*)
					nat_do_vs_rule $ops $name $if_ip $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end tcp $enable $src_dport $dest_port $loopback_ipaddr
					nat_do_vs_rule $ops $name $if_ip $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end udp $enable $src_dport $dest_port $loopback_ipaddr
				;;
			esac
		}
	done

	flock -u 90
} 90>/tmp/nat_vs.lock

nat_load_rule_vs()
{
	#init config_load
	config_get name $1 name
	config_get if $1 if
	config_get dest_ip $1 dest_ip
	config_get src_dport_start $1 src_dport_start
	config_get src_dport_end $1 src_dport_end
	config_get dest_port_start $1 dest_port_start
	config_get dest_port_end $1 dest_port_end
	config_get proto $1 proto
	config_get enable $1 enable
	config_get src_dport $1 src_dport
	config_get dest_port $1 dest_port
	config_get loopback_ipaddr $1 loopback_ipaddr
	if [ -z "$loopback_ipaddr" ]; then
		loopback_ipaddr="---"
	fi
	local ops=$2
	vs_rule_operation $ops $name $if $dest_ip $src_dport_start $src_dport_end $dest_port_start $dest_port_end $proto $enable $src_dport $dest_port $loopback_ipaddr
}

nat_rule_vs_operation()
{
	config_load firewall
	config_foreach nat_load_rule_vs redirect add
}

update_vs_rule()
{
	#hotplug config_load
	config_get if $1 if
	config_get enable $1 enable
	config_get proto $1 proto
	config_get name $1 name
	INTERFACE=$2

	[ -n "$proto" ] && {
		proto=$(echo $proto|tr '[A-Z]' '[a-z]')
	}

	if [ -n "${if}" -a "${enable}" == "on" -a "${if}" == "${INTERFACE}" ];then
		local ifaces=$(zone_get_effect_ifaces "${if}")
		local iface
		local device
		local if_ip
		local rule_vs_wan_ip
		for iface in $ifaces;do
			device=$(zone_get_effect_devices "${iface}")
			network_get_ipaddr if_ip ${iface}
			case $proto in
				all)
					rule_vs_wan_ip=$(get_vs_rule_ipaddr_by_name "${name}" tcp)
				;;
				*)
					rule_vs_wan_ip=$(get_vs_rule_ipaddr_by_name "${name}" "${rule_vs_protocol}")
				;;
			esac

			[ -n "$if_ip" -a "$if_ip" != "$rule_vs_wan_ip" ] && {
				nat_load_rule_vs $1 del
				nat_load_rule_vs $1 add
			}
		done
	fi
}

nat_rule_vs_hotplug()
{
	config_load firewall
	config_foreach update_vs_rule redirect $1
}