#!/bin/sh
#author qiufusheng
#brief dealing with interface up/down events

pppoeserver_pid_file=/var/run/pppoeserver.pid
#natpolicy="pppoeserver_nat_policy"

case ${ACTION} in
    ifup)
        . /lib/functions.sh
        . /lib/zone/zone_api.sh
        . /lib/functions/network.sh
        if [ -f ${pppoeserver_pid_file} ];then
            userif=`uci get pppoe_server.setting.service_if`
            iface=$(zone_userif_to_effective_iface ${userif})
            if [[ "$iface" == "$INTERFACE" ]]; then
                echo "pppoe server start" >> /dev/console
                /etc/init.d/pppoe-server restart
            fi
        fi
    ;;
    ifdown)
        # do nothing
    ;;
    ifupdate)
        # do nothing
    ;;
esac