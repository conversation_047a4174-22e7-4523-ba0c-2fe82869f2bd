#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"
local uci = require "luci.model.uci"

local db_apmngr = {
	name = "apmngr.db",
	tables = {
		{
			name = "ApEntryCfg_table",
			columns = {
				{
					name = "seq",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_unique"},
					default_value = nil
				}, {
					name = "group_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "ap_role",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "normal"
				}, {
					name = "cap_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = -1
				}, {
					name = "mac",
					stype = "VARCHAR ( 17 )",
					db_attr = {"db_unique"},
					default_value = nil
				}, {
					name = "entry_name",
					stype = "VARCHAR ( 52 )",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "model_id",
					stype = "INTEGER",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "hardware_ver",
					stype = "VARCHAR ( 20 )",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "ap_keep_alive",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "client_keep_alive",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "client_idle",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "offline_selfmanage",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "username",
					stype = "VARCHAR ( 32 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "password",
					stype = "VARCHAR ( 16 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_1",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_2",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_3",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_4",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "ap_trunk",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "hdap_peer_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "led",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "led_wifi_sync",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = "off"
				}, {
					name = "led_timer_enable",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = "off"
				}, {
					name = "led_close_date",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "eve"
				}, {
					name = "led_close_time",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "00:00:00"
				}, {
					name = "led_open_date",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "eve"
				}, {
					name = "led_open_time",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "00:00:00"
				}, {
					name = "cloud_config_id",
					stype = "VARCHAR ( 65 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "telnet_status",
					stype = "INTEGER",
					db_attr = {},
					default_value = -1
				}, {
                                        name = "fit_fat_button_status",
                                        stype = "INTEGER",
                                        db_attr = {},
                                        default_value = -1
                                }
			},
			datas = {}
		}, {
			name = "ApTemplate_table",
			columns = {
				{
					name = "seq",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_unique"},
					default_value = nil
				}, {
					name = "group_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "entry_name",
					stype = "VARCHAR ( 52 )",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "model_id",
					stype = "INTEGER",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "hardware_ver",
					stype = "VARCHAR ( 20 )",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "ap_keep_alive",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "client_keep_alive",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "client_idle",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "offline_selfmanage",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "username",
					stype = "VARCHAR ( 32 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "password",
					stype = "VARCHAR ( 16 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_1",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_2",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_3",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "phy_wire_vlan_4",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "ap_trunk",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "led",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "led_wifi_sync",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = "off"
				}, {
					name = "led_timer_enable",
					stype = "VARCHAR ( 4 )",
					db_attr = {},
					default_value = "off"
				}, {
					name = "led_close_date",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "eve"
				}, {
					name = "led_close_time",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "00:00:00"
				}, {
					name = "led_open_date",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "eve"
				}, {
					name = "led_open_time",
					stype = "VARCHAR ( 10 )",
					db_attr = {},
					default_value = "00:00:00"
				}, {
                                        name = "telnet_status",
                                        stype = "INTEGER",
                                        db_attr = {},
                                        default_value = -1
                                }, {
					name = "fit_fat_button_status",
					stype = "INTEGER",
					db_attr = {},
					default_value = -1
				}

			},
			datas = {}
		}, {
			name = "Group_table",
			columns = {
                {
                    name = "seq",
                    stype = "INTEGER",
                    db_attr = {"db_key", "db_auto_increment"},
                    default_value = nil
                }, {
                    name = "group_id",
                    stype = "INTEGER",
                    db_attr = {"db_unique"},
                    default_value = nil
                }, {
                    name = "name",
                    stype = "TEXT ( 52 )",
                    db_attr = {},
                    default_value = nil
                }
			},
			datas = {}
		}
	}
}

-- 保证apmng_set.aplog_setting这个section存在
function check_aplog_section()
	local uci_r = uci.cursor()
	local section_entry = uci_r:get_all("apmng_set", "aplog_setting")
	if section_entry == nil then
		uci_r:section("apmng_set", "global", "aplog_setting", {
			ac_log_switch = "on",
			ac_log_level = "4",
			remote_server_switch = "off",
			remote_log_level = "4",
			remote_server = "",
			remote_report_interval = ""
		})
		uci_r:commit("apmng_set")
	else
		local ac_log_switch = uci_r:get("apmng_set", "aplog_setting", "ac_log_switch")
		local ac_log_level = uci_r:get("apmng_set", "aplog_setting", "ac_log_level")
		local remote_server_switch = uci_r:get("apmng_set", "aplog_setting", "remote_server_switch")
		local remote_log_level = uci_r:get("apmng_set", "aplog_setting", "remote_log_level")
		-- dbg(ac_log_switch, ac_log_level, remote_server_switch, remote_log_level)
		if ac_log_switch == nil or ac_log_level == nil or remote_server_switch == nil or remote_log_level == nil then
			ac_log_switch = ac_log_switch or "on"
			ac_log_level = ac_log_level or "4"
			remote_server_switch = remote_server_switch or "off"
			remote_log_level = remote_log_level or "4"

			uci_r:set("apmng_set", "aplog_setting", "ac_log_switch", ac_log_switch)
			uci_r:set("apmng_set", "aplog_setting", "ac_log_level", ac_log_level)
			uci_r:set("apmng_set", "aplog_setting", "remote_server_switch", remote_server_switch)
			uci_r:set("apmng_set", "aplog_setting", "remote_log_level", remote_log_level)
			uci_r:commit("apmng_set")
		end
	end
end

check_aplog_section()
cfgsync.config_sync(db_apmngr, "database")
