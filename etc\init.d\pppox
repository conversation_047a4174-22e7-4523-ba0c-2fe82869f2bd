#!/bin/sh /etc/rc.common
#author: wangdechu

START=50
STOP=26

start()
{
	has_mppe=$(lsmod | awk '{if($1=="ppp_mppe")print $1}')
	[ -z "$has_mppe" ] && insmod ppp_mppe
	
#	mkdir -p /etc/hotplug.d/vpniface1
#	ln -s /etc/hotplug.d/vpniface/10-pppox-if-up-down.sh /etc/hotplug.d/vpniface1/10-pppox-if-up-down.sh
#	mkdir -p /etc/hotplug.d/vpnhook
#	ln -s /etc/hotplug.d/wanhook/25-pppox.sh /etc/hotplug.d/vpnhook/25-pppox.sh

    . /lib/pppox/pppox-pppoetimer.sh
    load_pppoe

	mkdir -p /tmp/pppox/global
	l2tp_tunnel_max=`uci get profile.@l2tp[0].tunnel_max 2>/dev/null`
	pptp_tunnel_max=`uci get profile.@pptp[0].tunnel_max 2>/dev/null`

	let tunnel_max=l2tp_tunnel_max+pptp_tunnel_max
	echo $tunnel_max >/tmp/pppox/global/tunnel_max

}

down_all_pppoe()
{
	config_get proto $1 proto
	[ "$proto" != "pppoe" ] && return 0

	ifdown $1
}


stop()
{
	echo "kill all l2tp/pptp process" >/dev/console
	local pppox_ppppath=/tmp/ppp
	for file in `ls ${pppox_ppppath}/pid 2>/dev/null`; do
		local getconfigpath=`uci -c ${pppox_ppppath}/pid get ${file}.@info[0].configpath 2>/dev/null`
		if [ -f "${getconfigpath}/config/l2tp.type" ]; then
			kill -TERM ${file}
		elif [ -f "${getconfigpath}/config/pptp.type" ]; then
			local getisserver=`uci -c ${pppox_ppppath}/pid get ${file}.@info[0].isserver 2>/dev/null`
		
			if [ "${getisserver}" = "1" ]; then
				kill -TERM ${file}
			else
				local path=${getconfigpath}/config
				local ppp_pid=${path}/ppp
				local mgr_pid=${path}/mgr
				local mgrfather_pid=${path}/mgrfather
	
				ppp_pid=$(cat $ppp_pid 2>/dev/null)
				mgr_pid=$(cat $mgr_pid 2>/dev/null)
				mgrfather_pid=$(cat $mgrfather_pid 2>/dev/null)
				[ ${ppp_pid}x != ''x ] && kill -TERM $ppp_pid
				#[ ${mgrfather_pid}x != ''x ] && kill -TERM $mgrfather_pid
				#[ ${mgr_pid}x != ''x ] && kill -TERM $mgr_pid
			fi
		fi
	done

	echo "kill all pppoe process" >/dev/console
	. /lib/functions.sh
	config_load network
	config_foreach down_all_pppoe interface
	killall pppd
	sleep 2
}
