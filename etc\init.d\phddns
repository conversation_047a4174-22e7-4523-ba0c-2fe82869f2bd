#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=99


local PHDDNS_INIT=/tmp/phddns.init
PHDDNS_READY=/tmp/phddnsFlag

local phddns_enabled_rule_num=0

get_rule_num(){
	config_get interface $1 interface
	config_get enable $1 enable
	
	if [ -n "${interface}" ];then
		let phddns_enabled_rule_num++
	fi
}

get_phddns_num()
{
	config_load phddns
	config_foreach  get_rule_num phddns
}

start()
{
		get_phddns_num
		
		if [ ${phddns_enabled_rule_num} -gt 0 ];then
		{
			#echo "phddns_enabled_rule_num = ${phddns_enabled_rule_num},restart phddns" > /dev/console
			touch $PHDDNS_INIT
			touch $PHDDNS_READY	
			service_start /usr/sbin/phddnsd
			
		}
		else 
		{
			if [ -f $PHDDNS_INIT ];then
			{
				#echo "phddns_enabled_rule_num = 0,but not init state,restart phddns" > /dev/console
				touch $PHDDNS_INIT
				touch $PHDDNS_READY	
				service_start /usr/sbin/phddnsd
			}
			fi
		}
		fi
        return 0
}

stop()
{
        service_stop /usr/sbin/phddnsd
        return 0
}

restart()
{
        stop
		start
}

reload()
{
        restart   
}

