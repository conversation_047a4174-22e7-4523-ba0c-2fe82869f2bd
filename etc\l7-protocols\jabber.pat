# Jabber (XMPP) - open instant messenger protocol - RFC 3920 - http://jabber.org
# Pattern attributes: good notsofast notsofast
# Protocol groups: chat ietf_proposed_standard
# Wiki: http://www.protocolinfo.org/wiki/Jabber
# Copyright (C) 2008 <PERSON>, <PERSON>; See ../LICENSE
#
# This pattern has been tested with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.  It is only tested 
# with non-SSL mode <PERSON><PERSON><PERSON> with no proxies.

# Thanks to <PERSON> for some improvements.

# J<PERSON><PERSON> seems to take a long time to set up a connection.  I'm
# connecting with Gabber 0.8.8 to 12jabber.org and the first 8 packets
# is this:
# <stream:stream to='12jabber.com' xmlns='jabber:client'
# xmlns:stream='http://etherx.jabber.org/streams'><?xml
# version='1.0'?><stream:stream
# xmlns:stream='http://etherx.jabber.org/streams' id='3f73e951'
# xmlns='jabber:client' from='12jabber.com'>
#
# No mention of my username or password yet, you'll note.

jabber
<stream:stream[\x09-\x0d ][ -~]*[\x09-\x0d ]xmlns=['"]jabber
