#! /bin/sh
#
# lib_sdb_app.sh
# author : fufeng<PERSON>@tp-link.com.cn
# date   : 2021/5/25

. /lib/sdb/dbg.sh

PORTAL_NOTIFY_SCRIPT_PATH="/usr/lib/lua/luci/model/wechat_domain_handler.lua"

# purpose: 读取特征库管理模块解包后的数据，将其导入到提交目录中
# call   : 当特征库从签名库导出后，会调用该接口对特征库数据进一步处理
#           从而得到CSE可用的数据
prepare_commit(){
    local dataDir="$1"
    local commitDir="$2"
    if [ -z "$dataDir" ] || [ -z "$commitDir" ]; then
        dbg_error "param error: lack dataDir or commitDir"
        dbg_echo  "Usage : $SHELL_NAME load dataDir commitDir"
        return 1
    fi

    # 在加载完 app.db 后生成 app_list.csv, 用于向云端上报当前设备的应用识别支持情况
    lua /usr/lib/lua/sdb/app_list_construct.lua

    appjson2csedb ${dataDir}/app.json;
    # 配置兼容
    /etc/cfgsync.d/11_app_library_cfg_sync.sh
}

# purpose: 将提交目录和CSE中的数据清除
# call   : 当不需要该库时，会调用该接口清除commit目录下的文件，去除
#           CSE中该特征库数据
restore_factory_commit(){
    local commitDir="$1"
    if [ -z "$commitDir" ]; then
        dbg_error "param error: lack commitDir"
        return 1
    fi

    echo \# > ${commitDir}/appsig.rules
    echo \# > ${commitDir}/appsig_port.conf
}

# purpose : 用于提交后，将占用内存较大的文件删除，减少内存占用;
#           内存占用较小，可以不做处理
clean_after_commit(){
    # Notify portal that sdbapp has changed.
    [ -f $PORTAL_NOTIFY_SCRIPT_PATH ] && {
        lua $PORTAL_NOTIFY_SCRIPT_PATH update
    }
}

case "$1" in
prepare)
    prepare_commit "$2" "$3"
    ;;
restore)
    restore_factory_commit "$2"
    ;;
clean)
    clean_after_commit "$2"
    ;;
esac

exit "$?"
