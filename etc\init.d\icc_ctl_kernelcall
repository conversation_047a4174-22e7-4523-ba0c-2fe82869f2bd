#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2021 Shenzhen TP-LINK Technologies Co.Ltd.
# file     icc_ctl_kernelcall
# brief    Loads Inter-Core Communication based on shared memory modules.
# author   <PERSON>
# version  1.0.0
# date     18May21
# history  1.0.0, 18May21, <PERSON>, Initial version

START=78

start() {
    insmod icc
    insmod icc_kernelcall
}

stop() {
    # Rmmod transports. Ignore the err msg for not-exist
    rmmod icc_kernelcall 2> /dev/null

    rmmod icc 2> /dev/null
}
