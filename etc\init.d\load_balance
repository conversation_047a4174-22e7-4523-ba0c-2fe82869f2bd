#!/bin/sh /etc/rc.common

START=96

IPT="iptables -t mangle -w"

start()
{
	. /lib/balance/api.sh
	state=`get_balance_global_state`
	[ "$state" == "off" ] && return

	state=`uci get load_balance.basic.balance_state 2>/dev/null`

	if [ "$state" == "on" ]; then
		/usr/sbin/load_balance &
	fi

	touch /tmp/load_balance.ready
}

stop()
{
	#killall load_balance &> /dev/null
	if [ -f /var/run/load_balance.pid ];then
		kill $(cat /var/run/load_balance.pid) &> /dev/null
		rm /var/run/load_balance.pid &> /dev/null
	fi

	#delete ip rule
	for rule in $(ip rule list | egrep '^[7][0-9]{3}\:' | cut -d ':' -f 1); do
		ip rule del pref $rule &> /dev/null
	done
	for rule in $(ip rule list | egrep '^[8][0-9]{3}\:' | cut -d ':' -f 1); do
		ip rule del pref $rule &> /dev/null
	done

	#delete load_balance
	$IPT -D PREROUTING -j load_balance &> /dev/null

	$IPT -F load_balance_pre &> /dev/null
	$IPT -F load_balance_new &> /dev/null
	$IPT -F load_balance &> /dev/null

	$IPT -X load_balance &> /dev/null
	$IPT -X load_balance_new &> /dev/null
	$IPT -X load_balance_pre &> /dev/null

	#destroy load balance ipset
	ipset destroy load_balance_set &> /dev/null
}

restart() {
	( flock -x 69
		stop
		start
	  flock -u 69
	) 69<>/tmp/.load_balance_lock
}
