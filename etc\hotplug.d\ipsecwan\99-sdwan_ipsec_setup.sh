#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.
. /lib/zone/zone_api.sh


CONFIG_NAME="vpn"
SECTION_NAME="ipsec_connection"
OPTION_KEY="virtualif"
OPTION_ENTRY_ID="entry_id"

[ ! -f /tmp/iface_setup.ready ] && {
    exit 0
}

# ipset list ${INTERFACE}_FACES 2>/dev/null || exit 0

report_ipsec_iface_event(){
	section_name=$1
	xfrmi_key=$2

	config_get section_xfrmi $section_name $OPTION_KEY
	if [ -z "$section_xfrmi" -o "$section_xfrmi" != "$xfrmi_key" ]; then
		return
	fi
	config_get entry_id $section_name $OPTION_ENTRY_ID
	# 上报对应接口的up/down事件
	ubus call cloudclient report_notice_msg '{"msg":"{\"noticeType\":\"LINK_STATUS_NOTIFY\", \"attachment\":{\"entryIdList\":[\"'$entry_id'\"]}}"}' 2>/dev/null
}

case "$ACTION" in
	ifup)
		{
			/usr/lib/lua/sdwan/handle_ipsec_up_down write ${XFRMI_IF}
		}
	;;
	ifdown)
		{
			/usr/lib/lua/sdwan/handle_ipsec_up_down write ${XFRMI_IF}
		}
	;;
	ifupdate)
		# do nothing
	;;
esac
