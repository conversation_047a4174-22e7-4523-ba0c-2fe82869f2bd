#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

# shellcheck disable=SC2034

START=84

start () {
	# save url_uid to uci
	local url_uid
	url_uid=`url_uid_gen`
	uci set license.global=global
	uci set license.global.url_uid=$url_uid
	uci commit license

	if [ ! -d /tmp/system/license ]; then
		mkdir /tmp/system/license
	fi
	if [ ! -d /tmp/license ]; then
		mkdir /tmp/license
	fi
	/lib/license/license.sh start
}
