#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

[ ! -f /tmp/ipgroup.ready ] && exit

. /lib/functions.sh
. /lib/zone/zone_api.sh

delinfs=

local userif=$(zone_iface_to_userif $INTERFACE)

case ${ACTION} in
	ifup)
		[ -n "${INTERFACE}" ] && [ "${userif}" = "LAN" ] && {
			#config_load ipgroup
		    #config_foreach modify_lan rule_ipscope
			#uci_commit ipgroup
			#lua /usr/lib/lua/ipgroup/ipgroup_reload.lua
			local netmask=$(uci get network.${INTERFACE}.netmask)
			local ipaddr=$(uci get network.${INTERFACE}.ipaddr)
			local network=$(ipcalc -n ${ipaddr} ${netmask} | cut -d '=' -f2)
			local prefix=$(ipcalc -p ${ipaddr} ${netmask} | cut -d '=' -f2)
			lua /usr/lib/lua/ipgroup/lanhook.lua "${network}/${prefix}"
			ipset flush "IPGROUP_LAN"
			ipset add "IPGROUP_LAN" "${network}/${prefix}" -exist
		}
	;;
	*)
	;;
esac