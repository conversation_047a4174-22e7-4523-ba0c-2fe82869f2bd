#!/usr/bin/lua

local uci = require ("luci.model.uci")
local cloud_sync_update = require("luci-cloud.cloud_sync_update")

-- independence of frame should notify to save cfg itself
local function cloud_sync_config()
	local uci_r = uci.cursor()
	local config_id  = uci_r:get("cloud_config", "config_id", "config_id")

	-- 兼容已出货使用cloud_config.config_id.config_id的设备
	if nil ~= config_id then
		uci_r:set("cloud_sync", "config_id", "config_id", tostring(config_id))
		uci_r:commit("cloud_sync")
		uci_r:delete("cloud_config", "config_id")
		uci_r:commit("cloud_config")
	end
end

local function cloud_sync_check_is_first_reboot_after_import()
	local uci_r = uci.cursor()
	local is_first_reboot_after_import  = uci_r:get("cloud_sync", "config_id", "is_first_reboot_after_import")
	if nil~= is_first_reboot_after_import and "1" == tostring(is_first_reboot_after_import) then
		uci_r:foreach(
			"cloud_sync", "config_modify",
			function(section)
				    for k,v in pairs(section) do
						if nil == string.find(k,"%..*") then -- 排除.name .index .type .anonymous的情况
							uci_r:set("cloud_sync", section[".name"], k, "0")
						end
					end
			end
		)
		uci_r:delete("cloud_sync", "config_id", "is_first_reboot_after_import")
		uci_r:commit("cloud_sync")
	end
end

cloud_sync_config()
cloud_sync_update_main()
cloud_sync_check_is_first_reboot_after_import()