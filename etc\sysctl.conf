kernel.panic=3
net.ipv4.conf.default.arp_ignore=1
net.ipv4.conf.all.arp_ignore=1
net.ipv4.ip_forward=1
net.ipv4.icmp_echo_ignore_broadcasts=1
net.ipv4.icmp_ignore_bogus_error_responses=1
net.ipv4.igmp_max_memberships=100
net.ipv4.tcp_ecn=0
net.ipv4.tcp_fin_timeout=30
net.ipv4.tcp_keepalive_time=120
net.ipv4.tcp_syncookies=1
net.ipv4.tcp_timestamps=1
net.ipv4.tcp_sack=1
net.ipv4.tcp_dsack=1

# Fix Bug129941
net.ipv4.icmp_errors_use_inbound_ifaddr=1

net.ipv6.conf.default.forwarding=1
net.ipv6.conf.all.forwarding=1

net.netfilter.nf_conntrack_acct=1
net.netfilter.nf_conntrack_checksum=0
net.netfilter.nf_conntrack_max=16384
net.netfilter.nf_conntrack_tcp_timeout_close=4
net.netfilter.nf_conntrack_tcp_timeout_close_wait=15
net.netfilter.nf_conntrack_tcp_timeout_established=1800
net.netfilter.nf_conntrack_tcp_timeout_fin_wait=120
net.netfilter.nf_conntrack_tcp_timeout_last_ack=30
net.netfilter.nf_conntrack_tcp_timeout_syn_recv=60
net.netfilter.nf_conntrack_tcp_timeout_syn_sent=30
net.netfilter.nf_conntrack_tcp_timeout_time_wait=15
net.netfilter.nf_conntrack_udp_timeout=30
net.netfilter.nf_conntrack_udp_timeout_stream=180
net.netfilter.nf_conntrack_tcp_no_window_check=1
net.netfilter.nf_conntrack_skip_filter=1

# disable bridge firewalling by default
net.bridge.bridge-nf-call-arptables=1
net.bridge.bridge-nf-call-ip6tables=0
net.bridge.bridge-nf-call-iptables=1
net.bridge.bridge-nf-filter-vlan-tagged=1
net.bridge.bridge-nf-filter-pppoe-tagged=1
net.bridge.bridge-nf-pass-vlan-input-dev=1
# change xfrm's gc_thresh from 1024 to 8192
# this will improve the IPSec tunnel's connection-establish performance[Bug97836].
net.ipv4.xfrm4_gc_thresh=32768
net.ipv6.xfrm6_gc_thresh=32768

net.core.rmem_max=2621440
net.core.wmem_max=2621440
# moved from system_params for early application
net.ipv4.neigh.default.gc_thresh1=512
net.ipv4.neigh.default.gc_thresh2=2048
net.ipv4.neigh.default.gc_thresh3=16384

net.ipv4.fib_multipath_use_neigh=1

# Our RT thread may run longer than expected, but it will hand out CPUs eventually.
kernel.sched_rt_runtime_us=-1

# the background kernel flusher threads will start writeback
vm.dirty_ratio=10
vm.dirty_background_ratio=5
# every 500ms wakeup flusher, dirty page expired in 3s
vm.dirty_writeback_centisecs=50
vm.dirty_expire_centisecs=300
net.netfilter.nf_conntrack_max=200000
