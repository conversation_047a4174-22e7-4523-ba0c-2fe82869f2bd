#!/bin/sh

local iface_id_min=256
local iface_id_max=511

SR_DIR="/tmp/special_route/"
SR_CUR_ID="/tmp/special_route/.cur_id"
SR_INFACE_ID="/tmp/special_route/.iface_id"

#device_id for iface down
#like:eth0=wan1
SR_DEVICE_IFACE="/tmp/special_route/.device_iface"

#$1:inface
specail_route_iface_id_alloc(){
    local iface=$1 dev
	local cur_id

	if [ ! -d "$SR_DIR" ]; then
		mkdir -p $SR_DIR
	fi

	if [ ! -f $SR_CUR_ID ]; then
		touch $SR_CUR_ID
		echo $iface_id_min > $SR_CUR_ID
	fi

	if [ ! -f $SR_INFACE_ID ]; then
		touch $SR_INFACE_ID
	fi

	if [ ! -f $SR_DEVICE_IFACE ]; then
		touch $SR_DEVICE_IFACE
	fi

	ret=`grep "^${iface}=.*" "$SR_INFACE_ID"`
	if [ -n "$ret" ];then
		echo "`cat $SR_INFACE_ID |grep "\<${iface}\>" |cut -d '=' -f 2`"
	else
	    cur_id=`cat $SR_CUR_ID`

		if [ $cur_id -gt 1 ];then
			cur_id=$((cur_id+1))
			if [ $cur_id -gt $iface_id_max ]; then
				cur_id=$iface_id_min
		    fi
		fi

		while [ 1 -eq 1 ]
		do
			local id=`cat $SR_INFACE_ID |grep "${cur_id}$"`
			if [ -z $id  ]; then
			   break;
			else
			   cur_id=$((cur_id+1))
			   if [ $cur_id -gt $iface_id_max ]; then
			       cur_id=$iface_id_min
			   fi
			fi
		done
		echo $cur_id > $SR_CUR_ID

		echo "${iface}=${cur_id}" >> "$SR_INFACE_ID"

		dev=`zone_get_effect_devices $iface`
		echo "${dev}=${iface}" >> "$SR_DEVICE_IFACE"

		echo "$cur_id"
	fi
}

#$1:DEVICE
#return iface_id
special_route_get_id_by_device()
{
	local dev=$1
	local iface

	grep "^${dev}=.*" "$SR_DEVICE_IFACE" 2>&1 >/dev/null || {
		echo "-1"
		return
	}

	iface=`cat $SR_DEVICE_IFACE |grep "\<${dev}\>" |cut -d '=' -f 2`

	if [ -n "$iface" ];then
		grep "^${iface}=.*" "$SR_INFACE_ID" 2>&1 >/dev/null || {
			echo "-1"
			return
		}

		echo "`cat $SR_INFACE_ID |grep "\<${iface}\>" |cut -d '=' -f 2`"
	else
		echo "-1"
		return
	fi
}

#$1:DEVICE
#del inface_id and device_iface file
#del the mark's hash info
special_route_del_iface_id()
{
	local dev="$1"
	local iface=`cat $SR_DEVICE_IFACE |grep "\<${dev}\>" |cut -d '=' -f 2`
	local iface_id=`cat $SR_INFACE_ID |grep "\<${iface}\>" |cut -d '=' -f 2`

	grep "^${iface}=.*" $SR_INFACE_ID 2>&1 >/dev/null && sed "/^${iface}=.*/d" -i $SR_INFACE_ID

	grep "^${dev}=.*" $SR_DEVICE_IFACE 2>&1 >/dev/null && sed "/^${dev}=.*/d" -i $SR_DEVICE_IFACE

	#del the iface_id's hash info
	#note:[iface_id==mark]
	if [ -n "$iface_id" ];then
		echo $iface_id > /proc/special_route
	fi
}

special_route_set_general_iptables()
{
	#if ! $IPT -S TP_sr_rules  &> /dev/null; then
		#$IPT -N TP_sr_rules
	#fi

	#if ! $IPT -S TP_sr_connected &> /dev/null; then
	#	$IPT -N TP_sr_connected
	#fi

	if ! $IPT -S TP_sr_hook &> /dev/null; then
		$IPT -N TP_sr_hook
		#save conneted's mark to all packages
		$IPT -A TP_sr_hook -j CONNMARK --restore-mark --nfmask 0x7ff --ctmask 0x7ff
		#except the policy_route's packages, the mark is 0x600-0x6FF
		#because the policy_route's priority is greater than special_route's
		#$IPT -A TP_sr_hook -m conntrack --ctstate NEW -m mark ! --mark 0x600/0x600 -j TP_sr_rules
		$IPT -A TP_sr_hook -m mark --mark 0/0x7ff -j special_route --mask 0x7ff -m comment --comment "Special route find mark"

		#save package's mark to conneted
		$IPT -A TP_sr_hook -j CONNMARK --save-mark --nfmask 0x7ff --ctmask 0x7ff
		#$IPT -A TP_sr_hook -j TP_sr_connected
	fi

	#if ! $IPT -S TP_sr_post_rules  &> /dev/null; then
		#$IPT -N TP_sr_post_rules
	#fi

	if ! $IPT -S TP_sr_post_hook &> /dev/null; then
		$IPT -N TP_sr_post_hook
		#$IPT -A TP_sr_post_hook -m mark ! --mark 0x600/0x600 -j TP_sr_post_rules
		$IPT -A TP_sr_post_hook -j CONNMARK --save-mark --nfmask 0x7ff --ctmask 0x7ff
	else
		$IPT -F TP_sr_post_hook
		$IPT -A TP_sr_post_hook -j CONNMARK --save-mark --nfmask 0x7ff --ctmask 0x7ff
	fi

	if ! $IPT -S POSTROUTING |grep TP_sr_post_hook &>/dev/null; then
		#check the wifidog
		wifidog_income=`$IPT -n --line-number -L POSTROUTING |grep WiFiDog_Incoming`
		if [ -n "$wifidog_income" ];then
			num=`echo $wifidog_income |cut -d " " -f 1`
			$IPT -I POSTROUTING $num -m conntrack --ctstate NEW -m mark ! --mark 0x600/0x600 -j TP_sr_post_hook
		else
			$IPT -A POSTROUTING -m conntrack --ctstate NEW -m mark ! --mark 0x600/0x600 -j TP_sr_post_hook
		fi
	fi

	##policy_route[TP_pr_hook]---->special_route[TP_sr_hook]------>isp_route---->load_balance--->default_balance--->wifidog
	if ! $IPT -S PREROUTING | grep TP_sr_hook &> /dev/null; then
		TP_policy_route=`$IPT -n --line-number -L PREROUTING |grep TP_pr_hook`
		if [ -n "$TP_policy_route" ];then
			num=`echo $TP_policy_route |cut -d " " -f 1`
			let num++
			$IPT -I PREROUTING $num -j TP_sr_hook
		else
			isp_route=`$IPT -n --line-number -L PREROUTING |grep isp_route`
			if [ -n "$isp_route" ]; then
				num=`echo $isp_route |cut -d " " -f 1`
				$IPT -I PREROUTING $num -j TP_sr_hook
			else
				load_balance=`$IPT -n --line-number -L PREROUTING |grep load_balance`
				default_balance=`$IPT -n --line-number -L PREROUTING |grep default_balance`
				if [ -n "$load_balance" ]; then
					num=`echo $load_balance |cut -d " " -f 1`
					$IPT -I PREROUTING $num -j TP_sr_hook
				elif [ -n "$default_balance" ]; then
					num=`echo $default_balance |cut -d " " -f 1`
					$IPT -I PREROUTING $num -j TP_sr_hook
				else
					#check the wifidog
					local wechat_num=0 trust_num=0 outgo_num=0 min_num=65535
					wifidog_trust=`$IPT -n --line-number -L PREROUTING |grep WiFiDog_Trusted`
					wifidog_outgo=`$IPT -n --line-number -L PREROUTING |grep WiFiDog_Outgoing`
					wifidog_wechat=`$IPT -n --line-number -L PREROUTING |grep WiFiDog_Wechat`

					if [ -n "$wifidog_wechat" ];then
						wechat_num=`echo $wifidog_wechat |cut -d " " -f 1`
						[ $wechat_num -lt $min_num ] && min_num=$wechat_num
					fi

					if [ -n "$wifidog_trust" ]; then
						trust_num=`echo $wifidog_trust |cut -d " " -f 1`
						[ $trust_num -lt $min_num ] && min_num=$trust_num
					fi

					if [ -n "$wifidog_outgo" ]; then
						outgo_num=`echo $wifidog_outgo |cut -d " " -f 1`
						[ $outgo_num -lt $min_num ] && min_num=$outgo_num
					fi

					if [ $min_num -eq 65535 ];then
						$IPT -A PREROUTING -j TP_sr_hook
					else
						$IPT -I PREROUTING $num -j TP_sr_hook
					fi
				fi
			fi
		fi
	fi

	#$IPT -F TP_sr_rules
	#$IPT -F TP_sr_post_rules
}

special_route_set_connected_iptables()
{
	local connected_networks

	if $IPT -S TP_sr_connected  &> /dev/null; then
		$IPT -F TP_sr_connected

		ipset -! create TP_sr_connected hash:net

		for connected_network in $(ip -4 route | awk '{print $1}' | egrep '[0-9]{1,3}(\.[0-9]{1,3}){3}'); do
			ipset -! add TP_sr_connected $connected_network
		done

		for connected_network in $(ip -4 route list table 0 | awk '{print $2}' | egrep '[0-9]{1,3}(\.[0-9]{1,3}){3}'); do
			ipset -! add TP_sr_connected $connected_network
		done

		$IPT -A TP_sr_connected -m set --match-set TP_sr_connected dst -j MARK --set-mark 0x7ff/0x7ff
	fi
}

special_route_set_iface_rules()
{
	local iface_id

	if [ $ACTION == "ifup" ]; then
		iface_id=`specail_route_iface_id_alloc $INTERFACE`
		[ -n "$iface_id" ] || return 0
	else
		#get iface_id for del the ip rule
		iface_id=`special_route_get_id_by_device $DEVICE`

		[ $iface_id -eq -1 ] && return 0
	fi

	if [ -n "$(ip -4 rule list | awk '$1 == "'$(($iface_id+3000)):'"')" ]; then
		ip -4 rule del pref $(($iface_id+3000)) &> /dev/null
	fi

	if [ -n "$(ip -4 rule list | awk '$1 == "'$(($iface_id+4000)):'"')" ]; then
		ip -4 rule del pref $(($iface_id+4000)) &> /dev/null
	fi

	if [ $ACTION == "ifup" ];then
		local count=0

		iface=`zone_get_effect_ifaces $INTERFACE`
		while [ -z "$iface" -a "$count" -lt 30 ]; do
			sleep 1
			iface=`zone_get_effect_ifaces $INTERFACE`
			let count++
			if [ "$count" -ge 30 ]; then
				echo "[`date`]: Failed to get iface by zone in special_route_set_iface_rules, INTERFACE=[$INTERFACE], ACTION=[$ACTION]" >>/tmp/special_route/special_route.log
			fi
		done

		state=`balance_get_state $INTERFACE`
		[ "$state" == "on" ] || return 0

		ret=`ip -4 rule add pref $(($iface_id+4000)) fwmark $iface_id/0x7ff lookup $iface 2>&1`
		[ -n "$ret" ] || ip -4 rule add pref $(($iface_id+3000)) iif $DEVICE lookup main
	fi
}


special_route_set_find_mark_iptables()
{
	$IPT -A TP_sr_rules -m mark --mark 0/0x7ff -j special_route --mask 0x7ff -m comment --comment "Special route find mark"
}

special_set_post_policy()
{
	local iface_id

	state=`balance_get_state $1`
	[ "$state" == "on" ] || return 0

	iface_id=`specail_route_iface_id_alloc $1`

	if [ -n "$iface_id" ];then
		device=`zone_get_effect_devices $1`
		#$IPT -A TP_sr_post_rules -o $device -m comment --comment "inface=[$1]" -j MARK --set-mark $iface_id/0x7ff

		$IPT -I TP_sr_post_hook 1 -o $device -m comment --comment "inface=[$1]" -j MARK --set-mark $iface_id/0x7ff
	fi
}

special_route_set_post_iptables()
{
	config_list_foreach special_policy use_if special_set_post_policy
}

#check the iface in use_policy
#flag = 1 inside
#flag = 0 outside
special_route_check()
{
	flag=`uci get mwan3.$INTERFACE.balance 2>/dev/null`
}

#clean the iptables when the policy_ifaces down
special_route_clean_rule()
{
	ifaces=`uci get mwan3.special_policy.use_if 2>/dev/null`
	for iface in $ifaces;do
		state=`balance_get_state $iface`
		if [ "$state" == "on" ];then
			return
		fi
	done

	$IPT -D PREROUTING -j TP_sr_hook &> /dev/null
	$IPT -D POSTROUTING -m conntrack --ctstate NEW -m mark ! --mark 0x600/0x600 -j TP_sr_post_hook &> /dev/null

	for table in $($IPT -S | awk '{print $2}' | grep TP_sr | sort -u); do
		$IPT -F $table &> /dev/null
	done

	for table in $($IPT -S | awk '{print $2}' | grep TP_sr | sort -u); do
		$IPT -X $table &> /dev/null
	done
}

special_route_ifupdown()
{
	local flag=0 policy
	local error="table id value is invalid"

	if [ $ACTION == "ifup" ]; then
		[ -n "$DEVICE" ] || exit 0
		[ -n "$INTERFACE" ] || exit 0

		special_route_check
		#the iface outside of the special_route
		[ $flag -eq 0 ] && exit 0

		# Reload the whole module and exit if it has not been started before, and
		# no one else is reloading it.
		# This happens when there is only one interface up at boot.
		[ ! -f /proc/special_route ] && {
			if [ -z "$(find /tmp -maxdepth 1 -name '.special_route_restarting_*')" ]; then
				touch /tmp/.special_route_restarting_$$
				return
			else
				# Someone is already reloading. Wait a bit.
				local _count=0
				while [ -n "$(find /tmp -maxdepth 1 -name '.special_route_restarting_*')" ]; do
					sleep 1
					let _count++
					[ "$_count" -ge 30 ] && {
						echo "[`date`]: Timeout waiting for reload. Give up on this event. INTERFACE=[$INTERFACE], ACTION=[$ACTION]" >>/tmp/special_route/special_route.log
						exit 0
					}
				done
			fi
		}

		local count=0
		iface=`zone_get_effect_ifaces $INTERFACE`
		while [ -z "$iface" -a "$count" -lt 30 ]; do
			sleep 1
			iface=`zone_get_effect_ifaces $INTERFACE`
			let count++
			if [ "$count" -ge 30 ]; then
				echo "[`date`]: Failed to get iface by zone, INTERFACE=[$INTERFACE], ACTION=[$ACTION]" >>/tmp/special_route/special_route.log
				exit 0
			fi
		done

		ret=`ip route list table $iface 2>&1 |grep "$error"`
		if [ -n "$ret" ];then
			echo "[`date`]: Failed to get ip route, when iface=[$INTERFACE], ACTION=[$ACTION]" >> /tmp/special_route/special_route.log
			exit 0
		fi
	fi

	config_load mwan3

	special_route_set_general_iptables

	special_route_set_iface_rules

	#special_route_set_find_mark_iptables

	special_route_set_post_iptables

	#special_route_set_connected_iptables

	if [ $ACTION == "ifdown" ] ;then
		special_route_clean_rule
		special_route_del_iface_id $DEVICE
	fi
}

IPT="iptables -t mangle -w"

. /lib/functions.sh
. /lib/zone/zone_api.sh
. /lib/balance/api.sh

# Do not set rules for single WAN.
# But rules already added should be deleted, and our now redundant
# and useless rules should be removed.
local g_state=`balance_get_open_state`
[ $g_state = "disable" ] && {
	[ -f /proc/special_route ] && /etc/init.d/mwan3 stop
	return
}

local start_flag=0

#start the special_route
if [ "$ACTION" == "start" ];then
	start_flag=1
	ACTION="ifup"
fi

state=`get_balance_global_state`
[ "$state" == "off" ] && return

case "$ACTION" in
	ifup|ifdown)
		( flock -x 56

			if [ -f /tmp/mwan3.ready ] || [ $start_flag -eq 1 ];then
				special_route_ifupdown
			fi

		  flock -u 56
		) 56<>/tmp/.speical_route_lock

		[ -f /tmp/.special_route_restarting_$$ ] && {
			/etc/init.d/mwan3 restart
			rm /tmp/.special_route_restarting_$$
		}
	;;
esac
