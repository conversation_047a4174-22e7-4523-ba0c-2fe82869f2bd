#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"

function get_listen_list(old_value)
    if old_value == nil or type(old_value) ~= "table" then
        return nil
    end

    local old_ipv4 = ""
    local old_ipv6 = ""

    -- 获取之前的ipv4监听地址
    for i = 1, #old_value do
        if old_value[i]:match("0\.0\.0\.0:") then
            old_ipv4 = old_value[i]
        end
    end

    if old_ipv4 == "" then
        return nil
    end

    -- 如果ipv6监听地址为空，补全ipv6监听地址（与ipv4地址相同端口号）
    if old_ipv6 == "" then
        old_ipv6 = old_ipv4:gsub("0\.0\.0\.0", "[::]")
    end
    -- dbg(old_ipv4, old_ipv6)
    return {
        old_ipv4, old_ipv6
    }
end

-- uci config_sync
local uci_uhttpd = {
    name = "uhttpd",
    snames = {
        {
            name = "main",
            additional_fields = {},
            list_contains = {
                listen_http_lan = function(index, entry)
                    return get_listen_list(entry.listen_http_lan)
                end,
                listen_https = function(index, entry)
                    return get_listen_list(entry.listen_https)
                end
            }
        }
    }
}

-- database config_sync
cfgsync.config_sync(uci_uhttpd, "configfile")
