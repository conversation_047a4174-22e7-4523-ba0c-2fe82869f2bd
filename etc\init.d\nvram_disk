#!/bin/sh /etc/rc.common
# 在使用flash的情况下，初始化对应目录

START=18
STOP=18

SYSTEM_BLK="/dev/mmcblk0p1"
USER_BLK="/dev/mmcblk0p2"
SDB_BLK="/dev/mmcblk0p3"

SDB_DIR="/tmp/sdb"
CSEDB_SHADOW="/tmp/sdb/csedb_shadow"
SDB_NVRAM="/tmp/sdb/nvram"
SDB_NVRAM_2="/tmp/sdb/nvram_2"
SYSTEM_NVRAM="/tmp/system"
USER_NVRAM="/tmp/user_nvram"

disk_dev_check_and_mount() {
    dev=$1
    dir=$2

    if [ -z "$1" -o -z "$2" ]; then
        echo "Invalid param \"$1\" \"$2\" . Abort" > /dev/console
        return 2
    fi

     # fsck.ext4 return value:
     # 0    - No errors
     # 1    - File system errors corrected
     # 2    - File system errors corrected, system should be rebooted
     # 4    - File system errors left uncorrected
     # 8    - Operational error
     # 16   - Usage or syntax error
     # 32   - E2fsck canceled by user request
     # 128  - Shared library error

    umount $1 2>/dev/null
    fsck.ext4 -fy $1
    if [ $? -gt 3 ]; then
        echo "Bad ext4 fs in dev $dev" > /dev/console
        mkfs.ext4 -m 0 -F $dev
        if [ $? -ne 0 ]; then
            echo "Mkfs.ext4 in dev $dev fail. Abort" > /dev/console
            return 1
        fi
    fi

    mkdir -p $dir
    mount -t ext4 $dev $dir
}

start() {
    mkdir -p ${SDB_DIR}
    mkdir -p ${CSEDB_SHADOW}
    # 在flash中创建保存目录
    if [ ! -d "$SDB_NVRAM" ]; then
        mkdir -p "${SDB_NVRAM}"
    fi

    if [ -b "$SDB_BLK" ]; then
        disk_dev_check_and_mount $SDB_BLK $SDB_NVRAM
        mkdir -p ${SDB_NVRAM}/nvram_2
        ln -s ${SDB_NVRAM}/nvram_2 ${SDB_NVRAM_2}
    fi

    if [ -b "$SYSTEM_BLK" ]; then
        disk_dev_check_and_mount $SYSTEM_BLK $SYSTEM_NVRAM
    fi

    if [ -b "$USER_BLK" ]; then
        disk_dev_check_and_mount $USER_BLK $USER_NVRAM
    fi
}

stop() {
    # Nothing to do
    true
}
