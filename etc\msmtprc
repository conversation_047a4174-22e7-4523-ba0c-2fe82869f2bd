# Example for a system wide configuration file

# A system wide configuration file is optional.
# If it exists, it usually defines a default account.
# This allows msmtp to be used like /usr/sbin/sendmail.
account default

# The SMTP smarthost
host mail.oursite.example

# Use TLS on port 465
port 465
tls on
tls_starttls off

# Construct envelope-from addresses of the form "<EMAIL>"
from %<EMAIL>

# Syslog logging with facility LOG_MAIL instead of the default LOG_USER
syslog LOG_MAIL
