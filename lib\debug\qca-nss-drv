#!/bin/sh /sbin/sysdebug
#
# Copyright (c) 2015-2016, The Linux Foundation. All rights reserved.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
# OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
#

log cat /sys/kernel/debug/qca-nss-drv/stats/pppoe
log cat /sys/kernel/debug/qca-nss-drv/stats/n2h
log cat /sys/kernel/debug/qca-nss-drv/stats/ipv6
log cat /sys/kernel/debug/qca-nss-drv/stats/ipv4
log cat /sys/kernel/debug/qca-nss-drv/stats/gmac
log cat /sys/kernel/debug/qca-nss-drv/stats/drv
log cat /sys/kernel/debug/qca-nss-drv/stats/wifi
log cat /sys/kernel/debug/qca-nss-drv/stats/wifi_if
log cat /sys/kernel/debug/qca-nss-drv/stats/eth_rx
