#!/bin/sh /etc/rc.common
# Copyright (C) 2006 OpenWrt.org

START=60
BIN=pptpd
DEFAULT=/etc/default/$BIN
RUN_D=/var/run
PID_F=$RUN_D/$BIN.pid

start() {
	/lib/pptp/test.sh
	[ -f $DEFAULT ] && . $DEFAULT
	mkdir -p $RUN_D
	for m in arc4 sha1 slhc crc-ccitt ppp_generic ppp_async ppp_mppe_mppc; do
		insmod $m >/dev/null 2>&1
	done
	#$BIN $OPTIONS
	lua /lib/pptp/pptp_process_manager.lua
}

stop() {
	[ -f $PID_F ] && kill $(cat $PID_F)
}


restart() {
	stop
	start
}