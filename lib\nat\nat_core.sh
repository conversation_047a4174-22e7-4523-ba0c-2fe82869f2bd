#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat.init
# brief    
# author   <PERSON> chen
# version  1.0.0
# date     20Apr15
# histry   arg 1.0.0, 20Apr15, <PERSON> chen, Create the file. 

NAT_LIBDIR=${NAT_LIBDIR:-/lib/nat}

. /lib/functions.sh
. /lib/functions/network.sh
. /lib/zone/zone_api.sh
include /lib/network

. ${NAT_LIBDIR}/nat_public.sh

NAT_READY=/tmp/nat.ready

nat_main() {
NAT_PRINT "## nat_main args: $1 $2"
	local which=$1
	nat_init

	nat_is_loaded && {
		NAT_LOG_PRINT " info, nat already loaded, exit "
		exit 1
	}

	echo "@ nat_set_state"
	uci_revert_state nat
	uci_set_state nat core "" nat_state
	uci_set_state nat env "" nat_env_state

	case $which in
		nat)
			shift
	NAT_LOG_PRINT " info, cmd=nat_run $1"
			nat_run "$1"
		;;
		*)
	NAT_LOG_PRINT " info(no nat), cmd=nat_run $@"
			nat_run "$@"
		;;
	esac
}

nat_start() {
NAT_LOG_PRINT " ################## nat_start ##################"
    {
		flock -x 50
	nat_main nat "$1"
	touch $NAT_READY
		flock -u 50
	} 50<>/tmp/nat.lock
}

nat_stop() {
NAT_LOG_PRINT " ################## nat_stop ##################"
    {
		flock -x 50
	nat_init
	nat_clear
	config_clear
	uci_revert_state nat
		flock -u 50
	} 50<>/tmp/nat.lock
	
	rm -rf $NAT_READY
}

nat_restart() {
	echo -e "time\t\t\tlog_info,restart proc" > "$NAT_LOG"
NAT_LOG_PRINT " ################## nat_restart ##################"
	nat_stop
	nat_start "$1"
	env -i RESTART=on /sbin/hotplug-call nat
}



nat_reload() {
    echo -e "time\t\t\tlog_info,reload proc" > "$NAT_LOG"
	nat_restart $1
}

nat_init() {

	. $NAT_LIBDIR/config.sh
	#. $NAT_LIBDIR/nat_log.sh

	nat_config_append nat
	nat_config_append administration
	#nat_config_append network

	# source nat_* sh 
	local f n
	for f in $NAT_LIBDIR/nat_*.sh; do
		n=$(basename $f.sh)
		if [ "$n" != "nat_core" -a "$n" != "nat_log" ]; then
			. $f
		fi
	done

	NAT_INITIALIZED=1

	return 0
}