#!/bin/sh

[ "$INTERFACE" == "loopback" ] && exit 0

. /lib/functions.sh
. /lib/zone/zone_api.sh
. /lib/functions/network.sh

dev=`zone_get_effect_devices $INTERFACE`

case "$ACTION" in
        ifup)
			[ -n "${dev}" ]  && {
				echo "[dnsproxy] $INTERFACE $dev up" > /dev/console
				ubus call dnsproxy update_if "{\"name\":\"$INTERFACE\",\"dev\":\"$dev\",\"status\":\"on\"}"
			}
        ;;
        ifdown)
			[ -n "${dev}" ]  && {
				echo "[dnsproxy] $INTERFACE $dev down" > /dev/console
				ubus call dnsproxy update_if "{\"name\":\"$INTERFACE\",\"dev\":\"$dev\",\"status\":\"off\"}"
			}
        ;;
esac



