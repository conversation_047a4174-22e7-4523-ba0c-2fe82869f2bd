#! /bin/sh
#
# lib_sdb_ips.sh
# author : fuf<PERSON><PERSON>@tp-link.com.cn
# date   : 2021/5/25

. /lib/sdb/dbg.sh
. /lib/sdb/sdb_config.sh

# purpose: 回滚特征库版本，将备份目录中的文件放回提交目录中，具体包括：
#           1、release_url_signed.sdb
#           2、url_plugin_uci
#           3、ips-ipcredit.db，ips-metadata.db
# call   : 当检查发现特征库版本不合法时，会调用该接口进行回滚操作
fallback_commit(){
    local dbname="ips"
    local sdb_file="$(get_sdb_file_name ${dbname})"
    local sdb_save_dir="$(get_sdb_save_dir ${dbname})"
    local sdb_bak_dir="$(get_sdb_bak_dir ${dbname})"
    local sdb_uci="$(get_sdb_uci ${dbname})"
    local sdb_tmp_dir="$(get_sdb_tmp_dir ${dbname})"
    local sdb_tmp_bak_dir="$(get_sdb_tmp_bak_dir ${dbname})"

    rm -f ${sdb_save_dir}/${sdb_file}
    mv "${sdb_bak_dir}/${sdb_file}" ${sdb_save_dir}/
    rm -f ${sdb_save_dir}/${sdb_uci}
    mv "${sdb_bak_dir}/${sdb_uci}" ${sdb_save_dir}/
    rm -f ${sdb_tmp_dir}/*
    mv ${sdb_tmp_bak_dir}/* ${sdb_tmp_dir}/
}

get_max_source_index(){
    local dataDir="$1"
    local max_source_index=0
    local ipsdb_version=$(sqlite3 "${dataDir}/ips-metadata.db" "SELECT schema_version FROM metadata;")

    # 直接使用 schema_version >= 3 判断数据库版本是否有规格限制.
    if [ "$ipsdb_version" -lt "3" ]; then
        dbg_echo "ips version too low, please upgrade to the new ips database"
        echo 0
        return 1
    fi
    max_source_index=$(sqlite3 "${dataDir}/ips-metadata.db" "SELECT MAX(source_index) FROM signatures;")
    if [ "$?" -eq 1 ]; then
        dbg_echo "ips version too low, please upgrade to the new ips database"
        echo 0
        return 1
    fi

    echo ${max_source_index}
}

# purpose: 读取特征库管理模块解包后的数据，将其导入到提交目录中
#            然后根据license中获得的spec,修改数据库数据规格
# call   : 当特征库从签名库导出后，会调用该接口对特征库数据进一步处理
#           从而得到CSE可用的数据，并根据license
prepare_commit(){
    local dbname="ips"
    local dataDir="$1"
    local commitDir="$2"
    if [ -z "$dataDir" ] || [ -z "$commitDir" ]; then
        dbg_error "param error: lack dataDir or commitDir"
        dbg_echo  "Usage : $SHELL_NAME prepare_commit dataDir commitDir"
        return 1
    fi

    mv ${dataDir}/*.rules ${commitDir}/

    local spec_num="$(. /lib/license/license_api.sh && license_get_spec_num ${dbname})"
    if [ -n "$spec_num" ] && [ -f "${dataDir}/ips-metadata.db" ]; then
        # 检测数据库中 signatures 表数据是否有 source_index 列，并获得 source_index 最大值
        local max_source_index="$(get_max_source_index ${dataDir})"
        if [ "$max_source_index" -eq "0" ]; then
            fallback_commit
            # 回滚特征库版本后，还要检查原来的特征库是否合法
            max_source_index="$(get_max_source_index ${dataDir})"
            if [ "$max_source_index" -eq "0" ]; then
                fallback_commit
            fi
            return 1
        fi

        # 根据 spec_num 删除对应顺序的数据
        if [ "$max_source_index" -ge "$spec_num" ]; then
            local start_index=$((spec_num + 1))
            sqlite3 "${dataDir}/ips-metadata.db" "DELETE FROM signatures WHERE source_index >= ${start_index};"
            sqlite3 "${dataDir}/ips-metadata.db" "VACUUM;"
        fi
    fi
}

# purpose: 将提交目录和CSE中的数据清除
# call   : 当不需要该库时，会调用该接口清除commit目录下的文件，去除
#           CSE中该特征库数据
restore_factory_commit(){
    local commitDir="$1"
    if [ -z "$commitDir" ]; then
        dbg_error "param error: lack commitDir"
        return 1
    fi

    echo \# > ${commitDir}/ips.rules
}

# purpose : 用于提交后，将占用内存较大的文件删除，减少内存占用
#           内存占用较小，可以不做处理
clean_after_commit(){
    # nothing to do
    true
}

case "$1" in
prepare)
    prepare_commit "$2" "$3"
    ;;
restore)
    restore_factory_commit "$2"
    ;;
clean)
    clean_after_commit "$2"
    ;;
esac

exit "$?"
