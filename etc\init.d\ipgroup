#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

START=42

IPGROUP_LIBDIR=/lib/ipgroup

ipgroup() {
	. $IPGROUP_LIBDIR/core.sh
	
	ipgroup_$1
}

start() {
	ipgroup start

	touch /tmp/ipgroup.ready
	lua /usr/lib/lua/openvpn/resource_config.lua reload
	lua /usr/lib/lua/ipgroup/sdwan_subnet_init.lua start
}

stop() {
	ipgroup stop
	rm -rf /tmp/ipgroup.ready
}

restart() {
	rm -rf /tmp/ipgroup.ready
	ipgroup restart
	touch /tmp/ipgroup.ready
	lua /usr/lib/lua/openvpn/resource_config.lua reload
}

reload() {
	ipgroup reload
	lua /usr/lib/lua/openvpn/resource_config.lua reload
}
