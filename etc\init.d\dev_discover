#!/bin/sh /etc/rc.common
# Copyright (C) 2007-2012 OpenWrt.org

START=65

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1

start() {
	#register feature
	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo 15","wait_time":0,"type":"feature","action":"add"}' &> /dev/null

	#start dev_discover
	service_start /bin/dev_discover

	touch /tmp/.dev_discover.ready
}

stop() {
	#stop dev_discover
	service_stop /bin/dev_discover

	rm /tmp/.dev_discover.ready

	#unregister feature
	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo 15","wait_time":0,"type":"feature","action":"delete"}' &> /dev/null
}

restart() {
	stop
	start
}
