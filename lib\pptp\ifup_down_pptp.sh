#!/bin/sh
. /lib/zone/zone_api.sh
. /lib/pptp/pptp-option.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/pptp/pptp-ifdevice-info.sh

#$1: cmd, ifup or ifdown
#$2: the tunnnel name of the pptp tunnel

local cmd=$1
local ifname=$2
local loop=0
local username=""
local passwd=""
local server_ip=""
local outif=""
local client_path=""
local filepath=${pppox_ppppath}/pid
local result=""

get_pac_param() {
    local section=${1}
    config_get result ${section} tunnelname

	[ $result == $ifname -o "pptp-$result" == $ifname ] && {
		#echo "find $result !" > /dev/console
		username=$(uci get vpn.@pac[$loop].username 2>/dev/null)
		passwd=$(uci get vpn.@pac[$loop].password 2>/dev/null)
		server_ip=$(uci get vpn.@pac[$loop].server_ip 2>/dev/null)
		outif=$(uci get vpn.@pac[$loop].outif 2>/dev/null)
		outif=$(zone_get_effect_ifaces $outif)
		local devices=$(zone_get_device_byif $outif)
		for i in $devices;do
			outif=$i
		done
		#in the slp the path in /tmp/pptp/client/ is the section name
		client_path="/tmp/pptp/client/$section/config"
		#echo "${client_path}" > /dev/console
	}
}

echo "PPTP: now need to $cmd $ifname" > /dev/console
config_load vpn
config_foreach get_pac_param pac

[ x$username == 'x' -a x$passwd == 'x' ] && echo "not found corresponding pptp item" > /dev/console && return 0
case $cmd in
	*ifup)
		#check if it's already up
		#echo $ifname | grep -q '^pt-' || ifname="pt-"$ifname
		#ifconfig | grep -q "^$ifname" && return 0

		local pppfile=$client_path/ppp
		local pid=$(cat $pppfile)
		[ x$pid != 'x' ] && echo "the interface ${ifname} is dialing or up" >/dev/console && return 0

		#start it up
		client_start_pppd $username $passwd $server_ip $outif $client_path
		#echo "client_start_pppd $username $passwd $server_ip $outif $client_path" > /dev/console
	;;
	*ifdown)
		#check it's already running
		local pppfile=$client_path/ppp
		#echo "${pppfile}" > /dev/console
		local pid=$(cat $pppfile)
		[ x$pid == 'x' -a ! -f "$filepath/$pid" ] && echo "no need to down the interface " && return 0
		local mgrfile=$client_path/mgr
		local mgrpid=$(cat $mgrfile)
		kill -9 `cat ${client_path}/mgrfather 2>/dev/null` >/dev/null
		kill -TERM $pid
		kill -TERM $mgrpid
		rm -f $mgrfile
		rm -f $pppfile
		rm -f $filepath/$pid
		rm -f ${client_path}/mgrfather
	;;
esac