#!/bin/sh /etc/rc.common

START=80

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/lldp.pid

start() {
	if [ ! -f ${SERVICE_PID_FILE} ];then
		service_start /usr/sbin/lldp &
	fi
}

stop() {
	service_stop /usr/sbin/lldp
	if [ -f ${SERVICE_PID_FILE} ];then
		rm -f ${SERVICE_PID_FILE}
	fi
}

restart()
{
	echo "restarting lldp service" >/dev/console
	stop
	start
}

reload()
{
	restart
}
