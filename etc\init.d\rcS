#!/bin/sh
# Copyright (C) 2006 OpenWrt.org

boot_done_after_run_scripts() {
	# 对于支持conntrack的系统，在系统初始化完成时，要清掉连接，保证数据流重新选路
	[ -f "/proc/net/nf_conntrack" ] && conntrack -F

	echo 1 > /tmp/boot_done
	#表明所有process已经开始执行
	echo "- boot done -"
}

run_scripts() {
	for i in /etc/rc.d/$1*; do
		[ -x $i ] && $i $2 2>&1
	done > /dev/console
	#启动才需要执行boot_done函数
	[ "$1" = "S" ] && boot_done_after_run_scripts
}

system_config() {
	config_get_bool foreground $1 foreground 0
}

. /lib/functions.sh

config_load system
config_foreach system_config system

if [ "$1" = "S" -a "$foreground" != "1" ]; then
	run_scripts "$1" "$2" &
else
	run_scripts "$1" "$2"
fi
