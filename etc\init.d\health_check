#!/bin/sh /etc/rc.common

START=65

PROG=/usr/sbin/health_check
DEV_PROG=/lib/health_check/health_dev_check.lua

start() {
    mkdir -p /tmp/health_check
    mkdir -p /tmp/nqa
    echo "100 100 100" > /tmp/health_check/dev_score
    lua /lib/health_check/health_check_config.lua
    lua $DEV_PROG &
    ${PROG} &
}

stop()  {
    ps -w | grep ${DEV_PROG} | grep -v "grep" | awk '{print $1}' | xargs kill -s 9 &>/dev/null
    ps -w | grep ${PROG} | grep -v "grep" | awk '{print $1}' | xargs kill -s 9 &>/dev/null
    rm -rf /tmp/health_check
    rm -rf /tmp/nqa
}

restart() {
    stop
    start
}

reload() {
    stop
    start
}