#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     ipsec init
# brief    this file will be called by INIT process.
# author   <PERSON>
# version  1.0.0
# date     08June15
# histry   arg 1.0.0, 08June15, <PERSON>, Create the file. 

START=95

IPSEC_STATE_DIR=/tmp/ipsec/tunnes_state/
IPSEC_HANDLE_XFRFMI_FLG_DIR=/tmp/ipsec/handle_xfrmi/

modifiy_max_child_sas(){
	#set the strongswan.conf according to profile
	local max_child_sas=`uci get profile.@ipsec[0].tunnel_limits`
	#echo "$max_child_sas" > /dev/console
	[ -n "${max_child_sas}" ] && {
		sed -i "/max_child_sas/s/\=.*/= $max_child_sas/" /tmp/.volatiles/etc/strongswan.conf
	}
}

init_ipsec_ifaces_ipset(){
	ipset create IPSEC_IFACES hash:name -exist
	ipset create SDVPN_IFACES hash:name -exist
}

start() {
	# 在VPN启动时提前创建出状态文件集的路径，避免因为多条隧道并发创建目录冲突
	[ ! -e $IPSEC_STATE_DIR ] && mkdir -p $IPSEC_STATE_DIR

	init_ipsec_ifaces_ipset
	modifiy_max_child_sas
	#ipsec_generate_domain_startup.lua仅仅是把/etc/config/ipsec_check_dns文件构造出来，不做任何具体解析工作.
	#解析工作交给ipsec_check_domain.sh来完成.
	lua /usr/lib/lua/ipsec/ipsec_generate_domain_startup.lua
	# 清除所有旧配置条目的隧道接口, 之后创建全部新配置条目的隧道接口, 设备启动阶段放在前台同步执行，确保接口创建完成之后再执行策略路由
	[ ! -e $IPSEC_HANDLE_XFRFMI_FLG_DIR ] && mkdir -p $IPSEC_HANDLE_XFRFMI_FLG_DIR
	lua /usr/lib/lua/ipsec/ipsec_rt_tables_reinit.lua
	lua /usr/lib/lua/ipsec/ipsec_handle_xfrm_interface.lua renew
	#由于ipsec_check_dns并没有任何实际内容，因此ipsec_reload.lua脚本不会创建任何条目，但是不能删除，因为
	#ipsec.conf的必要的头部还是需要的
	#ipsec start
	/lib/ipsec/ipsec_handle_iptables.sh init
	{
		lua /usr/lib/lua/ipsec/ipsec_process_manager.lua
		sleep 3
		/lib/ipsec/ipsec_check_domain.sh
		lua /usr/lib/lua/ipsec/ipsec_reload.lua
		swanctl --load-all >/dev/null 2>&1
	}&
}

stop() {
	rm -rf /var/log/ipsec_updown_histroy
	touch /var/log/ipsec_updown_histroy
	ipsec stop
	# 清除所有旧配置条目的隧道接口
	lua /usr/lib/lua/ipsec/ipsec_handle_xfrm_interface.lua flush
}

restart() {
	# 在VPN启动时提前创建出状态文件集的路径，避免因为多条隧道并发创建目录冲突
	[ ! -e $IPSEC_STATE_DIR ] && mkdir -p $IPSEC_STATE_DIR

	rm -rf /var/log/ipsec_updown_histroy
	touch /var/log/ipsec_updown_histroy

	/lib/ipsec/ipsec_monitor_tunnel.sh&
	/lib/ipsec/ipsec_check_domain_wrap.sh&
	#由于ipsec_generate_domain_startup.lua生成的ipsec_check_dns没有任何实际内容端口ip为xx.xx.xx.xx
	#ipsec_generate_domain_startup.lua仅仅是把/etc/config/ipsec_check_dns文件构造出来，不做任何具体解析工作.
	#解析工作交给ipsec_check_domain.sh来完成.
	lua /usr/lib/lua/ipsec/ipsec_generate_domain_startup.lua

	# 切换成swanctl后，不会自动加载配置
	# 在ipsec charon进程启动starter是一个后台执行程序后，需要等待一定时间进程才能正常
	ipsec restart
	sleep 3
	/lib/ipsec/ipsec_check_domain.sh
	lua /usr/lib/lua/ipsec/ipsec_reload.lua
	# 清除所有旧配置条目的隧道接口，之后创建全部新配置条目的隧道接口
	[ ! -e $IPSEC_HANDLE_XFRFMI_FLG_DIR ] && mkdir -p $IPSEC_HANDLE_XFRFMI_FLG_DIR
	lua /usr/lib/lua/ipsec/ipsec_handle_xfrm_interface.lua renew
	swanctl --load-all >/dev/null 2>&1
}

reload() {
{
flock -x 201

	# 在VPN启动时提前创建出状态文件集的路径，避免因为多条隧道并发创建目录冲突
	[ ! -e $IPSEC_STATE_DIR ] && mkdir -p $IPSEC_STATE_DIR

	init_ipsec_ifaces_ipset

	# 配合 sdwan 模块 在进程没有启动时直接开启拓扑,需要检查并第一次拉起进程,仅在发生进程启动(lua退出码为1)时才需要sleep 3等待
	lua /usr/lib/lua/ipsec/ipsec_process_manager.lua || sleep 3
	# 更新 /etc/config/ipsec_check_dns 域名检查配置
	/lib/ipsec/ipsec_generate_domain.sh renew
	# 更新 /etc/swanctl/swanctl.conf strongswan 运行时隧道配置
	lua /usr/lib/lua/ipsec/ipsec_reload.lua
	# 断开所有的旧隧道，解决响应者模式隧道重新load后不会主动断开导致的旧隧道残留问题
	swanctl --terminate --all >/dev/null 2>&1
	# 需要解决handle_xfrm_interface操作耗时导致strongswan运行时配置清理不及时问题，需要补充strongswan操作, 清除运行时配置
	swanctl --load-conns --flush >/dev/null 2>&1
	# 清除所有旧配置条目的隧道接口，之后创建全部新配置条目的隧道接口
	[ ! -e $IPSEC_HANDLE_XFRFMI_FLG_DIR ] && mkdir -p $IPSEC_HANDLE_XFRFMI_FLG_DIR
	lua /usr/lib/lua/ipsec/ipsec_handle_xfrm_interface.lua renew
	# 加载最新配置，并发起隧道协商
	swanctl --load-all >/dev/null 2>&1
flock -u 201
} 201<>/tmp/ipsec_reload.lock
# 增加文件操作锁解决云端连续下发sd-wan配置后的多次reload操作冲突问题
}
