#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

START=42

USERGROUP_LIBDIR=/lib/usergroup

usergroup() {
	. $USERGROUP_LIBDIR/core.sh
	
	usergroup_$1 $2 $3
}

start() {
	usergroup start

	touch /tmp/usergroup.ready
}

stop() {
	usergroup stop
	rm -rf /tmp/usergroup.ready
}

restart() {
	rm -rf /tmp/usergroup.ready
	usergroup restart
	touch /tmp/usergroup.ready
}

reload() {
	usergroup reload $1 $2
}
