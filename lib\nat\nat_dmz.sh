
#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat_dmz.sh
# brief
# author   <PERSON><PERSON>
# version  1.0.0
# date     23Apr15
# histry   arg 1.0.0, 23Apr15, <PERSON><PERSON>, Create the file.

. /lib/zone/zone_api.sh
. /lib/firewall/fw.sh
. /lib/functions/network.sh

dmz_prerouting_chain="prerouting_rule_dmz"
prerouting_chain="prerouting_rule"
nat_dmz_log="/tmp/.nat/nat_dmz.log"
NAT_ZONE_LAN="LAN"
IPT="iptables -w -t nat "

TCP_SERVICE_LIST="http https pptp ssh"
UDP_SERVICE_LIST="ike l2tp sae"
TCP_SERVICE_LIST_NO_HTTP_NO_HTTPS_NO_SSH="pptp"

nat_service_to_port(){ #$service
	local port http_list https_list ssh_list
	case "$1" in
	http|HTTP|Http)
		port="$(uci_get_state uhttpd main listen_http |cut -d : -f 5)"
		[ -z "$port" ] && port=80
		;;
	https|HTTPS|Https)
		port="$(uci_get_state uhttpd main listen_https |cut -d : -f 5)"
		[ -z "$port" ] && port=443
		;;
	pptp|Pptp|PPTP)
		port=1723  #default port
		;;
	l2tp|L2TP|L2tp)
		port=1701  #default port
		;;
	IKE|ike|Ike)
		port=500  #default port
		;;
	ssh|SSH|Ssh)
		port=$(uci_get administration port SSH)
		[ -z "$port" ] && port=22
		;;
	sae|SAE|Sae)
		port=4500  #default port
		;;
	*)
		return 1
		;;
	esac

	echo "$port"
	return 0
}

nat_dmz_service_ports(){
	local service_list=$@
	local ports port
	for ser in $service_list;do
		port=$(nat_service_to_port $ser)
		ports=${ports:+$ports,}${port:+$port}
	done
	echo $ports
	return 0
}

check_remote_mngt_state(){
	local enabled=`uci get system.$section.enabled`
	if [ "$enabled" = "1" ]; then
		REMOTE_MNGT_ON=1
		local whole_net="`uci get system.$section.ipaddr`"
		if [ "$whole_net" = "0.0.0.0/0" ]; then
			IPSET_USED=0
		fi
	fi
}

nat_rule_dmz_def_rules(){
	local tcp_ports=$(nat_dmz_service_ports $TCP_SERVICE_LIST)
	local tcp_ports_default=$(nat_dmz_service_ports $TCP_SERVICE_LIST_NO_HTTP_NO_HTTPS_NO_SSH)
	local udp_ports=$(nat_dmz_service_ports $UDP_SERVICE_LIST)
	iptables -t nat -w -I ${dmz_prerouting_chain} -p udp -m multiport --dport "$udp_ports" -j RETURN
	iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m multiport --dport "$tcp_ports_default" -j RETURN
	iptables -t nat -w -I ${dmz_prerouting_chain} -p 47 -j RETURN
	iptables -t nat -w -I ${dmz_prerouting_chain} -p 50 -j RETURN
	iptables -t nat -w -I ${dmz_prerouting_chain} -p 51 -j RETURN

	. /lib/functions.sh
	local REMOTE_MNGT_ON=0
	local IPSET_USED=1
	local tcp_ports_rm=""
	config_load system
	config_foreach check_remote_mngt_state remote_rule
	if [ $REMOTE_MNGT_ON -eq 1 ]; then
		tcp_ports_rm=`iptables -w -nvL remote_mngt_chain |grep -w "\* remote_mngt \*" | sed 's/.*multiport dports //g' | awk '{print $1}'`
		if [ $IPSET_USED -eq 1 ]; then
			iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m set --match-set remote_mngt_accept_ip_set src -m multiport --dport "$tcp_ports_rm" -m comment --comment remote_mngt -j RETURN
		else
			iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m multiport --dport "$tcp_ports_rm" -m comment --comment remote_mngt -j RETURN
		fi
		echo "remote mngt is on, DMZ reject ralative tcp port: $tcp_ports_rm" > /dev/console
	fi

	local iface_in_rm=""
	local mngt_iface=""
	mngt_ifaces=`iptables -w -nvL remote_mngt_chain | grep -w ACCEPT | grep -v "\* remote_mngt \*" | awk '{print $6}'`
	for mngt_iface in $mngt_ifaces; do
		if [ "$iface_in_rm" = "" ]; then
			tcp_ports_rm=`iptables -w -nvL remote_mngt_chain | grep -w "$mngt_iface" | awk '{print $NF}'`
			iface_in_rm="$mngt_iface"
		else
			iface_in_rm="$mngt_iface,$iface_in_rm"
		fi
	done
	if [ "$iface_in_rm" != "" ]; then
		iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m multinetdev --in "$iface_in_rm" -m multiport --dport "$tcp_ports_rm" -j RETURN
		echo "mngt ifaces exist, DMZ reject relative tcp port: $tcp_ports_rm" > /dev/console
	fi
}

nat_rule_dmz_def_rules_del(){

	local tcp_ports=$(nat_dmz_service_ports $TCP_SERVICE_LIST)
	local udp_ports=$(nat_dmz_service_ports $UDP_SERVICE_LIST)
	local tcp_ports_default=$(nat_dmz_service_ports $TCP_SERVICE_LIST_NO_HTTP_NO_HTTPS_NO_SSH)

	iptables -t nat -w -D ${dmz_prerouting_chain} -p udp -m multiport --dport "$udp_ports" -j RETURN
	iptables -t nat -w -D ${dmz_prerouting_chain} -p tcp -m multiport --dport "$tcp_ports_default" -j RETURN
	iptables -t nat -w -D ${dmz_prerouting_chain} -p 47 -j RETURN
	iptables -t nat -w -D ${dmz_prerouting_chain} -p 50 -j RETURN
	iptables -t nat -w -D ${dmz_prerouting_chain} -p 51 -j RETURN

	local ln=`iptables -t nat -w -nvL ${dmz_prerouting_chain} --line| grep -w "\* remote_mngt \*" | awk '{print $1}'`
	[ "$ln" != "" ] && iptables -t nat -w -D ${dmz_prerouting_chain} $ln
	ln=`iptables -t nat -w -nvL ${dmz_prerouting_chain} --line| grep -w multinetdev | awk '{print $1}'`
	[ "$ln" != "" ] && iptables -t nat -w -D ${dmz_prerouting_chain} $ln
}

nat_rule_dmz_def_rules_update_tcp_ports(){
	sleep 3

	local line_number=$(iptables -t nat -w -nvL ${dmz_prerouting_chain} --line |grep "tcp.*multiport dports" |awk '{print $1}')
	if [ -z "$line_number" ]; then
		echo "DMZ is off, do nothing." >/dev/console
	else
		# delete exist relative rule first

		local ln=`iptables -t nat -w -nvL ${dmz_prerouting_chain} --line| grep -w "\* remote_mngt \*" | awk '{print $1}'`
		[ "$ln" != "" ] && iptables -t nat -w -D ${dmz_prerouting_chain} $ln
		ln=`iptables -t nat -w -nvL ${dmz_prerouting_chain} --line| grep -w multinetdev | awk '{print $1}'`
		[ "$ln" != "" ] && iptables -t nat -w -D ${dmz_prerouting_chain} $ln

		# add new relative rule then

		. /lib/functions.sh
		local REMOTE_MNGT_ON=0
		local IPSET_USED=1
		local tcp_ports_rm=""
		config_load system
		config_foreach check_remote_mngt_state remote_rule
		if [ $REMOTE_MNGT_ON -eq 1 ]; then
			tcp_ports_rm=`iptables -w -nvL remote_mngt_chain |grep -w "\* remote_mngt \*" | sed 's/.*multiport dports //g' | awk '{print $1}'`
			if [ $IPSET_USED -eq 1 ]; then
				iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m set --match-set remote_mngt_accept_ip_set src -m multiport --dport "$tcp_ports_rm" -m comment --comment remote_mngt -j RETURN
			else
				iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m multiport --dport "$tcp_ports_rm" -m comment --comment remote_mngt -j RETURN
			fi
			echo "remote mngt is on, DMZ reject ralative tcp port: $tcp_ports_rm" > /dev/console
		fi

		local iface_in_rm=""
		local mngt_iface=""
		mngt_ifaces=`iptables -w -nvL remote_mngt_chain | grep -w ACCEPT | grep -v "\* remote_mngt \*" | awk '{print $6}'`
		for mngt_iface in $mngt_ifaces; do
			if [ "$iface_in_rm" = "" ]; then
				tcp_ports_rm=`iptables -w -nvL remote_mngt_chain | grep -w "$mngt_iface" | awk '{print $NF}'`
				iface_in_rm="$mngt_iface"
			else
				iface_in_rm="$mngt_iface,$iface_in_rm"
			fi
		done
		if [ "$iface_in_rm" != "" ]; then
			iptables -t nat -w -I ${dmz_prerouting_chain} -p tcp -m multinetdev --in "$iface_in_rm" -m multiport --dport "$tcp_ports_rm" -j RETURN
			echo "mngt ifaces exist, DMZ reject relative tcp port: $tcp_ports_rm" > /dev/console
		fi

	fi
}

dmz_nat_chain_add()
{
	local prerouting_chain_main=`iptables -w -t nat -nvL | grep -w ${prerouting_chain}`
	local prerouting_chain_dmz=`iptables -w -t nat -nvL | grep -w ${dmz_prerouting_chain}`

	if [ -z "$prerouting_chain_main" ];then
		$IPT -N ${prerouting_chain}
		$IPT -I PREROUTING -j ${prerouting_chain}
		echo "$IPT -N ${prerouting_chain}" > /dev/console
		echo "$IPT -I PREROUTING -j ${prerouting_chain}" > /dev/console
	fi

	if [ -z "$prerouting_chain_dmz" ];then
		$IPT -N ${dmz_prerouting_chain}
		$IPT -A ${prerouting_chain} -j ${dmz_prerouting_chain}
		echo "$IPT -N ${dmz_prerouting_chain}" > /dev/console
		echo "$IPT -A ${prerouting_chain} -j ${dmz_prerouting_chain}" > /dev/console
		nat_rule_dmz_def_rules
	fi

}

dmz_nat_chain_del()
{

	local prerouting_chain_dmz_rule_num=`iptables -t nat -S ${dmz_prerouting_chain} | wc -l`

	if [ -z "$prerouting_chain_dmz_rule_num" -o $prerouting_chain_dmz_rule_num -eq 6 ];then
		nat_rule_dmz_def_rules_del
		$IPT -D  ${prerouting_chain} -j ${dmz_prerouting_chain}
		echo "$IPT -D  ${prerouting_chain} -j ${dmz_prerouting_chain}" > /dev/console
		$IPT -X ${dmz_prerouting_chain}
		echo "$IPT -X ${dmz_prerouting_chain} " > /dev/console
	fi

	local prerouting_chain_rule_num=`iptables -t nat -S ${prerouting_chain} | wc -l`
	if [ -z "$prerouting_chain_rule_num" -o $prerouting_chain_rule_num -eq 1 ];then
		$IPT -D  PREROUTING -j ${prerouting_chain}
		echo "$IPT -D  PREROUTING -j ${prerouting_chain}" > /dev/console
		$IPT -X ${prerouting_chain}
		echo "$IPT -X ${prerouting_chain} " > /dev/console
	fi
}


get_dmz_rule_dev_by_name()
{
	local rule_dmz_sym=$1
	local dmz_rule_dev=`$IPT -S ${dmz_prerouting_chain} | grep -w "\/\* ${rule_dmz_sym} \*\/"  | awk '{print $6}'`
	#echo "rule_dmz_sym is $rule_dmz_sym,dmz_rule_dev is $dmz_rule_dev " > /dev/console
	echo $dmz_rule_dev
}

get_dmz_rule_ipaddr_by_name()
{
	local rule_dmz_sym=$1
	local dmz_rule_ip_subnet=`$IPT -S ${dmz_prerouting_chain} | grep -w "\/\* ${rule_dmz_sym} \*\/" | awk '{print $4}'`
	local dmz_rule_ipaddr=${dmz_rule_ip_subnet%/*}
	#echo "rule_dmz_sym is $rule_dmz_sym,dmz_rule_ipaddr is $dmz_rule_ipaddr " > /dev/console
	echo $dmz_rule_ipaddr
}

del_dmz_rule()
{
	local rule_dmz_sym=$1
	if [ -z "${rule_dmz_sym}" ] ; then
		echo "err rule_dmz_sym"
		return
	fi

	local dmz_rule_num=`$IPT -nv --line-number -L ${dmz_prerouting_chain} | grep -w "\/\* ${rule_dmz_sym} \*\/" | cut -d " " -f 1`

	if [ -n "$dmz_rule_num" ];then
		$IPT -D ${dmz_prerouting_chain} $dmz_rule_num
	fi

	dmz_nat_chain_del
}

add_dmz_rule()
{
	local op=$1
	local rule_dmz_name=$2
	local rule_dmz_interface=$3
	local rule_dmz_ipaddr=$4
	local rule_dmz_enable=$5

	#echo " op=$1 rule_dmz_name=$2 rule_dmz_interface=$3 rule_dmz_ipaddr=$4 rule_dmz_enable=$5" > /dev/console

	[ -z "$rule_dmz_interface" ] && {
		echo "interface is not set " >> /dev/console
		return
	}

	[ -z "$rule_dmz_ipaddr" -o "$rule_dmz_ipaddr" == "0.0.0.0" ] && {
		echo "Host ipaddr is not set" >> /dev/console
		return
	}
	if [ "$rule_dmz_enable" == "on" ]; then
		ifaces=$(zone_get_effect_ifaces "${rule_dmz_interface}")
		dmz_nat_chain_add
		for iface in $ifaces;do
			#network_get_device device "$iface"
			local device=$(zone_get_effect_devices "${iface}")
			[ -n "$iface" -a -n "$device" ] && {
				#if_ip=$(uci_get_state nat env ${iface}_ip)
				network_get_ipaddr if_ip ${iface}
				$IPT -A ${dmz_prerouting_chain} -i $device -d ${if_ip} -j DNAT --to-destination ${rule_dmz_ipaddr} -m comment --comment ${rule_dmz_name}
				echo "$IPT -A ${dmz_prerouting_chain} -i $device -d ${if_ip} -j DNAT --to-destination ${rule_dmz_ipaddr} -m comment --comment ${rule_dmz_name}" > /dev/console
			}
		done

	fi
}

dmz_rule_operation(){
	local op=$1
	local rule_dmz_name=$2
	local rule_dmz_interface=$3

flock -x 90
	case $op in
	add)
		add_dmz_rule $@
	;;
	del)
		del_dmz_rule ${rule_dmz_name}
	;;
	update)
		del_dmz_rule ${rule_dmz_name}
		add_dmz_rule $@
	;;
	esac
flock -u 90
} 90>/tmp/nat_dmz.lock

nat_load_dmz() {
	config_get name $1 name
	config_get interface $1 if
	config_get ipaddr $1 ip
	config_get enable $1 enable
	local ops=$2
	#echo "dmz_rule_operation $ops $name $interface $ipaddr $enable" > /dev/console
	dmz_rule_operation $ops $name $interface $ipaddr $enable
}

nat_rule_dmz_operation() {
	fw flush 4 n $dmz_prerouting_chain
	nat_rule_dmz_def_rules

	config_load nat
	config_foreach nat_load_dmz rule_dmz add
}

update_dmz_rule(){
	config_get interface $1 if
	config_get enable $1 enable
	config_get name $1 name

	INTERFACE=$2

	if [ -n "${interface}" -a "${enable}" == "on" -a "${interface}" == "${INTERFACE}" ];then
		local rule_dmz_dev=$(get_dmz_rule_dev_by_name "${name}")
		local rule_dmz_wan_ip=$(get_dmz_rule_ipaddr_by_name "${name}")
		local ifaces=$(zone_get_effect_ifaces "${interface}")
		for iface in $ifaces;do
			#network_get_device device "$iface"
			local device=$(zone_get_effect_devices "${iface}")
			[ -n "$iface" -a "$device" == "$rule_dmz_dev" ] && {
				network_get_ipaddr if_ip ${iface}
				[ -n "$if_ip" -a "$if_ip" != "$rule_dmz_wan_ip" ] && {
					echo "if_ip is ${if_ip},rule_dmz_wan_ip is ${rule_dmz_wan_ip}, need to set iptables rules " > /dev/console
					nat_load_dmz $1 update
				}
			}

			[ -n "$iface" -a "$device" != "$rule_dmz_dev" ] && {
				echo "rule_dmz_dev is ${rule_dmz_dev},device is ${device}, need to set iptables rules " > /dev/console
				nat_load_dmz $1 update
			}
		done

	fi
}

nat_rule_dmz_hotplug(){
	config_load nat
	config_foreach update_dmz_rule rule_dmz $1
}
