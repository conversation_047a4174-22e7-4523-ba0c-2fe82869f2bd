{
	"show":
	{
		"___help":"Display system information",
		"history":
		{
			"___help":"Display command history",
			"___priv":1,
			"___mode":1
		},
		"system-info":
		{
			"___help":"Display System information",
			"___priv":2,
			"___mode":1
		},
		"user":
		{
			"___help":"Display WEB's user name",
			"account-list":
			{
				"___help":"Display WEB's user name",
				"___priv":2,
				"___mode":1
			}
		},
		"ip":
		{
			"___help":"Display IP information",
			"http":
			{
				"___help":"HTTP(S) server information",
				"configuration":
				{
					"___help":"HTTP server information",
					"___priv":2,
					"___mode":1
				},
				"secure-server":
				{
					"___help":"HTTPS server information",
					"___priv":2,
					"___mode":1
				}
			}
		}
	},
	"clear":
	{
		"___help":"Clear statistic",
		"history":
		{
			"___help":"Clear previously run command history",
			"___priv":2,
			"___mode":1
		}
	},
	"debug":
	{
		"___help":"Enter debug mode",
		"___priv":2,
		"___mode":2
	},
	"reboot":
	{
		"___help":"Reboot device",
		"___priv":2,
		"___mode":2,
		"___confirm":1,
		"___confirm_str":" This will reboot device. Continue? ",
		"___cancel_str":" System reboot is cancelled."
	},
	"reset":
	{
		"___help":"Reset device",
		"___priv":2,
		"___mode":2,
		"___confirm":1,
		"___confirm_str":" This will reset device. Continue? ",
		"___cancel_str":" System reset is cancelled."
	},
	"ping":
	{
		"___help":"Ping command,ex:ping ip_addr [ -n count ] [ -l length ]",
		"___priv":1,
		"___mode":2,
		"___para":[
			["addr","",1,2,"","Ping destination address or hostname"],
			["num","-n",0,1,"1-50","Number of echo requests to send"],
			["size","-l",0,1,"4-1472","Send buffer size"]
		]
	},
	"tracert":
	{
		"___help":"Tracert command,ex:tracert ip_addr [maxHops]",
		"___priv":1,
		"___mode":2,
		"___para":[
			["addr","",1,2,"","Tracert destination address or hostname"],
			["hops","",1,1,"1-30","Max hops"],
		]
	},
	"enable":
	{
		"___help":"Configure enable password",
		"password":
		{
			"___help":"Configure enable password",
			"___priv":2,
			"___mode":3,
			"___para":[
				["password","",1,2,"^[!-~]{1,31}$","Unencrypted user password,whose length should be 1-31"]
			]
		},
		"secret":
		{
			"___help":"Configure encrypted enable password",
			"___priv":2,
			"___mode":3,
			"___para":[
				["password","",1,2,"^[!-~]{1,31}$","encrypted user password,whose length should be 1-31"]
			]
		}
	},
	"no":
	{
		"___help":"Undo configure",
		"enable":
		{
			"___help":"Clear enable password",
			"password":
			{
				"___help":"Clear enable password",
				"___priv":2,
				"___mode":3
			},
			"secret":
			{
				"___help":"Clear enable encrypted password",
				"___priv":2,
				"___mode":3
			}
		}
	}
}