#!/bin/sh
: '
 Copyright (c) 2017, The Linux Foundation. All rights reserved.

 Permission to use, copy, modify, and/or distribute this software for any
 purpose with or without fee is hereby granted, provided that the above
 copyright notice and this permission notice appear in all copies.

 THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
'

SERVER=$(fw_printenv serverip | cut -c10-24);

if [ ! -n "$SERVER" ]; then
	printf "%s\n" "Wrong configuaration SERVER = $SERVER" > /dev/console
	exit 0
fi

if [ -e /dev/$DEVICENAME ] && [ "$ACTION" = add ]; then
	printf "%s\n" "Collecting $DEVICENAME dump in $SERVER" > /dev/console
	cd /dev
	$(tftp -l $DEVICENAME -p $SERVER 2>&1)
	if [ $? -eq 0 ]; then
		printf "%s\n" "$DEVICENAME dump collected in $SERVER" \
								> /dev/console
	else
		printf "%s\n" "$DEVICENAME dump collection failed in $SERVER" \
								> /dev/console
	fi
	cd -
fi

