#!/usr/bin/lua

local projection_util = require("luci.model.projection_util")
local cjson = require("cjson")
local dbg = require("luci.torchlight.debug")

function get_tv_ip_by_phone_ip(phone_ip)
    local tv_ip = projection_util.get_tv_ip_by_phone_ip(phone_ip)
    if nil ~= tv_ip then
        print(tv_ip)
    end
end

function get_phone_ip_by_tv_address(tv_address)
    local phone_ip = projection_util.get_phone_ip_by_tv_address(tv_address)
    if nil ~= phone_ip then
        print(phone_ip)
    end
end

function del_bind_rule_by_phone_ip(phone_ip)
    projection_util.del_bind_rule_by_phone_ip(phone_ip)
end

function del_bind_rule_by_tv_address(tv_address)
    projection_util.del_bind_rule_by_tv_address(tv_address)
end

function del_all_bind_rule()
    projection_util.del_all_bind_rule()
end

function get_all_tv_to_phone()
    local tbl = projection_util.get_all_tv_to_phone()
    if nil ~= tbl then
        print(cjson.encode(tbl))
    end
end

local cmds = {
    ['get_tv_ip_by_phone_ip'] = get_tv_ip_by_phone_ip,
    ['get_phone_ip_by_tv_address'] = get_phone_ip_by_tv_address,
    ['del_bind_rule_by_phone_ip'] = del_bind_rule_by_phone_ip,
    ['del_bind_rule_by_tv_address'] = del_bind_rule_by_tv_address,
    ['del_all_bind_rule'] = del_all_bind_rule,
    ['get_all_tv_to_phone'] = get_all_tv_to_phone
}

local func = cmds[arg[1]]

if func then
    if arg[1] and arg[2] then
        dbg("==> CALL "..arg[1].." "..arg[2])
        func(arg[2])
    elseif arg[1] then
        dbg("==> CALL "..arg[1])
        func()
    else
        dbg("==> CALL nil")
    end
end