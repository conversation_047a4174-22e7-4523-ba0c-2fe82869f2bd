#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"


local db_wportal = {
	name = "portal.db",
	tables = {
		{
			name = "jumpPageTable",
			columns = {
				{
					name = "jump_page_mode",
					stype = "VARCHAR(20)",
					db_attr = {},
					default_value = nil
				}, {
					name = "jump_page_name",
					stype = "VARCHAR(100)",
					db_attr = {"db_index", "db_not_null", "db_unique"},
					default_value = nil
				}, {
					name = "reference",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "note",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil				
				}, {
					name = "portalPageId",
					stype = "VARCHAR(20)",
					db_attr = {},
					default_value = nil				
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			}, -- end columns
			datas = {

			} -- end datas
		},
		{
			name = "portalTable",
			columns = {
				{
					name = "portal_id",
					stype = "VARCHAR(100)",
					db_attr = {"db_index", "db_not_null"},
					default_value = nil
				}, {
					name = "rule_name",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "jump_page_name",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "default_page_type",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "vlan_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "serv_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "ssid_id",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "interface",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "auth_type",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "success_url",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "fail_url",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "fail_id",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "note",
					stype = "VARCHAR(200)",
					db_attr = {},
					default_value = nil
				}, {
					name = "is_cloud",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_auth_server_type",
					stype = "VARCHAR(20)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_auth_server_group",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_nosense_auth",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_remind_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_remind_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_remind_interval",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_remind_type",
					stype = "VARCHAR(20)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_remind_page_title",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "web_remind_page_content",
					stype = "VARCHAR(150)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_ssid",
					stype = "VARCHAR(128)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_shop_id",
					stype = "VARCHAR(32)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_app_id",
					stype = "VARCHAR(32)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_secret_key",
					stype = "VARCHAR(32)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_force_concern",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_qrcode",
					stype = "VARCHAR(128)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_qrcode_pic_url",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_app_secret",
					stype = "VARCHAR(64)",
					db_attr = {},
					default_value = nil
				}, {
					name = "wechat_token_server",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "onekey_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "onekey_free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_auth_expiry",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_channel_type",
					stype = "VARCHAR(50)",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "sms_http_url",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_http_mode",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_http_code_type",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_http_template",
					stype = "VARCHAR(500)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_username",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_password",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_template_id",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_signname",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "sms_app_id",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "remote_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "remote_url",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "remote_auth_server_type",
					stype = "VARCHAR(20)",
					db_attr = {},
					default_value = nil
				}, {
					name = "remote_auth_server_group",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "remote_free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "dossen_auth_devname",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "dossen_auth_devid",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "dossen_auth_key",
					stype = "VARCHAR(64)",
					db_attr = {},
					default_value = nil
				}, {
					name = "remote_nosense_auth",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_url",
					stype = "VARCHAR(256)",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_auth_server_type",
					stype = "VARCHAR(20)",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_auth_server_group",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_nosense_auth",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "cmcc_ac_name",
					stype = "VARCHAR(150)",
					db_attr = {},
					default_value = nil
				}, {
					name = "escape_enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			}, -- end columns
			datas = {

			} -- end datas
		}
	} -- end tables
}

local db_auth_user = {
	name = "auth_user.db",
	tables = {
		{
			name = "authUserTable",
			columns = {
				{
					name = "user_type",
					stype = "VARCHAR(10)",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "username",
					stype = "VARCHAR(100)",
					db_attr = {"db_index", "db_unique", "db_not_null"},
					default_value = nil
				}, {
					name = "password",
					stype = "VARCHAR(100)",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "expire_time",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "allow_auth_time",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "free_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "mac_bind_type",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "ip_bind_type",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "mac",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "bind_ip",
					stype = "VARCHAR(50)",
					db_attr = {},
					default_value = nil
				}, {
					name = "allow_user",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "up_limit",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "down_limit",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "name",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "telephone",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "note",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "enable",
					stype = "VARCHAR(10)",
					db_attr = {},
					default_value = nil
				}, {
					name = "auth_user_id",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			}, -- end columns
			datas = {

			} -- end datas			
		}
	} -- end tables
}

local db_auth_server = {
	name = "auth_server.db",
	tables = {
		{
			name = "radiusTable",
			columns = {
				{
					name = "server_name",
					stype = "VARCHAR(100)",
					db_attr = {"db_index", "db_not_null", "db_unique"},
					default_value = nil
				}, {
					name = "server_addr",
					stype = "VARCHAR(256)",
					db_attr = {"db_not_null"},
					default_value = nil	
				}, {
					name = "auth_port",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil	
				}, {
					name = "account_port",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil	
				}, {
					name = "share_key",
					stype = "VARCHAR(120)",
					db_attr = {},
					default_value = nil	
				}, {
					name = "retry_count",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil	
				}, {
					name = "retry_interval",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil	
				},{
					name = "nas_identifier",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = ""
				},
				{
					name = "nas_ip",
					stype = "VARCHAR(32)",
					db_attr = {},
					default_value = nil	
				}, {
					name = "radius_auth_type",
					stype = "VARCHAR(20)",
					db_attr = {"db_not_null"},
					default_value = nil	
				}, {
					name = "reference",
					stype = "INTEGER",
					db_attr = {"db_not_null"},
					default_value = nil	
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil	
				}
			}, -- end columns
			datas = {

			} -- end datas
		},
		{
			name = "authServerGroupTable",
			columns = {
				{
					name = "group_name",
					stype = "VARCHAR(100)",
					db_attr = {"db_index", "db_not_null", "db_unique"},
					default_value = nil
				}, {
					name = "server_type",
					stype = "VARCHAR(20)",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "main_server",
					stype = "VARCHAR(100)",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "backup_server",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "recovery_time",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "note",
					stype = "VARCHAR(100)",
					db_attr = {},
					default_value = nil
				}, {
					name = "reference",
					stype = "INTEGER",
					db_attr = {"db_not_null"},
					default_value = nil
				}, {
					name = "entry_id",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			}, -- end columns
			datas = {

			} -- end datas
		}
	} -- end tables
}

--uci config_sync

--database config_sync
cfgsync.config_sync(db_wportal, "database")
cfgsync.config_sync(db_auth_user, "database")
cfgsync.config_sync(db_auth_server, "database")