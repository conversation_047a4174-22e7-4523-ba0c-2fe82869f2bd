#!/bin/sh

cmd=$1
#rule_name is slave_iface
rule_name="$2"

shift
case $cmd in
    *ACTIVE)         
		(flock -x 66 
        /usr/sbin/line_backup -t timer -s $rule_name -o "on"       
         flock -u 66
        ) 66<>/tmp/.line_backup_time_lock   
    ;;
    
    *EXPIRE)        
		(flock -x 66
        /usr/sbin/line_backup -t timer -s $rule_name -o "off" 
         flock -u 66
        ) 66<>/tmp/.line_backup_time_lock   
    ;;
    
    *RESET)
        echo "reset evnet!"
    ;;   
      
esac 