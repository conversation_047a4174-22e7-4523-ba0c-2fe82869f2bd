#!/usr/bin/lua

local dbg     = require "luci.torchlight.debug"
local uci     = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"

local conf_dir = "/tmp/etc/uc_conf"
local uci_r = uci.cursor()

local function change_enable(old_val)
    if "enable" == old_val then
        return "on"
    else
        return "off"
    end
end

local function change_action(old_val)
    if "drop" == old_val then
        return "block"
    elseif "accept" == old_val then
        return "allow"
    else
        return old_val
    end
end

local app_control_change_table = {
    ["http_post_method"]            = {["new_item"]="http_post_action",               ["func"]=change_action},
    ["has_http_post_threshold"]     = {["new_item"]="http_post_threshold_enable",     ["func"]=change_enable},
    ["http_post_alert"]             = {["new_item"]="http_post_alert_threshold"},
    ["http_post_forbidden"]         = {["new_item"]="http_post_forbidden_threshold"},
    ["http_web_method"]             = {["new_item"]="http_web_action",                ["func"]=change_action},
    ["http_proxy_method"]           = {["new_item"]="http_proxy_action",              ["func"]=change_action},
    ["http_upload_method"]          = {["new_item"]="http_upload_action",             ["func"]=change_action},
    ["has_http_upload_threshold"]   = {["new_item"]="http_upload_threshold_enable",   ["func"]=change_enable},
    ["http_upload_alert"]           = {["new_item"]="http_upload_alert_threshold"},
    ["http_upload_forbidden"]       = {["new_item"]="http_upload_forbidden_threshold"},
    ["http_download_method"]        = {["new_item"]="http_download_action",           ["func"]=change_action},
    ["has_http_download_threshold"] = {["new_item"]="http_download_threshold_enable", ["func"]=change_enable},
    ["http_download_alert"]         = {["new_item"]="http_download_alert_threshold"},
    ["http_download_forbidden"]     = {["new_item"]="http_download_forbidden_threshold"},
    ["ftp_upload_method"]           = {["new_item"]="ftp_upload_action",              ["func"]=change_action},
    ["has_ftp_upload_threshold"]    = {["new_item"]="ftp_upload_threshold_enable",    ["func"]=change_enable},
    ["ftp_upload_alert"]            = {["new_item"]="ftp_upload_alert_threshold"},
    ["ftp_upload_forbidden"]        = {["new_item"]="ftp_upload_forbidden_threshold"},
    ["ftp_download_method"]         = {["new_item"]="ftp_download_action",            ["func"]=change_action},
    ["has_ftp_download_threshold"]  = {["new_item"]="ftp_download_threshold_enable",  ["func"]=change_enable},
    ["ftp_download_alert"]          = {["new_item"]="ftp_download_alert_threshold"},
    ["ftp_download_forbidden"]      = {["new_item"]="ftp_download_forbidden_threshold"},
    ["ftp_delete_method"]           = {["new_item"]="ftp_delete_action",              ["func"]=change_action},
    ["qq_login_method"]             = {["new_item"]="qq_login_action",                ["func"]=change_action},
    ["has_qq_black_list"]           = {["new_item"]="qq_black_list_enable",           ["func"]=change_enable},
    ["has_qq_white_list"]           = {["new_item"]="qq_white_list_enable",           ["func"]=change_enable}
}

local email_filter_change_table = {
    ["smtp_method"]                     = {["new_item"]="smtp_action",                      ["func"]=change_action},
    ["pop3_method"]                     = {["new_item"]="pop3_action",                      ["func"]=change_action},
    ["imap_method"]                     = {["new_item"]="imap_action",                      ["func"]=change_action},
    ["has_sender_black_list_smtp"]      = {["new_item"]="smtp_sender_black_list_enable",    ["func"]=change_enable},
    ["has_sender_white_list_smtp"]      = {["new_item"]="smtp_sender_white_list_enable",    ["func"]=change_enable},
    ["has_receiver_black_list_smtp"]    = {["new_item"]="smtp_receiver_black_list_enable",  ["func"]=change_enable},
    ["has_receiver_white_list_smtp"]    = {["new_item"]="smtp_receiver_white_list_enable",  ["func"]=change_enable},
    ["sender_black_list_smtp"]          = {["new_item"]="smtp_sender_black_list"},
    ["sender_white_list_smtp"]          = {["new_item"]="smtp_sender_white_list"},
    ["receiver_black_list_smtp"]        = {["new_item"]="smtp_receiver_black_list"},
    ["receiver_white_list_smtp"]        = {["new_item"]="smtp_receiver_white_list"},
    ["has_sender_black_list_pop3"]      = {["new_item"]="pop3_sender_black_list_enable",    ["func"]=change_enable},
    ["has_sender_white_list_pop3"]      = {["new_item"]="pop3_sender_white_list_enable",    ["func"]=change_enable},
    ["has_receiver_black_list_pop3"]    = {["new_item"]="pop3_receiver_black_list_enable",  ["func"]=change_enable},
    ["has_receiver_white_list_pop3"]    = {["new_item"]="pop3_receiver_white_list_enable",  ["func"]=change_enable},
    ["sender_black_list_pop3"]          = {["new_item"]="pop3_sender_black_list"},
    ["sender_white_list_pop3"]          = {["new_item"]="pop3_sender_white_list"},
    ["receiver_black_list_pop3"]        = {["new_item"]="pop3_receiver_black_list"},
    ["receiver_white_list_pop3"]        = {["new_item"]="pop3_receiver_white_list"},
    ["has_sender_black_list_imap"]      = {["new_item"]="imap_sender_black_list_enable",    ["func"]=change_enable},
    ["has_sender_white_list_imap"]      = {["new_item"]="imap_sender_white_list_enable",    ["func"]=change_enable},
    ["has_receiver_black_list_imap"]    = {["new_item"]="imap_receiver_black_list_enable",  ["func"]=change_enable},
    ["has_receiver_white_list_imap"]    = {["new_item"]="imap_receiver_white_list_enable",  ["func"]=change_enable},
    ["sender_black_list_imap"]          = {["new_item"]="imap_sender_black_list"},
    ["sender_white_list_imap"]          = {["new_item"]="imap_sender_white_list"},
    ["receiver_black_list_imap"]        = {["new_item"]="imap_receiver_black_list"},
    ["receiver_white_list_imap"]        = {["new_item"]="imap_receiver_white_list"}
}

local file_content_filter_change_table = {
    ["method"] = {["new_item"]="action", ["func"]=change_action}
}

local url_filter_change_table = {
    ["allow"] = {["new_item"]="action", ["func"]=change_action}
}

-- 根据映射表进行字段修改和替换
local function conf_sync_by_change_table(conf, stype, change_table)
    local is_changed = false

    uci_r.foreach(conf, stype, function(conf_section)
        for old_item, change_list in pairs(change_table) do
            if nil ~= conf_section[old_item] then
                local item_name = change_list.new_item or old_item
                local new_value
                -- 如果是需要替换的值，那么使用新的值进行替换
                if nil ~= change_list.func then
                    new_value = change_list.func(conf_section[old_item])
                else
                    new_value = conf_section[old_item]
                end
                -- 如果字段名或取值有变化，则修改uci
                if item_name ~= old_item or new_value ~= conf_section[old_item] then
                    uci_r:delete(conf, conf_section[".name"], old_item)
                    uci_r:set(conf, conf_section[".name"], item_name, new_value)
                    is_changed = true
                end
            end
        end
    end)

    return is_changed
end

local function sec_content_sync()
    uci_r:load(conf_dir)
    local is_changed = false

    -- app_control_filter_conf
    if true == conf_sync_by_change_table("sec_content_conf", "app_control_filter_conf", app_control_change_table) then
        is_changed = true
    end

    -- email_filter_conf
    if true == conf_sync_by_change_table("sec_content_conf", "email_filter_conf", email_filter_change_table) then
        is_changed = true
    end

    -- file_content_filter_conf
    if true == conf_sync_by_change_table("sec_content_conf", "file_content_filter_conf", file_content_filter_change_table) then
        is_changed = true
    end

    -- url_filter_conf
    if true == conf_sync_by_change_table("sec_content_conf", "url_filter_conf", url_filter_change_table) then
        is_changed = true
    end

    if is_changed == true then
        uci_r:commit("sec_content_conf")
        cfgsync.set_config_changed()
    end
end

sec_content_sync()
