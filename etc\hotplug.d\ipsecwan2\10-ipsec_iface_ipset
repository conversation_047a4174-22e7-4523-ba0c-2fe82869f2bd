#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.
. /lib/zone/zone_api.sh

[ ! -f /tmp/iface_setup.ready ] && {
	exit 0
}

# ipset list ${INTERFACE}_FACES 2>dev/null || exit 0

{
flock -x 210
case "$ACTION" in
	ifup)
		local iface=$IFACE
		local iface_prefix=${iface%%_*}
		local dev=$XFRMI_IF
		if [ -n "$iface" ]; then
			ipset create ${iface}_IFACES  hash:name -exist
			if [ -n "$dev" ]; then
				ipset add ${iface}_IFACES $dev -exist

				ipset add IPSEC_IFACES ${dev} -exist

				if [ "$iface_prefix" == "sdwan" ]; then
					# 需要加入到 SDVPN_IFACES 里面
					ipset add SDVPN_IFACES $dev -exist
				fi
			fi
		fi
	;;
	ifdown)
		local iface=$IFACE
		local iface_prefix=${iface%%_*}
		local dev=$XFRMI_IF
		#此处接口集合不能被删除,可能存在被引用情况,故采用FLUSH操作
		#当存在被引用时,destroy后,可能会导致iptables的kernel panic
		#删除IPSec 接口的时候会删除
		if [ -n "$iface" ]; then
			ipset flush ${iface}_IFACES 2>/dev/null
		fi
		if [ -n "$dev" ]; then
			ipset del IPSEC_IFACES ${dev} -exist

			if [ "$iface_prefix" == "sdwan" ]; then
				# 需要加入到 SDVPN_IFACES 里面
				ipset del SDVPN_IFACES $dev -exist
			fi
		fi
	;;
	ifupdate)
		# do nothing
	;;
esac
flock -u 210
} 210<>/tmp/ipsec_210.lock
