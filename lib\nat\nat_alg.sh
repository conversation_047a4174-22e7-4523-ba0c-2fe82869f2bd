#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat_alg.sh
# brief    
# author   <PERSON> chen
# version  1.0.0
# date     23Apr15
# histry   arg 1.0.0, 23Apr15, <PERSON> chen, Create the file.
. /lib/nat/config.sh
. /lib/functions.sh
. /lib/zone/zone_api.sh

nat__do_alg() {
        local alg=$1
        local enable=$2
        local setup_mods=$3

        local alg_mod
        for m in $setup_mods; do
            list_contains alg_mod $m && continue
            [ "$m" == "nf_nat_${alg}" ] && append alg_mod $m
            [ "$m" == "nf_conntrack_${alg}" ] && append alg_mod $m
        done
        
        local upper_alg=$(echo "$alg"|tr '[a-z]' '[A-Z]')
        [ "$enable" == "on" ] && {
            for mod in nf_conntrack_${alg} nf_nat_${alg}; do
				echo "insmod ${mod}" > /dev/console
                list_contains alg_mod $mod && continue
                insmod ${mod}
            done
            return
        }
        
        for mod in nf_nat_${alg} nf_conntrack_${alg}; do
            list_contains alg_mod $mod && {
				echo "rmmod ${mod}" > /dev/console
                rmmod ${mod}
            }
        done
    }
    
nat_config_load_alg() {
	config_get pptp $1 pptp 
	config_get ftp $1 ftp 
	config_get sip $1 sip
	config_get h323 $1 h323	
	
	#config_get l2tp $1 l2tp 
	#config_get tftp $1 tftp 
	#config_get ipsec $1 ipsec 
	
	echo "pptp : $pptp,ftp : $ftp,sip : $sip,h323 : $h323" > /dev/console
	
    [ -n "$ftp" ] && nat__do_alg ftp $ftp "$2"
    #[ -n "$nat_alg_tftp" ] && nat__do_alg tftp $nat_alg_tftp "$2"
    [ -n "$h323" ] && nat__do_alg h323 $h323 "$2"
    #[ -n "$nat_alg_rtsp" ] && nat__do_alg rtsp $nat_alg_rtsp "$2"
    [ -n "$sip" ] && nat__do_alg sip $sip "$2"
    [ -n "$pptp" ] && nat__do_alg pptp $pptp "$2"
}

nat_alg_load() {
    local setup_mods=$(lsmod|grep -E 'nf_(nat|conntrack)_*'|awk -F ' ' '{print $1}')
    
    nat_config_once nat_config_load_alg $1 "$setup_mods"
}

nat_alg_run() {
	config_load nat
    nat_alg_load nat_alg
    #unset ALG_FILTER_CHAINS
}