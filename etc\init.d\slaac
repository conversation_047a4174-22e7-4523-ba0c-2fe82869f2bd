#!/bin/sh /etc/rc.common

START=40
STOP=40

SLAAC_BOOTSTRAP_FILE=/tmp/slaac_bootstrap.init
CONFIG_RADVD=/etc/config/radvd
INTER_IP6ADDR=""

is_support_advertise_router() {
	support=`uci get profile.slaac_config.advertise_router 2>/dev/null`
	if [ "$support" != "1" ];then
		return 0
	fi

	return 1
}


get_interface_ip6addr() {
	local interface=$1
	local with_prefixlen=$2

	INTER_IP6ADDR=""
	local config_mode=`uci get network.$interface.ip6ifaceid 2>/dev/null`

	if [ "manual" == "$config_mode" ];then
		local ip6addr=`uci get network.$interface.ip6addr 2>/dev/null`

		if [ "" != "$ip6addr" ];then
			if [ "1" != "$with_prefixlen" ];then
				ip6addr=`echo "$ip6addr" | sed -ne 's/\/[0-9].*$//p'`
			fi
			INTER_IP6ADDR=$ip6addr
			return
		fi

	elif [ "eui64" == "$config_mode" ];then
		local mac=`uci get network.$interface.macaddr 2>/dev/null`
		local prefix=`uci get network.$interface.prefix 2>/dev/null`
		if [ "" != "$mac" -a "" != "$prefix" ];then
			local complete_prefix=""
			local double_colon_index=`awk 'BEGIN{print match("'$prefix'","'::'")}'`
			if [ "0" != "$double_colon_index" ];then
				# 缩写IPv6地址
				local index1
				local index2
				let index1=$double_colon_index-1
				let index2=$double_colon_index+1
				local pre_part=${prefix:0:$index1}
				local post_part=${prefix:$index2}
				local abbre_part=""
				local pre_colon_num=`echo "$pre_part" | awk -F":" '{print NF-1}'`
				local post_colon_num=`echo "$post_part" | awk -F":" '{print NF-1}'`
				local pre_word
				local post_word
				local abbre_word
				if [ "0" == "$pre_colon_num" ];then
					pre_word=0
				else
					let pre_word=$pre_colon_num+1
				fi
				if [ "0" == "$post_colon_num" ];then
					post_word=0
				else
					let post_word=$post_colon_num+1
				fi
				let abbre_word=8-$pre_word-$post_word
				
				local i=1
				while [ ${i} -le $abbre_word ]
				do
					if [ 1 == "$i" ];then
						abbre_part="0"
					else
						abbre_part="$abbre_part:0"
					fi
					let i=i+1
				done
				
				if [ "" == "$pre_part" ];then
					complete_prefix="$abbre_part"
				else
					complete_prefix="$pre_part:$abbre_part"
				fi
				if [ "" != "$post_part" ];then
					complete_prefix="$complete_prefix:$post_part"
				fi
			fi

			local prefix1
			local prefix2
			local prefix3
			local prefix4
			local i=1
			while [ 1 == 1 ]
			do
				split=`echo $complete_prefix | cut -d ":" -f$i`
		        if [ "$split" != "" ]
		        then  
					eval prefix${i}=$split
		        else
					break
		        fi
		        let i=$i+1
			done

			# echo "$prefix1 $prefix2 $prefix3 $prefix4"

			local mac1
			local mac2
			local mac3
			local mac_insert1="ff"
			local mac_insert2="fe"
			local mac4
			local mac5
			local mac6

			local i=1
			while [ 1 == 1 ]
			do
				split=`echo $mac | cut -d ":" -f$i`
		        if [ "$split" != "" ]
		        then  
					eval mac${i}=$split
		        else
					break
		        fi
		        let i=$i+1
			done

			# echo "$mac1 $mac2 $mac3 $mac4 $mac5 $mac6"

			# mac第2位取反
			tmp_mac=`printf %d 0x$mac1`
			local tmp_mac=`printf "%#x" $(( $tmp_mac ^ 0x02))`
			mac1=${tmp_mac:2}

			local ip6addr
			if [ "1" != "$with_prefixlen" ];then
				ip6addr="$prefix1:$prefix2:$prefix3:$prefix4:$mac1$mac2:$mac3$mac_insert1:$mac_insert2$mac4:$mac5$mac6"
			else
				ip6addr="$prefix1:$prefix2:$prefix3:$prefix4:$mac1$mac2:$mac3$mac_insert1:$mac_insert2$mac4:$mac5$mac6/64"
			fi
			INTER_IP6ADDR=$ip6addr
		fi
	else
		echo "ip6ifaceid of interface $interface is invalid, and slaac get ip6addr failed." > /dev/console
	fi
}


slaac_write_route() {
	local interface=$1

	local ipv6_enable=`uci get network.$interface.ipv6_enable 2>/dev/null`
	[ "on" != "$ipv6_enable" ] && return 0

	get_interface_ip6addr $interface "1"
	[ "" == "$INTER_IP6ADDR" ] && return 0

	prefix=`echo "$INTER_IP6ADDR" | sed 's:\\/[0-9]*$:\\/128:g'`
	printf "\tlist prefix '$prefix'\n" >> $CONFIG_RADVD
}

slaac_write_slaac() {
	local cfg=$1

	local enable
	local interface
	local prefix
	local dns_type
	local pri_dns
	local snd_dns

	config_get enable "$cfg" enable
	[ "$enable" != "on" ] && return 0

	config_get interface "$cfg" interface
	config_get prefix "$cfg" prefix
	config_get dns_type "$cfg" dns_type
	config_get pri_dns "$cfg" pri_dns
	config_get snd_dns "$cfg" snd_dns

	[ -z "$interface" -o -z "$dns_type" ] && return 0

	#set interface
	printf "config interface if_$interface\n"
	printf "\toption interface  '$interface'\n"
	printf "\toption AdvSendAdvert  1\n"
	printf "\toption AdvManagedFlag  0\n"

	is_support_advertise_router
	if [ $? -eq 1 ];then
		printf "\toption AdvDefaultLifetime  1800\n"
	else
		printf "\toption AdvDefaultLifetime  0\n"
	fi

	if [ "$dns_type" == "RDNSS" ]; then
		printf "\toption AdvOtherConfigFlag  0\n"
	else
		printf "\toption AdvOtherConfigFlag  1\n"
	fi
	printf "\toption ignore  0\n"
	printf "\n"


	#set prefix
	printf "config prefix prefix_$interface\n"
	printf "\toption interface  '$interface'\n"
	printf "\toption AdvOnLink  1\n"
	printf "\toption AdvAutonomous  1\n"
	printf "\toption AdvRouterAddr  0\n"
	printf "\toption DeprecatePrefix 1\n"
	if [ "$prefix" != "" ]; then
		printf "\tlist prefix  '$prefix'\n"
	fi
	printf "\toption ignore  0\n"
	printf "\n"


	if [ "$dns_type" == "RDNSS" ] && [ "" == "$pri_dns" -a "" == "$snd_dns" ];then
		is_support_advertise_router
		if [ $? -eq 1 ];then
			get_interface_ip6addr $interface "0"
			pri_dns=$INTER_IP6ADDR
		fi
	fi

	#set rdnss
	if [ "$dns_type" == "RDNSS" ] && [ "$pri_dns" != "" -o "$snd_dns" != "" ]; then
		printf "config rdnss rdnss_$interface\n"
		printf "\toption interface  '$interface'\n"
		if [ "$pri_dns" != "" ]; then
			printf "\tlist addr  '$pri_dns'\n"
		fi
		if [ "$snd_dns" != "" ]; then
			printf "\tlist addr  '$snd_dns'\n"
		fi
		printf "\toption ignore  0\n"
		printf "\n"
	fi

	return 0
}

slaac_write_dhcp() {
	local cfg=$1

	local enable
	local interface

	config_get enable "$cfg" enable
	[ "$enable" != "on" ] && return 0

	config_get interface "$cfg" interface
	[ -z "$interface" ] && return 0

	#set interface
	printf "config interface if_$interface\n"
	printf "\toption interface  '$interface'\n"
	printf "\toption AdvSendAdvert  1\n"
	printf "\toption AdvManagedFlag  1\n"

	is_support_advertise_router
	if [ $? -eq 1 ];then
		printf "\toption AdvDefaultLifetime  1800\n"
	else
		printf "\toption AdvDefaultLifetime  0\n"
	fi

	printf "\toption AdvOtherConfigFlag  1\n"
	printf "\toption ignore  0\n"
	printf "\n"

	#set route
	printf "config route route_$interface\n"
	printf "\toption interface  '$interface'\n"
	slaac_write_route $interface
	printf "\toption ignore  0\n"
	printf "\n"

	return 0
}

start_service() {
	echo "" > $CONFIG_RADVD
	config_load slaac

	config_foreach slaac_write_slaac slaac_list >> $CONFIG_RADVD

	config_foreach slaac_write_dhcp dhcp >> $CONFIG_RADVD
}

start() {
	INTERFACE=$1
	start_service

	if [ -f $SLAAC_BOOTSTRAP_FILE ]; then
		/etc/init.d/radvd start $INTERFACE
	else
		touch $SLAAC_BOOTSTRAP_FILE
	fi
}

stop() {
	INTERFACE=$1
	if [ -f $SLAAC_BOOTSTRAP_FILE ]; then
		/etc/init.d/radvd stop $INTERFACE
	fi
}

reload() {
	start $1
}

restart() {
	stop $1
	start $1
}