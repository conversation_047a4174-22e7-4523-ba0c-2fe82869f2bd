#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"
local uci = require ("luci.model.uci")

local NETWORK_UCI = "network"
local B6_TABLE = "bridge_v6"
local OLD_ENABLE_B6 = "enable"
local NEW_ENABLE_B6 = "bridge6_enable"

local function b6_should_sync()
    local uci_r = uci.cursor()
    local b6_enable = uci_r:get(NETWORK_UCI, B6_TABLE, NEW_ENABLE_B6)
    if nil ~= b6_enable then
        return false
    end
    return true
end

local function b6_do_sync()
    local uci_r = uci.cursor()
    local enable_value = uci_r:get(NETWORK_UCI, B6_TABLE, OLD_ENABLE_B6)
    if nil ~= enable_value then
        uci_r:set(NETWORK_UCI, B6_TABLE, NEW_ENABLE_B6, enable_value)
        uci_r:set(NETWORK_UCI, B6_TABLE, OLD_ENABLE_B6, "")
        uci_r:commit(NETWORK_UCI)
    end
end
    

local function bridge6_sync()
    local need_sync = false
    if true == b6_should_sync() then
        need_sync = true
    end
    if true == need_sync then
        b6_do_sync()
        cfgsync.set_config_changed()
    end
end

bridge6_sync()