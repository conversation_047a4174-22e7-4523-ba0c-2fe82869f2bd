#!/bin/sh
# Copyright (C) 2006-2010 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

cal_ubi_size() {
        local mtd_part=$1
        local reserve_block=$2
        local mtd_size
        local block_num
        local ubi_size

        mtd_size="0x$(cat /proc/mtd | grep -w $mtd_part | awk '{print $2}')"
        block_num=$(($mtd_size/0x20000))
        ubi_size=$(($(($block_num-$reserve_block))*0x20000*124/128))

        echo $ubi_size
}

find_mount_jffs2() {
    local uc_size=$(cal_ubi_size \"config\" 10)
    local no_uc_size=$(cal_ubi_size \"no_uc_config\" 14)
    local ubifs_size=$(cal_ubi_size \"rootfs_data\" 14)


    if [ -e /dev/ubi0_0 -o -h /dev/ubi0_0 ]; then
        ubirsvol /dev/ubi0 -n 0 -s $ubifs_size
    else
        ubimkvol /dev/ubi0 -N 0 -s $ubifs_size
    fi

    if [ -e /dev/ubi1_0 -o -h /dev/ubi1_0 ]; then
        ubirsvol /dev/ubi1 -n 0 -s $uc_size
    else
        ubimkvol /dev/ubi1 -N 0 -s $uc_size
    fi

    if [ -e /dev/ubi2_0 -o -h /dev/ubi2_0 ]; then
        ubirsvol /dev/ubi2 -n 0 -s $no_uc_size
    else
        ubimkvol /dev/ubi2 -N 0 -s $no_uc_size
    fi

    mkdir -p /tmp/overlay
    mount "ubi0_0" /tmp/overlay -t ubifs -o sync
}

jffs2_not_mounted() {
    if [ "$pi_jffs2_mount_success" != "true" ]; then
	return 0
    else
	return 1
    fi
}

do_mount_jffs2() {
    check_skip || {
	find_mount_jffs2 && pi_jffs2_mount_success=true
    }
}

boot_hook_add preinit_mount_root do_mount_jffs2

