#!/bin/sh

LICENSE_PATH="/tmp/license"

# purpose : 获取license当前状态
# param   : module  License内部名称
# return  : license正在生效，输出 1 ; 失效或未激活，输出 0
license_status_get() {
	local module="$1"
	local isFree license status
	isFree=$(uci get "license.${module}.type" 2>/dev/null)
	if [ "$isFree" = "free" ]; then
		echo -n "1"
		return 0
	fi

	license="$(uci get "license.${module}.content" 2>/dev/null)"
	if [ -z "${license}" ]; then
		echo "[license.sh:ERROR] can't find [${module}] license content in license config" > /dev/console
		echo -n "0"
		return 1
	fi
	# 可能出现磁盘损坏导致数据库损坏的情况、数据库db清除
	# 存量机型可能不存在网站分类库db
	# 在没有license情况下允许用户进行一次升级
	local hotsite_db="/tmp/sdb/nvram/hotsite.db"
	if [ "hotsite" = "${module}" ] && [ ! -f ${hotsite_db} ];then
		echo -n "1"
		return 0
	fi

	status=$(uci get -c $LICENSE_PATH "license_info.${license}.status" 2>/dev/null)

	[ -z "$status" ] && status=0
	echo -n "$status"
	return 0
}

# purpose : get License expired time
# param   : module  License内部名称
# return  : 如果激活过license，输出日期; 否则，不会输出任何东西;
#           对于type为free的特征库不输出，返回0
license_expired_time_get() {
	local module="$1"
	local isFree license expired_time
	isFree=$(uci get "license.${module}.type" 2>/dev/null)
	if [ "$isFree" = "free" ]; then
		# free 类型license不需要显示有效期
		# 这里不echo 任何值
		return 0
	fi

	license="$(uci get "license.${module}.content" 2>/dev/null)"
	if [ -z "${license}" ]; then
		echo "[license.sh:ERROR] can't find [${module}] license content in license config" > /dev/console
		return 1
	fi

	expired_time=$(uci get -c $LICENSE_PATH "license_info.${license}.expired_time" 2>/dev/null)

	echo -n "$expired_time"
}

# purpose : get License spec num
# param   : module  License内部名称
# return  : 如果模块有规格参数限制，则输出规格参数; 否则，不会输出任何东西
license_get_spec_num() {
	local module="$1"
	local spec_level="$2"
	local spec_num=""
	if [ ! -z "$spec_level" ]; then
		spec_num=$(uci get profile.@${module}[-1].${spec_level}_spec_num 2>/dev/null)
	else
		local spec=""
		spec=$(uci get -c $LICENSE_PATH license_info.${module}.spec 2>/dev/null)
		if [ -z "$spec" ]; then
			# license没有规格参数
			return 0
		fi
		spec_num=$(uci get profile.@${module}[-1].${spec}_spec_num 2>/dev/null)
	fi

	if [ -z "$spec_num" ]; then
		# 该模块不支持规格参数
		return 0
	elif [ "$spec_num" -eq "**********" ]; then
		# 0x7FFFFFFF表示满条目，不做限制
                return 0
	fi

	echo -n "$spec_num"
}
