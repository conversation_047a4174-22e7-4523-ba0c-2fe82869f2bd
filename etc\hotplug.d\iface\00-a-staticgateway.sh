#!/bin/sh

[ "$INTERFACE" == "" ] && return 0
[ "$ACTION" != "ifup" ] && return 0

. /lib/zone/zone_api.sh

protox=`uci get network.${INTERFACE}.proto 2>/dev/null`
gatewayx=`uci get network.${INTERFACE}.gateway 2>/dev/null`
metric=`uci get network.${INTERFACE}.metric 2>/dev/null`

[ "${protox}" != "static" ] && return 0
[ "${metric}" == "" ] && metric=0

devx=${DEVICE}
[ "${devx}" == "" ] && devx=`zone_get_device_byif $INTERFACE`

[ "${devx}" == "" ] && return 0

if [ "${gatewayx}" != "" ]; then
	route del -net default/0 gw ${gatewayx} dev ${devx}
	route add -net default/0 gw ${gatewayx} dev ${devx} metric ${metric}
fi

ipv6_enable=`uci get network.${INTERFACE}.ipv6_enable 2>/dev/null`
ip6gw=`uci get network.${INTERFACE}.ip6gw 2>/dev/null`

[ "${ipv6_enable}" == "" ] && return 0
[ "${ip6gw}" == "" ] && return 0

if [ "${ipv6_enable}" == "on" ]; then
	route del -A inet6 default gw ${ip6gw} dev ${devx}
	route add -A inet6 default gw ${ip6gw} dev ${devx} metric ${metric}
fi

