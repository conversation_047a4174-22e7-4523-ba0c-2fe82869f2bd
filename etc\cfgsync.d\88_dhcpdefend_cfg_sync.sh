#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"

local db_dhcpdef_bind = {
	name = "dhcpdef_bind.db",
	tables = {
		{
			name = "bind_tbl",
			columns = {
				{
					name = "ap_id",
					stype = "INTEGER",
					db_attr = {"db_index", "db_unique"},
					default_value = nil
				}, {
					name = "enable",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "svr_ids",
					stype = "VARCHAR ( 0 )",
					db_attr = {},
					default_value = nil
				}, {
					name = "priv_key",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			},
			datas = {}
		}
	}
}

local db_dhcpdef_svr = {
	name = "dhcpdef_svr.db",
	tables = {
		{
			name = "svr_tbl",
			columns = {
				{
					name = "svr_id",
					stype = "INTEGER",
					db_attr = {"db_index", "db_unique"},
					default_value = nil
				}, {
					name = "name",
					stype = "VARCHAR ( 160 )",
					db_attr = {"db_unique"},
					default_value = nil
				}, {
					name = "addr_type",
					stype = "INTEGER",
					db_attr = {},
					default_value = nil
				}, {
					name = "svr_addr",
					stype = "VARCHAR ( 40 )",
					db_attr = {"db_unique"},
					default_value = nil
				}, {
					name = "priv_key",
					stype = "INTEGER",
					db_attr = {"db_key", "db_auto_increment"},
					default_value = nil
				}
			},
			datas = {}
		}
	}
}

cfgsync.config_sync(db_dhcpdef_bind, "database")
cfgsync.config_sync(db_dhcpdef_svr, "database")