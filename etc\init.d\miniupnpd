#!/bin/sh /etc/rc.common
# Copyright (C) 2006-2011 OpenWrt.org

START=96
STOP=20

SERVICE_USE_PID=1

IPT_NAT="iptables -t nat -w"
#IPT="iptables -t filter -w"
prerouting_chain="prerouting_rule"

boot() {
	start
}

clean_iptables_rule()
{
	nat_upnp=`$IPT_NAT --line-number -L prerouting_rule |grep MINIUPNPD`
	del_time=`$IPT_NAT --line-number -L prerouting_rule |grep MINIUPNPD|wc -l`
	while [ -n "$nat_upnp" -a $del_time -gt 0 ]; do
		num=`echo $nat_upnp |cut -d " " -f 1`
		$IPT_NAT -D prerouting_rule $num 2>/dev/null

		nat_upnp=`$IPT_NAT --line-number -L prerouting_rule |grep MINIUPNPD`
		del_time=$(($del_time-1))
	done

	$IPT_NAT -F MINIUPNPD 2>/dev/null
	$IPT_NAT -X MINIUPNPD 2>/dev/null

	#del filter rule
	#$IPT -F MINIUPNPD 2>/dev/null
	#$IPT -F UPNP_EXTERNAL 2>/dev/null

	#$IPT -D FORWARD -j UPNP_EXTERNAL 2>/dev/null

	#$IPT -X MINIUPNPD 2>/dev/null
	#$IPT -X UPNP_EXTERNAL 2>/dev/null
	local prerouting_chain_rule_num=`iptables -t nat -S ${prerouting_chain} | wc -l`
	if [ -z "$prerouting_chain_rule_num" -o $prerouting_chain_rule_num -eq 1 ];then
		$IPT_NAT -D  PREROUTING -j ${prerouting_chain}
		$IPT_NAT -X ${prerouting_chain}
	fi
}

start() {
	state=`uci get upnpd.config.enable_upnp 2>/dev/null`
	if [ "$state" == "off" ];then
		return
	fi

	if [ -f "/etc/init.d/upnp_proxy" ]; then
		/etc/init.d/upnp_proxy restart
	fi

	. /lib/zone/zone_api.sh

	local extiface intiface logging
	local port conffile group_num
	local uuid

	#if ! $IPT -S MINIUPNPD &> /dev/null; then
	#	$IPT -N MINIUPNPD
	#fi

	#if ! $IPT -S UPNP_EXTERNAL &> /dev/null; then
	#	$IPT -N UPNP_EXTERNAL
	#fi

	#if ! $IPT -S FORWARD | grep UPNP_EXTERNAL &> /dev/null; then
	#	$IPT -I FORWARD -j UPNP_EXTERNAL
	#fi

	local prerouting_chain_main=`iptables -w -t nat -nvL | grep -w ${prerouting_chain}`
	if [ -z "$prerouting_chain_main" ]; then
		$IPT_NAT -N ${prerouting_chain}
		$IPT_NAT -I PREROUTING -j ${prerouting_chain}
	fi

	if ! $IPT_NAT -S MINIUPNPD &> /dev/null; then
		$IPT_NAT -N MINIUPNPD
	fi

	external_iface=`uci get upnpd.config.external_iface 2>/dev/null`

	pos=`iptables -t nat --line-number -L prerouting_rule | grep prerouting_rule_dmz | cut -d " " -f 1`

	for iface in $external_iface ; do
		dev=`zone_get_effect_devices $iface`

		[ -n $dev ] && {
			#if ! $IPT -S UPNP_EXTERNAL |grep $dev &> /dev/null; then
			#	$IPT -I UPNP_EXTERNAL -i $dev -j MINIUPNPD
			#fi
			if ! $IPT_NAT -S prerouting_rule | grep MINIUPNPD | grep $dev &> /dev/null; then
				if [ "${pos}" = "" ]; then
					$IPT_NAT -A prerouting_rule -i $dev -j MINIUPNPD
				else
					$IPT_NAT -I prerouting_rule $pos -i $dev -j MINIUPNPD
				fi
			fi
			if [ -z $extiface ]; then
				extiface=${dev}
			else
				extiface=${extiface}"/"${dev}
			fi
		}
	done

	local max_upnp_rules_num=`uci get profile.@upnp[0].upnp_mapping_max 2>/dev/null`
	local web_port=`uci get system.service_port.http_port 2>/dev/null`

	local args

	. /lib/functions/network.sh

	if [ -n "$conffile" ]; then
		args="-f $conffile"
	else
		local tmpconf="/var/etc/miniupnpd.conf"
		args="-f $tmpconf"
		mkdir -p /var/etc

		echo "ext_ifname=$extiface" >$tmpconf

		server_iface=`uci get upnpd.config.internal_iface 2>/dev/null`
		local iface
		local iface_eth
		for iface in ${server_iface}; do
			local ipnet
			dev=`zone_get_effect_devices $iface`
			iface_eth=`zone_get_effect_ifaces $iface`
			network_get_netmask ipnet "$iface_eth" && {
				echo "listening_ip=$dev"/"$ipnet" >>$tmpconf
			}
			let group_num++
		done

		echo "port=1900" >>$tmpconf

		rm -f "$upnp_lease_file" 2>/dev/null

		echo "enable_natpmp=yes" >>$tmpconf
		echo "enable_upnp=yes" >>$tmpconf
		echo "secure_mode=yes" >>$tmpconf
		echo "system_uptime=yes" >>$tmpconf

		echo "lease_file=/var/upnp.leases" >>$tmpconf

		if [ -n "$max_upnp_rules_num" ]; then
			echo "max_upnp_rules_num=$max_upnp_rules_num" >>$tmpconf
		else
			echo "max_upnp_rules_num=160" >>$tmpconf
		fi

		echo "web_port=$web_port" >>$tmpconf

		local max_pppoeserver_sessions=`uci get profile.@pppoeserver[0].max_sessions 2>/dev/null `
		[ -n "$max_pppoeserver_sessions" ] && {
			let group_num=${group_num}+${max_pppoeserver_sessions}
			sysctl -w net.ipv4.igmp_max_memberships=${group_num}
		}

		[ -z "$uuid" ] && {
			uuid="$(cat /proc/sys/kernel/random/uuid)"
		}

		[ "$uuid" = "nocli" ] || \
			echo "uuid=$uuid" >>$tmpconf

		echo "allow 0-65535 0.0.0.0/0 0-65535" >>$tmpconf
	fi


	if [ -n "$extiface" ]; then
		if [ "$logging" = "1" ]; then
			SERVICE_DAEMONIZE=1 \
			service_start /usr/sbin/miniupnpd $args -d
		else
			SERVICE_DAEMONIZE=1 \
			service_start /usr/sbin/miniupnpd $args
		fi
	else
		logger -t "upnp daemon" "external interface not found, not starting"
	fi
}

stop() {
	service_stop /usr/sbin/miniupnpd
	if [ -f "/etc/init.d/upnp_proxy" ]; then
		/etc/init.d/upnp_proxy restart
	fi
	clean_iptables_rule
	rm /var/upnp.leases
}

restart() {
	service_stop /usr/sbin/miniupnpd
	clean_iptables_rule
	start
}
