#!/bin/sh
# global variables:
#	RUNDIR RUNFILE CONFIF1 CONFIF2 BRIF ACTION

#################################################
# below defined by yourself                     #
#################################################

origdir=/etc/config
tmpdir=/tmp/nginx  # NOTE: tmpdir should not be /tmp/bridge, /tmp/bridge has been used for other purpose

mkdir -p ${tmpdir}

config="wportal portal.db"

# 1. copy config files to tmpdir
for ff in ${config}; do
	cp -f ${origdir}/${ff} ${tmpdir}/${ff}
done

# 2. modify the config files
case ${ACTION} in
	ADDBR)
		[ -n "${CONFIF2}" -a -n "${BRIF}" ] && {
			cntx=`sed -rn "/list\s+interface\s+'*${CONFIF2}'*/p"  ${tmpdir}/wportal|wc -l`
			sed -ri "s/list\s+interface\s+'*${CONFIF2}'*/list interface '${BRIF}'/g"  ${tmpdir}/wportal
			
			sqlite3 ${tmpdir}/portal.db "UPDATE wportal SET gw_id='br-${BRIF}' WHERE interface='${CONFIF2}'"
			
			sqlite3 ${tmpdir}/portal.db "UPDATE wportal SET interface='${BRIF}' WHERE interface='${CONFIF2}'"
			
			sqlite3 ${tmpdir}/portal.db "UPDATE wechat SET gw_id='br-${BRIF}' WHERE interface='${CONFIF2}'"
			
			sqlite3 ${tmpdir}/portal.db "UPDATE wechat SET interface='${BRIF}' WHERE interface='${CONFIF2}'"
			
			# reference modify
			
			cnt=`uci get network.${CONFIF2}.t_reference`
			cnt=${cnt:-0}
			let cnt=cnt-cntx
			[ ${cnt} -lt 0 ] && cnt=0
			uci set network.${CONFIF2}.t_reference=${cnt}
			uci commit network
			
			cnt=`uci get network.${BRIF}.t_reference`
			cnt=${cnt:-0}
			let cnt=cnt+cntx
			uci set network.${BRIF}.t_reference=${cnt}
			uci commit network
		}
		;;
	UPDBR)
		# to do
		;;
	DELBR)
		# to do
		;;
	*)
		;;
esac

# 3. replace the config files
for ff in ${config}; do
	cp -f ${tmpdir}/${ff} ${origdir}/${ff}
done


