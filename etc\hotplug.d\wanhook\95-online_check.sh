#!/bin/sh
. /lib/functions.sh
. /lib/zone/zone_api.sh

_online_del_zone()
{
	uci delete online_check.${1}
	uci commit online_check
}

_online_add_zone()
{
	uci set online_check.${1}=if
	uci set online_check.${1}.mode="auto"
	uci set online_check.${1}.gateway="0.0.0.0"
	uci set online_check.${1}.dns="0.0.0.0"
	uci commit online_check
}

_online_update_zone()
{
	mode=`uci get online_check.${1}.mode 2>/dev/null`
	manual_gw=`uci get online_check.${1}.gateway 2>/dev/null`
	manual_dns=`uci get online_check.${1}.dns 2>/dev/null`

	[ -n "$mode" ] || mode="auto"
	[ -n "$manual_gw" ] || manual_gw="0.0.0.0"
	[ -n "$manual_dns" ] || manual_dns="0.0.0.0"

	dns1="0.0.0.0"
	dns2="0.0.0.0"

	for cnt in $(seq 1 300); do
		dev=`zone_get_effect_devices $1`
		if [ -n "$dev" ]; then
			break
		fi
		sleep 1
	done

	ubus call online_check update_if "{\"name\":\"$name\",\"dev\":\"$dev\",\"mode\":\"$mode\",\"manual_gw\":\"$manual_gw\",
	\"manual_dns\":\"$manual_dns\",\"dns1\":\"$dns1\",\"dns2\":\"$dns2\"}"
}


case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in ${interfaces}; do
				_online_del_zone ${element}
			done
		}
	;;
	ADD)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in ${interfaces}; do
				_online_add_zone ${element}
			done
		}
	;;
	WANMOD)
		[ -n "${interfaces}" ] && {
			echo "WANMOD interfaces=$interfaces" >> /tmp/online_wanhook.log
		    interfaces=${interfaces//,/ }
		    for element in ${interfaces}; do
		    	#del_online_check ${element}
		    	#add_online_check ${element}
		    	_online_update_zone ${element}
		    done
		}

	;;
	*)
	;;
esac
