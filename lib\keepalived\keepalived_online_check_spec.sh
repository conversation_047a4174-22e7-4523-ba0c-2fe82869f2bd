#!/bin/sh
[ ! -f "/etc/keepalived/keepalived.conf" ] && exit 1

. /lib/functions.sh
. /lib/functions/network.sh

# check spec interface, up_flag =1
get_spec_if_online_status()
{
    local result=`ubus call online_check get_iface_spec "{\"name\":\"$1\"}"| grep  -E "state" | tr '"' ' '`
	local tmp_state=${result#*":"}
	if [ $tmp_state == "up" ]; then
		exit 0
	else
		exit 1
    fi
}

get_spec_if_online_status $1


