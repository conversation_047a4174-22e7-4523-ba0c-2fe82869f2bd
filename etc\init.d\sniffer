#!/bin/sh /etc/rc.common

START=35
LLDP_INIT_SCRIPT=/etc/init.d/lldp
LLDP_PID_FILE=/var/run/lldp.pid

#根据sniffer配置，初始化旁路审计
start() {
    local enable
    local sniffer_userif

    config_load sniffer
    config_get enable global enable "off"
    config_get sniffer_userif global userif ""

    #echo "Sniffer is $enable" > /dev/console
    if [ -x "$(which sfe_switch)" ]; then
        if [ -f "/etc/init.d/qca-nss-ecm" ]; then
            sfe_switch register sniffer off
        fi
    fi
    #Enable sniffer
    if [ "$enable" == "on" -a -n "$sniffer_userif" ]; then
        sniffer_cnt=0
        if [ -x "$(which sfe_switch)" ]; then
            if [ -f "/etc/init.d/qca-nss-ecm" ]; then
                sfe_switch update sniffer off
            fi
        fi
        for userif in $sniffer_userif; do
            audit_brname="br_AUDIT"$sniffer_cnt   #审计网桥前缀为br_AUDIT
            audit_devnames=$(. /lib/zone/zone_api.sh; zone_userif_to_effective_dev $userif)
            #echo "Setup sniffer of $userif, br $audit_brname, devs $audit_devnames" > /dev/console

            for audit_devname in $audit_devnames; do
                #Create audit bridge
                brctl addbr $audit_brname
                brctl addif $audit_brname $audit_devname
                ifconfig $audit_brname up

                #enable on port in/out
                echo 1 > /sys/class/net/$audit_brname/brif/$audit_devname/hairpin_mode

                #Set netdevice flag IFF_BRIDGE_PORT_FWDFLOW_NO_XMIT, to avoid xmit bridge forwarding flows
                old_flag=$(cat /sys/class/net/$audit_devname/flags)
                printf "%#x" $(($old_flag|0x80000)) > /sys/class/net/$audit_devname/flags
            done

            let sniffer_cnt=sniffer_cnt+1
        done
    else
        if [ -x "$(which sfe_switch)" ]; then
            if [ -f "/etc/init.d/qca-nss-ecm" ]; then
                sfe_switch update sniffer on
            fi
        fi
    fi

    # restart lldp since sniffer-mode ports is updated
    if [ -f "$LLDP_INIT_SCRIPT" ] && [ -f "$LLDP_PID_FILE" ]; then
        $LLDP_INIT_SCRIPT restart
    fi
}

#无条件撤销审计相关配置
stop() {
    audit_brnames=$(ls /sys/class/net/ | grep -E "^br_AUDIT")

    if [ -n "$audit_brnames" ]; then
        for audit_brname in $audit_brnames; do
            audit_devnames=$(ls /sys/class/net/$audit_brname/brif/)
            #echo "Stop sniffer of br $audit_brname, devs $audit_devnames" > /dev/console

            for audit_devname in $audit_devnames; do
                #Use IFF_CLR_BRIDGE_PORT_FWDFLOW_NO_XMIT to clear IFF_BRIDGE_PORT_FWDFLOW_NO_XMIT
                old_flag=$(cat /sys/class/net/$audit_devname/flags)
                printf "%#x" $(($old_flag|0x100000)) > /sys/class/net/$audit_devname/flags
                #clear promisc
                ifconfig $audit_devname -promisc
            done

            #Delete audit bridge
            ifconfig $audit_brname down
            brctl delbr $audit_brname
        done
    fi

    if [ -x "$(which sfe_switch)" ]; then
        if [ -f "/etc/init.d/qca-nss-ecm" ]; then
            sfe_switch update sniffer on
        fi
    fi
}

restart() {
    stop
    start
}
