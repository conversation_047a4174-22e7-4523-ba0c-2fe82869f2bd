#!/bin/sh /etc/rc.common

START=15

SERVICE_DAEMONIZE=1
SERVICE_USE_PID=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/cfg_save.pid

start() {
	if [ ! -f ${SERVICE_PID_FILE} ];then
		service_start /usr/sbin/cfg_save &
	fi
}

_stop()
{
	service_stop /usr/sbin/cfg_save
	if [ -f ${SERVICE_PID_FILE} ];then
		rm -f ${SERVICE_PID_FILE}
	fi
}

stop() {
	_stop
}


restart()
{
	echo "restarting cfg_save service" >/dev/console
	stop
	start
}

reload()
{
	restart
}
