#!/bin/sh /etc/rc.common

START=31

SERVICE_DAEMONIZE=1
USE_PROCD=0
PROG=/usr/sbin/tmngtd

start() {
	local rule_num=$(cat /etc/config/time_mngt | grep "\bconfig\b" | wc -l)
	[ "$rule_num" -le 1 ] && return
    . /lib/time_mngt/timeobj_api.sh
	timeobj_init_deleteAll
	rm -rf /etc/tmngtd.rules/*
	mkdir /etc/tmngtd.rules/conf_update.d

	#grep -q "tmngtd#/etc/init.d/tmngtd start" /etc/sys_monitor.conf || echo "tmngtd#/etc/init.d/tmngtd start" >>/etc/sys_monitor.conf
    service_start $PROG
    # procd_open_instance
	# procd_set_param command $PROG
	# procd_set_param respawn 3600 5 60
	# procd_close_instance
}

stop() {
    . /lib/time_mngt/timeobj_api.sh
	timeobj_init_deleteAll
	rm -rf /etc/tmngtd.rules/*
	mkdir /etc/tmngtd.rules/conf_update.d

	service_stop $PROG
}

restart() {
    stop
    start
}

reload() {
    restart
}