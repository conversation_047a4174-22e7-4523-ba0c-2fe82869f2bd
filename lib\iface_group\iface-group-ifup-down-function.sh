#!/bin/sh
# handle the user zone(sec_zone) affairs for vpn device ifup/ifdown
#
#${ACTION} ifup or ifdown
#${IFNAME} device of vpn
#${PPPD_USERNAME}
#${PPPD_PID}
#${PPPD_TYPE} server or client
#${PPPD_CONFIGPATH}
#${IPREMOTE}
#${IPLOCAL}
#

. /lib/functions.sh
. /lib/firewall/fw.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/zone/zone_api.sh

UCI_VPN_IFACE_DIR=/tmp/ppp
UCI_VPN_IFACE_BINDIF_FILE=vpn_iface_bindif

bindif=""
action=""
user_zone=""
user_iface=""

#$1: section name
#get the bindif(only can get when ifup)
get_param()
{
	case "${PPPD_TYPE}" in
		"server")
			config_get bindif $1 "bindif";;
		"client")
			config_get bindif $1 "outif";;
	esac
}

get_param_ipsec()
{
	virtualif=`uci get vpn.$1.virtualif`
	#echo "get_param_ipsec() $1.virtualif:${virtualif}" > /dev/console

	if [ -n "${virtualif}" -a "${virtualif}" = "${IFNAME}" ];then
		bindif=`uci get vpn.$1.bindif`
		#echo "get_param_ipsec() bindif:${bindif}" > /dev/console
	fi
}

#$1: section name
#$2: iface name
#$2: option name
#remove vpn iface from the vpn_iface_bindif and find out the bindif
remove_iface()
{

	iface_list=`uci -c ${UCI_VPN_IFACE_DIR} get ${UCI_VPN_IFACE_BINDIF_FILE}.$1.$3`
	#echo "iface_list: ${iface_list}" > /dev/console

	if [ -n "${iface_list}" ];then
		for iface in $iface_list;do
			#echo "iface: ${iface}" > /dev/console
			if [ -n "${iface}" -a "${iface}" = "$2" ];then
				bindif=$1
			fi
		done
	fi
	uci -c ${UCI_VPN_IFACE_DIR} del_list ${UCI_VPN_IFACE_BINDIF_FILE}.$1.$3=$2
}

#$1: section name
#find the user zone the bindif belong to
#tips:for FW, the userif is equal the iface in zone
get_user_zone()
{
	type=`uci get zone.$1.type`
	#echo "type:${type}" > /dev/console
	#only handle the user_zone
	if [ -n "${type}" -a "user" = "${type}" ];then
		bindif_list=`uci get zone.$1.iface`
		if [ -n "${bindif_list}" ];then
			for bindiface in ${bindif_list};do
				if [ "${bindiface}" = "${bindif}" ];then
					user_zone=`uci get zone.$1.name`
					#echo "user_zone: ${user_zone}" > /dev/console
				fi
			done
		fi
	fi
}


do_ifup()
{
	# record the relationship of vpniface and bindif in vpn_iface_bindif
	# tips:for ER/FW the bind_if is user_if
	case "${PPPD_TYPE}" in
		"server")
			#echo "server ifup: ${IFNAME}" > /dev/console
			user_iface="VPN_Server"
			uci -c ${UCI_VPN_IFACE_DIR} set ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}="bindif"
			uci -c ${UCI_VPN_IFACE_DIR} add_list ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}.server_iface=${IFNAME};;
		"client")
			#echo "client ifup: ${IFNAME}" > /dev/console
			user_iface=$(zone_get_userif_bydev "${IFNAME}")
			uci -c ${UCI_VPN_IFACE_DIR} set ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}="bindif"
			uci -c ${UCI_VPN_IFACE_DIR} add_list ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}.client_iface=${IFNAME};;
		"ipsec")
			# echo "ipsec ifup: ${IFNAME}" > /dev/console
			# echo "do_ifup() route ipsec ifup: ${IFNAME}" > /dev/console
			user_iface=${IFNAME}
			uci -c ${UCI_VPN_IFACE_DIR} set ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}="bindif"
			iflist=`uci -c ${UCI_VPN_IFACE_DIR} get ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}.ipsec_iface | grep ${IFNAME}`
			if [ -z "${iflist}" ];then
				uci -c ${UCI_VPN_IFACE_DIR} add_list ${UCI_VPN_IFACE_BINDIF_FILE}.${bindif}.ipsec_iface=${IFNAME}
			fi;;
	esac
	uci -c ${UCI_VPN_IFACE_DIR} commit ${UCI_VPN_IFACE_BINDIF_FILE}

	# add the device to the user_zone iface_group ipset
	# only the FW has the user_zone, so this code is only used for FW
	if [ "${PPPD_TYPE}" != "ipsec" ];then
		config_load /etc/config/zone
		config_foreach get_user_zone "zone"
		config_foreach config_clear
	fi
	if [ -n "${user_zone}" -a "" != "${user_zone}" ];then
		#echo "ipset add ${user_zone}_IFACES ${IFNAME} -exist" > /dev/console
		ipset add ${user_zone}_IFACES ${IFNAME} -exist
		# set the user_zone and userif to the netdevice, so that the FW ko can read the userzone and userif of the flow
		#echo "dev:${IFNAME}" > /dev/console
		#echo "user_zone:${user_zone}" > /dev/console
		#echo "user_iface:${user_iface}" > /dev/console

		if [ -n "${user_iface}" -a "" != "${user_iface}" ];then
			[ -f /sys/class/net/${IFNAME}/ifalias ] && echo "${user_zone};${user_iface}" > /sys/class/net/${IFNAME}/ifalias
		fi
	fi

	#echo "bindif:${bindif}" > /dev/console
	binddev=$(zone_get_device_byif "${bindif}")
	#echo "binddev:${binddev}" > /dev/console
	# the ipset named with ipset_szone_|ipset_dzone_" is created by fw-policy-manager
	for ipset in `ipset list -n | grep -E "ipset_szone_|ipset_dzone_"`
	do
		ret=`ipset test ${ipset} ${binddev};echo $?`
		if [ "$ret" == "0" ];then
			# the binddev is in this ipset, the vpn dev which is bind the binddev also need to add in the ipset
			ipset add ${ipset} ${IFNAME} -exist
		fi
	done
}

do_ifdown()
{
	# record the relationship of vpniface and bindif in vpn_iface_bindif
	# for ER/FW the bind_if is user_if
	case "${PPPD_TYPE}" in
		"server")
			#echo "server ifdown: ${IFNAME}" > /dev/console
			config_load ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE}
			config_foreach remove_iface "bindif" ${IFNAME} "server_iface"
			config_foreach config_clear;;
		"client")
			#echo "client ifdown: ${IFNAME}" > /dev/console
			config_load ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE}
			config_foreach remove_iface "bindif" ${IFNAME} "client_iface"
			config_foreach config_clear;;
		"ipsec")
			#echo "do_ifdown() ipsec ifdown: ${IFNAME}" > /dev/console
			config_load ${UCI_VPN_IFACE_DIR}/${UCI_VPN_IFACE_BINDIF_FILE}
			config_foreach remove_iface "bindif" ${IFNAME} "ipsec_iface"
			config_foreach config_clear;;
	esac
	uci -c ${UCI_VPN_IFACE_DIR} commit ${UCI_VPN_IFACE_BINDIF_FILE}

	# del the device from the iface_group ipset
	# only the FW has the user_zone, so this code is only used for FW
	if [ "${PPPD_TYPE}" != "ipsec" ];then
		config_load /etc/config/zone
		config_foreach get_user_zone "zone"
		config_foreach config_clear
	fi
	if [ -n "${user_zone}" -a "" != "${user_zone}" ];then
		#echo "ipset del ${user_zone}_IFACES ${IFNAME} -exist" > /dev/console
		ipset del ${user_zone}_IFACES ${IFNAME} -exist
	fi

	# the ipset named with ipset_szone_|ipset_dzone_" is created by fw-policy-manager
	for ipset in `ipset list -n | grep -E "ipset_szone_|ipset_dzone_"`
	do
		ret=`ipset test ${ipset} ${IFNAME};echo $?`
		if [ "$ret" == "0" ];then
			# the vpn dev is down, if the dev is in this ipset, it need to be deleted
			ipset del ${ipset} ${IFNAME} -exist
		fi
	done
}
