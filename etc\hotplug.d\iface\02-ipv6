#!/bin/sh

include /lib/network

. /lib/functions.sh
. /lib/functions/network.sh
. /usr/share/libubox/jshn.sh
. /lib/netifd/netifd-proto.sh

get_iface_gateway() {
	local iface="$1"
	local var="$2"
	gwstr=`ubus call network.interface.$iface status | grep -A2 '"target": "::"' | grep '"nexthop": '`
	gwstr=${gwstr##*: \"}
	gwstr=${gwstr%%\"}

	eval "export -- \"$var=$gwstr\""
}

get_iface_l3_device() {
	local iface="$1"
	local var="$2"
	ifstr=`ubus call network.interface.$iface status | grep '"l3_device": '`
	ifstr=${ifstr##*: \"}
	ifstr=${ifstr%%\",}

	eval "export -- \"$var=$ifstr\""
}

get_gw_route_gateway() {
	local gw_route=`ip -6 route|grep default`
	local var="$1"

	[ -n "$gw_route" ] && {
		local oIFS="$IFS";IFS=" "; set -- $gw_route;IFS="oIFS"
		eval "export -- \"$var=$3\""
	}
}

get_gw_route_dev () {
	local gw_route=`ip -6 route|grep default`
	local var="$1"

	[ -n "$gw_route" ] && {
		local oIFS="$IFS";IFS=" "; set -- $gw_route;IFS="oIFS"
		eval "export -- \"$var=$5\""
	}
}

setup_wanv6_gateway() {
	local ifname

	config_load network
	config_get ifname wanv6 ifname
	local gw_route=`route -A inet6 | grep ::/0`
	[ -z ${gw_route} ] && {
		local gateway=`cat /proc/sys/net/ipv6/conf/$ifname/default_gateway`
		[ ${#gateway} -ne 0 ] && route -A inet6 add default gw "$gateway" dev "$ifname" metric 1024
	}
}

[ "$ACTION" == "ifup" -a "$INTERFACE" == "wanv6" ] && {
    local proto
    local pd_mode
    local prefix

    config_load network
    config_get proto wanv6 proto
    config_get pd_mode wanv6 pd_mode

    if [ "$proto" == "dhcpcv6" -o "$proto" == "pppoev6" ]; then
        if [ "$pd_mode" == "prefix" ]; then
            prefix=`cat /tmp/dhcp6c/prefix.info`
            if [ $? -ne 0 ]; then
                exit 0
            else
                uci set network.lanv6.prefix="$prefix"
            fi
            ubus call network.interface.lanv6 down
            ubus call network.interface.lanv6 up
        fi
    fi

    echo "1" > /proc/sys/net/ipv6/conf/all/forwarding
    setup_wanv6_gateway
}

[ "$ACTION" == "ifdown" -a "$INTERFACE" == "wanv6" ] && {
	local ifname
	get_gw_route_dev ifname
	[ -n "$ifname" ] && route -A inet6 del default dev "$ifname" metric 1024
}

