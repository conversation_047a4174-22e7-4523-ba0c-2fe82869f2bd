#!/bin/sh /etc/rc.common
# Copyright (C) 2006 OpenWrt.org

START=99

start() {
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ifstat-cloud start", "wait_time": 10, "type": "online", "action":"add" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ifstat-cloud stop", "wait_time": 10, "type": "offline", "action":"add" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ifstat-cloud ifstat_get", "wait_time": 10, "type": "timing_report", "action":"add" }'
	ubus call nms_report_cache statistics_parms_reg '{"type":"interfaceStats", "get_cb":"lua /usr/lib/lua/ifstat/ifstat_helper.lua reportflowdata_get", "success_cb":"lua  /usr/lib/lua/ifstat/ifstat_helper.lua reportflowdata_clear" }'
}

stop() {
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ifstat-cloud start", "wait_time": 10, "type": "online", "action":"delete" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ifstat-cloud stop", "wait_time": 10, "type": "offline", "action":"delete" }'
	ubus call cloudclient "register_cmd_callback" '{ "register_notify_cmd": "ifstat-cloud ifstat_get", "wait_time": 10, "type": "timing_report", "action":"delete" }'
}

restart()
{
	stop
	start
}

reload()
{
	restart   
}
