local E = require("luci.http")
local o = require("luci.model.uci")
local e = require("luci.torchlight.debug")
local e = require("luci.torchlight.error")
local f = require("luci.torchlight.model")
local t = require("luci.torchlight.util")
local n = require("luci.torchlight.lib.shell")
local N = require("luci.util")
local h = require("luci.fs")
local y = require("luci.torchlight.validator")
local p = require("socket")
local s = require "luci.torchlight.uac.jcs"
local i = require("luci.model.userconfig")
local n = require("luci.sys")
module("luci.controller.ds", package.seeall)
KEY_METHOD = "method"
KEY_TYPE = "table"
KEY_NAME = "name"
KEY_PARA = "para"
KEY_COUNT = "count"
KEY_FILT_PARA = "filter"
KEY_INSERT_INDEX = "insert_index"
KEY_START = "start"
KEY_END = "end"
METHOD_DO = "do"
METHOD_ADD = "add"
METHOD_DELETE = "delete"
METHOD_MODIFY = "set"
METHOD_GET = "get"
SLP_UNIQUE_ID = "slp_unique_id"
KEY_FILTER_OPTIONS_OP = "filter_options"
DIFF_KEY_MOD = "modify"
DIFF_KEY_DEL = "delete"
DIFF_KEY_ADD = "add"
DIFF_KEY_NAME = "name"
DIFF_KEY_TYPE = "type"
DIFF_KEY_PARA = "para"
DIFF_KEY_FILTER_PARA = "filter"
DIFF_KEY_INSERT_INDEX = "insert_index"
DIFF_KEY_ALL = "*all*"
diff_handler_key = {
    parser = "parser",
    compartor = "compartor",
    prefix = "prefix"
}
KEYWORD_USERLIST = "userlist"
KEYWORD_FUNCPATH = "funcpath"
HTTP_RAW_JSON_DATA = nil
UCI_OPT_TYPE_OPTION = "option"
UCI_OPT_TYPE_LIST = "list"
uci_option_type_table = {}
keyword_filter_options_data = {}
json_datatype_table = {}
keyword_action_table = {}
keyword_data_table = {}
keyword_count_table = {}
keyword_set_data_table = {}
keyword_del_data_table = {}
keyword_add_data_table = {}
keyword_force_secname_table = {}
keyword_force_sectype_table = {}
USERLIST_UCI_SEC_SEP = ":"
secname_userlist = {}
sectype_userlist = {}
PC_UCI = "cfg_web_record"
APP_UCI = "cfg_app_record"
KEY_USR_CFG_NUM = "usr_cfg_num"
CLOUD_SYNC_UCI = "cloud_sync"
PROC_NET_VLAN_DIR = "/proc/net/vlan/"
BR_FILE = "bridge_packet_to_wan"
IS_BR_MODE = "bridge_packet_to_wan = 1"
local function u(n)
    local l = string.reverse(n)
    local r = nil
    local t = nil
    r, t = string.find(l, "_")
    if nil == r or nil == t then return e.EINVARG end
    local r = string.len(n) - t + 1
    local t = string.sub(n, 1, r - 1)
    local n = string.sub(n, r + 1, string.len(n))
    if nil == n or nil == t or nil == tonumber(n) then
        n = nil
        t = nil
        return e.EINVARG
    end
    return e.ENONE, t, n
end
UCI_SEC_LIMIT_EXT = {}
function register_sectype_limit(n, t, r)
    if type(n) ~= "string" or type(t) ~= "string" or type(r) ~= "number" then
        return false
    end
    local e = UCI_SEC_LIMIT_EXT[n] or {}
    UCI_SEC_LIMIT_EXT[n] = e
    e[t] = e[t] or r
    return true
end
function register_secname_userlist(t, n, e)
    if type(e) ~= "table" or type(n) ~= "string" or type(t) ~= "string" then
        return false
    end
    secname_userlist[t .. USERLIST_UCI_SEC_SEP .. n] = e
    return true
end
function register_sectype_userlist(e, n, t)
    if type(t) ~= "table" or type(n) ~= "string" or type(e) ~= "string" then
        return false
    end
    sectype_userlist[e .. USERLIST_UCI_SEC_SEP .. n] = t
    return true
end
function register_exclude_filepaths(e)
    if type(e) ~= "string" and type(e) ~= "table" then return false end
    local n = require("luci.torchlight.setting")
    e = type(e) ~= "table" and {e} or e
    for t, e in ipairs(e) do table.insert(n.EXCLUDE_FILE_PATHS, e) end
    return true
end
function do_checkauth(t, n)
    if type(t) ~= "table" or type(n) ~= "string" then return e.EINVARG end
    if n == METHOD_ADD then
        return do_add_authcheck(t)
    elseif n == METHOD_DELETE then
        return do_delete_authcheck(t)
    elseif n == METHOD_MODIFY then
        return do_modify_authcheck(t)
    else
        return e.EINVARG
    end
end
function user_registered(e, e, e) return true end
function do_add_authcheck(n)
    if type(n) ~= "table" then return e.EINVARG end
    for n, t in pairs(n) do
        if not user_registered(n, t[KEY_TYPE], false) then
            return e.EFORBID
        end
    end
    return e.ENONE
end
function do_delete_authcheck(n)
    if type(n) ~= "table" then return e.EINVARG end
    for r, n in pairs(n) do
        for t, n in pairs(n) do
            n = type(n) ~= "table" and {n} or n
            if t == KEY_TYPE then
                for t, n in pairs(n) do
                    if not user_registered(r, n, false) then
                        return e.EFORBID
                    end
                end
            elseif t == KEY_NAME then
                for t, n in pairs(n) do
                    if not user_registered(r, n, true) then
                        return e.EFORBID
                    end
                end
            end
        end
    end
    return e.ENONE
end
function do_modify_authcheck(n)
    if type(n) ~= "table" then return e.EINVARG end
    for t, n in pairs(n) do
        for n, r in pairs(n) do
            if not user_registered(t, n, true) then return e.EFORBID end
        end
    end
    return e.ENONE
end
module_speclist = {}
capacity_files = {}
KEY_CAPAB = "capability"
KEY_SPEC_INFO = "spec_info"
KEY_CAP_MODULE_VER = "module_version"
KEY_CAP_VERSION_OP = "version"
KEY_PRIVATE_SEC = "private_sec"
KEY_PRIVATE_OP = "private_op"
module_spec_info_data_table = {}
function register_module(e, n)
    if #e > 0 then
        e = type(e) == "string" and {e} or e
        for n, e in pairs(e) do
            if type(e) == "string" then module_speclist[e] = {} end
        end
    else
        if type(e) ~= "string" then return false end
        module_speclist[tostring(e)] = {}
    end
    if not n then return true end
    n = type(n) == "string" and {n} or n
    for n, e in pairs(n) do capacity_files[e] = true end
    return true
end
function module_register_check(e) return module_speclist[e] ~= nil end
function register_module_spec_info(e, n, r, l)
    if type(e) ~= "string" or type(n) ~= "string" or type(l) ~= "string" or
        type(r) ~= "string" then return false end
    local t = module_spec_info_data_table[e]
    if nil ~= t then
        if nil ~= t[n] then return false end
    else
        module_spec_info_data_table[e] = {}
    end
    local t = {}
    t[KEY_PRIVATE_SEC] = r
    t[KEY_PRIVATE_OP] = l
    module_spec_info_data_table[e][n] = t
    return true
end
function do_module_spec_info(e)
    local n = module_spec_info_data_table[e]
    local e = nil
    if nil ~= n and type(n) == "table" then
        local r = o.cursor()
        local t = nil
        for l, n in pairs(n) do
            t = r:get_profile(n[KEY_PRIVATE_SEC], n[KEY_PRIVATE_OP])
            if nil ~= t then
                e = e or {}
                e[l] = tostring(t)
            end
        end
    end
    return e
end
FILTER_UCI_SEC_SEP = ":"
uci_secname_filters = {}
uci_sectype_filters = {}
filter_key = {
    need = "need",
    validator = "validator",
    args = "args",
    injection_test = "injection_test"
}
function register_sectype_filter(e, n, t)
    if type(t) ~= "table" or type(e) ~= "string" or type(n) ~= "string" then
        return false
    end
    uci_sectype_filters[e .. FILTER_UCI_SEC_SEP .. n] = t
    return true
end
function register_keyword_filter_options_data(e, n, t, r, r)
    if type(e) ~= "string" or type(n) ~= "string" or type(t) ~= "string" then
        return false
    end
    local r = keyword_filter_options_data[e] or {}
    keyword_filter_options_data[e] = r
    local r = keyword_filter_options_data[e][n] or {}
    keyword_filter_options_data[e][n] = r
    r[e .. "_" .. n] = {[KEY_FILTER_OPTIONS_OP] = t}
    return true
end
function register_secname_filter(e, t, n)
    if type(n) ~= "table" or type(e) ~= "string" or type(t) ~= "string" then
        return false
    end
    uci_secname_filters[e .. FILTER_UCI_SEC_SEP .. t] = n
    return true
end
function register_keyword_action(e, r, t, l, n)
    return register_keyword_datastruct(e, keyword_action_table, r, t, l, n)
end
function register_keyword_data(n, e, t, r)
    return register_keyword_datastruct(n, keyword_data_table, e, t, r)
end
function register_keyword_count(e, n, t, r)
    return register_keyword_datastruct(e, keyword_count_table, n, t, r)
end
function register_keyword_set_data(r, t, n, e)
    return register_keyword_datastruct(r, keyword_set_data_table, t, n, e)
end
function register_keyword_del_data(n, t, r, e)
    return register_keyword_datastruct(n, keyword_del_data_table, t, r, e)
end
function register_keyword_add_data(r, n, t, e)
    return register_keyword_datastruct(r, keyword_add_data_table, n, t, e)
end
function register_sectype_force_return(e, r, n, t)
    n = n or {}
    t = t or {}
    if type(e) ~= "string" or type(r) ~= "string" or type(n) ~= "table" or
        type(t) ~= "table" then return false end
    local l = keyword_force_sectype_table[e] or {}
    keyword_force_sectype_table[e] = l
    local l = l[r] or {}
    local o = l["list"] or {}
    local l = l["option"] or {}
    for n, e in pairs(n) do table.insert(o, e) end
    for n, e in pairs(t) do table.insert(l, e) end
    keyword_force_sectype_table[e][r] = {["list"] = o, ["option"] = l}
    return true
end
function register_secname_force_return(n, r, e, t)
    e = e or {}
    t = t or {}
    if type(n) ~= "string" or type(r) ~= "string" or type(e) ~= "table" or
        type(t) ~= "table" then return false end
    local l = keyword_force_secname_table[n] or {}
    keyword_force_secname_table[n] = l
    local o = l[r] or {}
    local l = o["list"] or {}
    local o = o["option"] or {}
    for n, e in pairs(e) do table.insert(l, e) end
    for n, e in pairs(t) do table.insert(o, e) end
    keyword_force_secname_table[n][r] = {["list"] = l, ["option"] = o}
    return true
end
function register_keyword_datastruct(n, e, l, r, i, o)
    if type(n) ~= "string" or type(l) ~= "string" or type(r) ~= "string" then
        return false
    end
    e = e or {}
    local a = getfenv(3)._NAME
    local t = e[n] or {}
    e[n] = t
    local t = e[n][l] or {}
    e[n][l] = t
    if nil ~= o then
        if nil == t[o] then
            t[o] = {[KEYWORD_FUNCPATH] = a .. "." .. r, [KEYWORD_USERLIST] = i}
        else
            return false
        end
    else
        t[1] = {[KEYWORD_FUNCPATH] = a .. "." .. r, [KEYWORD_USERLIST] = i}
    end
    return true
end
function register_uci_option_type(e, n, t)
    if type(e) ~= "string" or type(n) ~= "string" or type(t) ~= "table" then
        return false
    end
    local r = uci_option_type_table[e] or {}
    uci_option_type_table[e] = r
    r[n] = t
    return true
end
function register_json_datatype(e, n, t)
    if type(e) ~= "string" or type(n) ~= "string" or type(t) ~= "table" then
        return false
    end
    local r = json_datatype_table[e] or {}
    json_datatype_table[e] = r
    r[n] = t
    return true
end
ds_pre_callback_list = {}
function register_ds_pre_callback(e)
    if "string" ~= type(e) then
        debug_print("register to ds pre callback failed.")
        return false
    end
    local n = getfenv(2)._NAME
    if ds_pre_callback_list[n .. "." .. e] then
        debug_print('"' .. e .. '" has register to ds pre callback.')
        return false
    end
    ds_pre_callback_list[n .. "." .. e] = true
    return true
end
function do_ds_pre_callback(n, e)
    if nil == e or nil == n then return end
    for r, l in pairs(ds_pre_callback_list) do t.funcpath_call(r, n, e) end
end
function do_ds(t)
    local r = {}
    local n = t[KEY_METHOD]
    local l = {
        [METHOD_DO] = do_action,
        [METHOD_ADD] = set_data,
        [METHOD_DELETE] = set_data,
        [METHOD_MODIFY] = set_data,
        [METHOD_GET] = get_data
    }
    t[KEY_METHOD] = nil
    local l = l[n]
    if l then
        do_ds_pre_callback(t, n)
        r = l(t, n)
        if METHOD_GET ~= n and METHOD_DO ~= n and r[e.NAME] == e.ENONE then
            cloud_sync_mark(t, n)
        end
    else
        r[e.NAME] = e.EINVINSTRUCT
    end
    return r
end
function index()
    local e = require("luci.torchlight.setting")
    local e = node("ds")
    e.target = firstchild()
    e.title = _("Administration")
    e.order = 20
    e.sysauth = get_authuser()
    e.sysauth_authenticator = "htmlauth"
    e.ucidata = true
    e.index = true
    entry({"ds", "ds"}, call("ds"), "DataService", 20).index = true
end
function ds()
    local t = {}
    local l = require("luci.json")
    local r = require("luci.http.protocol")
    local n = E.jsondata()
    n = n or l.decode(E.get_raw_data() or "", r.urldecode) or {}
    if not n then
        t[e.NAME] = e.EINVFMT
        write_json(t)
        return
    end
    t = do_ds(n)
    write_json(t)
end
function is_blank_tbl(e)
    if type(e) ~= "table" then return true end
    for e, e in pairs(e) do return false end
    return true
end
function transform_diffdata(n)
    if type(n) ~= "table" then return nil end
    local e, e, e, e, e, e
    local e = {}
    for t, n in pairs(n) do
        e[t] = {}
        for l, n in pairs(n) do
            for r, n in pairs(n) do
                if (not is_blank_tbl(n[DIFF_KEY_PARA]) or DIFF_KEY_DEL == l) and
                    n[DIFF_KEY_NAME] then
                    local r = n[DIFF_KEY_NAME]
                    e[t][r] = {}
                    e[t][r][DIFF_KEY_TYPE] = n[DIFF_KEY_TYPE]
                    e[t][r][DIFF_KEY_PARA] = n[DIFF_KEY_PARA]
                    e[t][r][KEY_METHOD] = l
                end
            end
        end
    end
    local n, n
    for n, t in pairs(e) do if is_blank_tbl(t) then e[n] = nil end end
    if is_blank_tbl(e) then e = nil end
    return e
end
local function d(r, n)
    local t = {}
    if r ~= e.ENONE and nil ~= n and type(n) == "table" and next(n) then
        t[e.MSG] = n
    end
    t[e.NAME] = r
    return t
end
local function I(r, a)
    local f = e.ENONE
    local n = {}
    local l = {}
    local i = {}
    local _ = {}
    local c = o.cursor()
    if type(r) ~= "table" and type(a) ~= "string" then
        n[e.NAME] = e.EINVARG
        return n
    end
    HTTP_RAW_JSON_DATA = r
    f = do_checkauth(r, a)
    if f ~= e.ENONE then
        n[e.NAME] = f
        return n
    end
    local r, a, o, f = diff_data(r, a)
    if r ~= e.ENONE then
        n[e.NAME] = r
        return n
    end
    if not f then
        n[e.NAME] = r
        return n
    end
    r, l = do_chkcb(o, a)
    n = t.merge_table(n, l or {})
    if r ~= e.ENONE then return d(r, l) end
    r = check_filter_options(o, a)
    if r ~= e.ENONE then
        n[e.NAME] = r
        return n
    end
    r = filter_args(o, a)
    if r ~= e.ENONE then
        n[e.NAME] = r
        return n
    end
    r, l = do_beforesavecb(o, a)
    n = t.merge_table(n, l or {})
    if r ~= e.ENONE then return d(r, l) end
    r, l = save_diffdata(c, o)
    n = t.merge_table(n, l or {})
    if r ~= e.ENONE then return d(r, l) end
    for e, n in pairs(o) do
        _ = n[DIFF_KEY_ADD]
        if _ and #_ > 0 then
            for t, n in pairs(_) do
                if n[DIFF_KEY_NAME] then
                    i[e] = i[e] or {}
                    i[e][KEY_NAME] = i[e][KEY_NAME] or {}
                    i[e][KEY_NAME][#i[e][KEY_NAME] + 1] = n[DIFF_KEY_NAME]
                end
            end
        end
        c:commit(e)
    end
    change_factory_status(c)
    local f = require("nixio")
    local E = "UCI_CONFIG"
    local c = require("luci.json")
    local _ = transform_diffdata(o)
    if nil ~= _ then f.setenv(E, c.encode(_)) end
    r, l = do_srvcb(o, a)
    n = t.merge_table(n, l or {})
    if r ~= e.ENONE then return d(r, l) end
    n[e.NAME] = e.ENONE
    n = t.merge_table(n, i or {})
    return n
end
function set_data(e, n)
    i.take_running_cfg_lock("luci-set_data")
    local e = I(e, n)
    i.give_running_cfg_lock("luci-set_data")
    return e
end
function get_data(n)
    local t = {}
    local _ = nil
    local o = 0
    local c = false
    local r = nil
    if type(n) ~= "table" then
        t[e.NAME] = e.EINVARG
        return t
    end
    for l, n in pairs(n) do
        local f = {}
        local u = {}
        local E = false
        local s = false
        local a = n[KEY_COUNT] or {}
        a = type(a) == "string" and {a} or a
        for a, i in pairs(a) do
            if nil ~= n[KEY_FILT_PARA] then
                if false ==
                    check_filter_options_is_register(l, i, n[KEY_FILT_PARA]) then
                    return e.EINVARG
                end
            end
            c, o, r = get_count_data(l, i, n[KEY_FILT_PARA] or {})
            if c then
                f[i] = {[KEY_COUNT] = o[1]}
                E = true
            else
                t = {}
                t[e.NAME] = r or e.EINVARG
                return t
            end
        end
        local i = n[KEY_TYPE] or {}
        i = type(i) == "string" and {i} or i
        for i, t in pairs(i) do
            if nil ~= n[KEY_FILT_PARA] then
                if false ==
                    check_filter_options_is_register(l, t, n[KEY_FILT_PARA]) then
                    return e.EINVARG
                end
            end
            if nil ~= n[KEY_PARA] then
                if nil ~= n[KEY_PARA]["start"] and nil == n[KEY_PARA]["end"] then
                    return e.EINVARG
                elseif nil == n[KEY_PARA]["start"] and nil ~= n[KEY_PARA]["end"] then
                    return e.EINVARG
                elseif nil ~= n[KEY_PARA]["start"] and nil ~= n[KEY_PARA]["end"] then
                    local t = tonumber(n[KEY_PARA]["start"])
                    local n = tonumber(n[KEY_PARA]["end"])
                    if n < t then return e.EINVARG end
                end
            end
            c, _, o, r = get_type_data(l, t, n[KEY_FILT_PARA] or {},
                                       n[KEY_PARA] or {})
            if c then
                f[t] = _
                if o ~= nil then
                    u[t] = o
                    s = true
                end
                E = true
            else
                r = r or e.EINVARG
                return d(r, _)
            end
        end
        local o = n[KEY_NAME] or {}
        o = type(o) == "string" and {o} or o
        for o, t in pairs(o) do
            if nil ~= n[KEY_FILT_PARA] then
                if false ==
                    check_filter_options_is_register(l, t, n[KEY_FILT_PARA]) then
                    return e.EINVARG
                end
            end
            c, _, r = get_name_data(l, t, n[KEY_FILT_PARA] or {},
                                    n[KEY_PARA] or {})
            if c then
                f[t] = _
                E = true
            else
                r = r or e.EINVARG
                return d(r, _)
            end
        end
        if #o == 0 and #i == 0 and #a == 0 then
            t = {}
            t[e.NAME] = e.EINVARG
            return t
        end
        if not E then
            t = {}
            t[e.NAME] = e.EINVARG
            return t
        end
        t[l] = f
        if s then t[l][KEY_COUNT] = u end
    end
    t[e.NAME] = e.ENONE
    return t
end
local function l(t)
    local n = {}
    local r = false
    local r = nil
    if type(t) ~= "table" then
        n[e.NAME] = e.EINVARG
        return n
    end
    for t, l in pairs(t) do
        for l, o in pairs(l) do
            local l, r, t = do_keyword_func(t, l, keyword_action_table, o, r)
            if l then
                if r ~= e.ENONE then
                    n = {}
                    n[e.NAME] = r
                    return n
                end
            else
                n = {}
                n[e.NAME] = e.EINVARG
                return n
            end
            t = t or {}
            N.update(n, t)
        end
    end
    n[e.NAME] = e.ENONE
    return n
end
function do_action(e)
    i.take_running_cfg_lock("luci-do_action")
    local e = l(e)
    i.give_running_cfg_lock("luci-do_action")
    return e
end
function urlencode(n)
    function __encode(e) return string.format("%%%02x", string.byte(e, 1, 1)) end
    return string.gsub(n, "[^%a%d%-%_%.%!%~%*%'%(%)]", __encode)
end
function write_json(e)
    E.prepare_content("application/json")
    E.write_json(e, urlencode)
end
function get_option_type(n, t, e)
    local n = uci_option_type_table[n] or {}
    local n = n[t] or {}
    if n[e] == UCI_OPT_TYPE_LIST then return UCI_OPT_TYPE_LIST end
    return UCI_OPT_TYPE_OPTION
end
function do_keyword_func(a, i, n, o, s, E)
    if type(i) ~= "string" or type(n) ~= "table" then return false end
    o = o or {}
    local n = n[a] or {}
    local n = n[i]
    local r = {}
    local _
    if not n or "table" ~= type(n) then return false end
    for n, f in pairs(n) do
        local l = f[KEYWORD_FUNCPATH]
        if not l then return false end
        local d, c = t.split_module_func(l)
        local n = nil
        if d ~= nil then n = require(d) end
        local n = n and c and n[c] or nil
        assert(n ~= nil, 'Cannot resolve function "' .. l ..
                   '". Is it misspelled or local?')
        local l = f[KEYWORD_USERLIST]
        if l ~= nil then
            local n = t.get_user_group()
            if not t.index(l, n) then return true, e.EFORBID end
        end
        local l, n, o = n(o, a, i, s, E)
        _ = o
        if nil ~= n then
            if type(n) == "table" then
                r = t.merge_table(r, n)
            elseif type(n) == "string" or type(n) == "number" then
                table.insert(r, n)
            end
        end
        if e.ENONE ~= l then return true, l, r end
    end
    return true, e.ENONE, r, _
end
function convert_jsontype(t, r, e)
    e = e or {}
    local n = {}
    local t = json_datatype_table[t] or {}
    local r = t[r]
    if r == nil then return e end
    local o = require("luci.torchlight.jsontype")
    for t, l in pairs(e) do
        n[t] = l
        local e = r[t]
        if e then
            local r = o[e] or nil
            assert(r ~= nil, 'Cannot resolve function "' .. e ..
                       '". Is it misspelled or local?')
            n[t] = r(l)
        end
    end
    return n
end
function get_count_data(t, r, _)
    if type(t) ~= "string" or type(r) ~= "string" then return false, nil end
    local d = o.cursor()
    local n = false
    local a = 0
    local n = {}
    local o = nil
    local i = false
    local l = e.ENONE
    local c = 0
    local c = 0
    local c = 0
    local c = nil
    i, l, a = do_keyword_func(t, r, keyword_count_table, _, c)
    if i then return l == e.ENONE, a, l end
    n = {}
    d:foreach(t, r, function(e)
        o = {}
        e = convert_jsontype(t, r, e)
        o[e[".name"]] = e
        n[#n + 1] = o
    end)
    return true, #n
end
function get_type_data(_, i, a, l)
    if type(_) ~= "string" or type(i) ~= "string" then return false, nil end
    local n = false
    local r = {}
    local n = 0
    local u = false
    local f = e.ENONE
    local t = 1
    local d = 0
    local c = nil
    local E = nil
    local s = false
    local p = nil
    u, f, r, n = do_keyword_func(_, i, keyword_data_table, a, l, p)
    n = n or 0
    if u then return f == e.ENONE, r, n, f end
    local o = o.cursor()
    if l and l[KEY_START] and l[KEY_END] then
        c = tonumber(l[KEY_START])
        E = tonumber(l[KEY_END])
    end
    r = {}
    n = 0
    local e = keyword_force_sectype_table[_] or {}
    local e = e[i] or {}
    local l = e["list"] or {}
    local f = e["option"] or {}
    o:foreach(_, i, function(e)
        n = n + 1
        local n = false
        if a ~= nil and #a > 0 then
            for r, t in ipairs(a) do
                n = true
                for t, r in pairs(t) do
                    n = true
                    if type(r) == "table" then
                        if type(e[t]) == "table" then
                            for l = 1, #r do
                                n = true
                                local e = e[t]
                                for t = 1, #e do
                                    n = true
                                    if e[t] == r[l] then
                                        n = false
                                        break
                                    end
                                    if n == false then
                                        break
                                    end
                                end
                                if n == true then
                                    break
                                end
                            end
                        end
                    else
                        if type(e[t]) == "table" then
                            local e = e[t]
                            for t = 1, #e do
                                if e[t] == r then
                                    n = false
                                    break
                                end
                            end
                        else
                            if e[t] ~= nil and e[t] == r then
                                n = false
                            end
                        end
                    end
                    if n == true then break end
                end
                if n == false then break end
            end
        end
        if n == false then
            if c and E then
                d = d + 1
                if c > d - 1 or E < d - 1 then return end
            end
            r[t] = {}
            for t, n in pairs(l) do e[n] = e[n] or {} end
            for t, n in pairs(f) do e[n] = e[n] or "" end
            r[t][e[".name"]] = e
            t = t + 1
            if KEY_CAP_MODULE_VER == e[".name"] and i == KEY_CAPAB then
                s = true
            end
        end
    end)
    if i == KEY_CAPAB then
        local e = do_module_spec_info(_)
        if nil ~= e then
            r[t] = {}
            r[t][KEY_SPEC_INFO] = e
            t = t + 1
            n = n + 1
        end
        if false == s then
            local e = {}
            e[KEY_CAP_VERSION_OP] = "1.0"
            r[t] = {}
            r[t][KEY_CAP_MODULE_VER] = e
            t = t + 1
            n = n + 1
        end
    end
    if a ~= nil and #a > 0 then n = #r end
    return true, r, n
end
function get_name_data(t, l, c)
    if type(t) ~= "string" or type(l) ~= "string" then return false, nil end
    local n = nil
    local E = o.cursor()
    local a = false
    local i = false
    local r = e.ENONE
    local d = nil
    local o = nil
    local f = nil
    i, r, n = do_keyword_func(t, l, keyword_data_table, f, o)
    if i then return r == e.ENONE, n, r end
    a = true
    n = {}
    n = E:get_all(t, l)
    if n then
        n = convert_jsontype(t, n[".type"], n)
    else
        r, d, o = u(l)
        if nil ~= d and nil ~= o then
            i, r, n = do_keyword_func(t, d, keyword_data_table, nil, o)
            if i then return r == e.ENONE, n, r end
        end
    end
    n = n or {}
    if l == KEY_CAPAB then a, n, _ = get_type_data(t, KEY_CAPAB, c, nil) end
    local e = keyword_force_secname_table[t] or {}
    local e = e[l] or {}
    local r = e["list"] or {}
    local t = e["option"] or {}
    for t, e in pairs(r) do n[e] = n[e] or {} end
    for t, e in pairs(t) do n[e] = n[e] or "" end
    return a, n
end
function section_name_validate(e)
    if type(e) == "string" then
        e = {e}
    elseif type(e) ~= "table" then
        return false
    end
    for n, e in pairs(e) do
        if not e:match("^[a-zA-Z0-9_]+$") then return false end
    end
    return true
end
function diff_data(o, i)
    if type(o) ~= "table" or type(i) ~= "string" then
        return e.EINVARG, nil, nil
    end
    local l = nil
    local r = nil
    local t = nil
    local n = false
    if i == METHOD_ADD then
        l, r, t, n = diff_add_data(o)
    elseif i == METHOD_DELETE then
        l, r, t, n = diff_del_data(o)
    elseif i == METHOD_MODIFY then
        l, r, t, n = diff_modify_data(o)
    else
        return e.EINVARG, nil, nil
    end
    return l, r, t, n
end
function diff_add_data(u)
    if type(u) ~= "table" then return e.EINVARG, nil, nil end
    local f = false
    local N = o.cursor()
    local h = nil
    local i = nil
    local o = nil
    local a = nil
    local r = nil
    local E = nil
    local l = nil
    local c = nil
    local d = nil
    local _ = nil
    local s = nil
    local n = nil
    local t = nil
    function handle_patch(t, _, r, o, i, a, l)
        n = {}
        n[DIFF_KEY_NAME] = r
        n[DIFF_KEY_TYPE] = o
        n[DIFF_KEY_PARA] = i
        n[DIFF_KEY_FILTER_PARA] = a
        n[DIFF_KEY_INSERT_INDEX] = l
        t[#t + 1] = n
        return e.ENONE
    end
    function transform_args(n, t, r)
        if not t or type(r) ~= "string" then
            debug_print(
                "add func parameter is empty or table type is not string.")
            return e.EINVARG
        end
        if n and t and #t > 0 and (type(n) ~= "table" or #n ~= #t) then
            debug_print("add func name length and parameter length is not equal")
            return e.EINVARG
        end
        if #t > 0 then
            if type(n) == "table" then return e.ENONE, n, t end
            n = {}
            for e = 1, #t do
                table.insert(n, r .. "_" ..
                                 (tostring(math.ceil(p.gettime() * 100)):sub(3)) ..
                                 tostring(e))
            end
        else
            if n then return e.ENONE, {n}, {t} end
            n = {r .. "_" .. (tostring(math.ceil(p.gettime() * 100)):sub(3))}
            t = {t}
        end
        return e.ENONE, n, t
    end
    for n, t in pairs(u) do
        i = i or {}
        o = i[n] or {}
        a = o[DIFF_KEY_ADD] or {}
        i[n] = o
        o[DIFF_KEY_ADD] = a
        _ = t[KEY_TYPE]
        s = t[KEY_FILT_PARA]
        l, d, c = transform_args(t[KEY_NAME], t[KEY_PARA], _)
        if l ~= e.ENONE then return l end
        for o = 1, #d do
            if keyword_add_data_table[n] and keyword_add_data_table[n][_] then
                l = handle_patch(a, n, d[o], _, c[o], s, t[KEY_INSERT_INDEX])
                f = true
                if l ~= e.ENONE then return l end
            else
                E = {}
                r = {}
                r[DIFF_KEY_NAME] = d[o]
                r[DIFF_KEY_TYPE] = _
                r[DIFF_KEY_PARA] = c[o]
                r[DIFF_KEY_INSERT_INDEX] = t[KEY_INSERT_INDEX]
                if not section_name_validate(d[o]) then
                    return e.EINVARG, nil, nil
                end
                local t = N:get_all(n, r[DIFF_KEY_NAME])
                if t then return e.EENTRYEXIST, nil, nil end
                local e = c[o] or {}
                for t, e in pairs(e) do
                    E[t] = e
                    if get_option_type(n, r[DIFF_KEY_TYPE], t) ==
                        UCI_OPT_TYPE_LIST then
                        E[t] = type(e) == "table" and e or {e}
                    end
                end
                f = true
                a[#a + 1] = r
            end
        end
    end
    return e.ENONE, h, i, f
end
function diff_del_data(f)
    if type(f) ~= "table" then return e.EINVARG, nil, nil end
    local l = nil
    local d = false
    local p = o.cursor()
    local _ = nil
    local a = nil
    local c = nil
    local r = nil
    local t = nil
    local i = nil
    local n = nil
    local o = nil
    local h = nil
    function handle_patch(t, i, r, l, o)
        n = {}
        n[DIFF_KEY_NAME] = r
        n[DIFF_KEY_TYPE] = r
        n[DIFF_KEY_FILTER_PARA] = l
        n[SLP_UNIQUE_ID] = o
        t[#t + 1] = n
        return e.ENONE
    end
    local o = nil
    local s = {}
    local E = 1
    for n, f in pairs(f) do
        a = a or {}
        i = a[n] or {}
        r = i[DIFF_KEY_DEL] or {}
        a[n] = i
        i[DIFF_KEY_DEL] = r
        _ = _ or {}
        c = _[n] or {}
        _[n] = c
        local i = f[KEY_TYPE] or ""
        if keyword_del_data_table[n] and keyword_del_data_table[n][i] then
            l = handle_patch(r, n, i, f[DIFF_KEY_FILTER_PARA], nil)
            d = true
            if l ~= e.ENONE then return l end
        else
            local i = f[KEY_NAME] or {}
            i = type(i) == "table" and i or {i}
            if not section_name_validate(i) then
                return e.EINVARG, nil, nil
            end
            for a, i in pairs(i) do
                t = {}
                local n = p:get_all(n, i)
                if not n then
                    l, table_name, unique_id = u(i)
                    if nil ~= table_name and nil ~= unique_id then
                        if nil == o then
                            o = table_name
                        elseif o ~= table_name then
                            return e.EENTRYNOTEXIST, nil, nil
                        end
                        if nil ~= o then
                            s[E] = unique_id
                            E = E + 1
                        end
                    else
                        return e.EENTRYNOTEXIST, nil, nil
                    end
                    table_name = nil
                    unique_id = nil
                else
                    c[i] = n
                    t[DIFF_KEY_NAME] = i
                    t[DIFF_KEY_TYPE] = n[".type"]
                    d = true
                    r[#r + 1] = t
                end
            end
            local e = f[KEY_TYPE] or {}
            e = type(e) == "table" and e or {e}
            for e, l in pairs(e) do
                p:foreach(n, l, function(e)
                    t = {}
                    t[DIFF_KEY_NAME] = e[".name"]
                    t[DIFF_KEY_TYPE] = l
                    d = true
                    r[#r + 1] = t
                    c[e[".name"]] = e
                end)
            end
        end
    end
    if nil ~= o then
        l = handle_patch(r, h, o, nil, s)
        d = true
        if l ~= e.ENONE then return l end
    end
    return e.ENONE, _, a, d
end
function diff_modify_data(p)
    if type(p) ~= "table" then return e.EINVARG, nil, nil end
    local _ = false
    local h = o.cursor()
    local d = nil
    local a = nil
    local E = nil
    local i = nil
    local n = nil
    local l = nil
    local t = nil
    local t = nil
    local c = nil
    local f = nil
    local o = e.ENONE
    local s = false
    function handle_patch(t, a, r, i, o, l)
        n = {}
        n[DIFF_KEY_NAME] = r
        n[DIFF_KEY_TYPE] = r
        n[DIFF_KEY_PARA] = i
        n[DIFF_KEY_FILTER_PARA] = o
        n[SLP_UNIQUE_ID] = l
        t[#t + 1] = n
        return e.ENONE
    end
    for t, r in pairs(p) do
        a = a or {}
        f = a[t] or {}
        i = f[DIFF_KEY_MOD] or {}
        a[t] = f
        f[DIFF_KEY_MOD] = i
        d = d or {}
        E = d[t] or {}
        d[t] = E
        if r[KEY_TYPE] and r[KEY_PARA] and r[KEY_FILT_PARA] then
            o = handle_patch(i, t, r[KEY_TYPE], r[KEY_PARA], r[KEY_FILT_PARA],
                             nil)
            _ = true
            if o ~= e.ENONE then return o end
        else
            for r, a in pairs(r) do
                n = {}
                c = {}
                s = false
                if r ~= KEY_TYPE and r ~= KEY_PARA and KEY_FILT_PARA ~= r then
                    n[DIFF_KEY_NAME] = r
                    if keyword_set_data_table[t] and
                        keyword_set_data_table[t][r] then
                        n[DIFF_KEY_TYPE] = r
                        n[DIFF_KEY_PARA] = a
                        _ = true
                    else
                        if not section_name_validate(r) then
                            return e.EINVARG, nil, nil
                        end
                        l = h:get_all(t, r)
                        if l == nil then
                            local l = nil
                            local n = nil
                            o, l, n = u(r)
                            if nil ~= l and nil ~= n then
                                o = handle_patch(i, t, l, a, nil, n)
                                _ = true
                                s = true
                                if o ~= e.ENONE then
                                    return o
                                end
                            else
                                return e.EENTRYNOTEXIST, nil, nil
                            end
                        else
                            E[r] = l
                            n[DIFF_KEY_TYPE] = l[".type"]
                            n[DIFF_KEY_PARA] = c
                            for n, e in pairs(a) do
                                if type(e) ~= "table" then
                                    e = tostring(e)
                                end
                                if (l[n] ~= nil and e ~= l[n]) or
                                    (l[n] == nil and e ~= "") then
                                    c[n] = e
                                    _ = true
                                    if get_option_type(t, l[".type"], n) ==
                                        UCI_OPT_TYPE_LIST then
                                        c[n] = type(e) == "table" and e or {e}
                                    end
                                end
                            end
                        end
                    end
                    if false == s then i[#i + 1] = n end
                end
            end
        end
    end
    return e.ENONE, d, a, _
end
function save_collect_data(i, r, e, l)
    local t = o.cursor()
    local e = require("luci.dispatcher")
    local n = e.get_client_info()
    local e = n[e.CI_IS_PC]
    local n = e and PC_UCI or APP_UCI
    local o = l or {}
    local e = 0
    local l = 0
    if not h.isfile("/etc/config/%s" % n) then return true, l end
    if not t:get(n, r) then t:set(n, r, i) end
    for o, i in pairs(o) do
        e = t:get(n, r, o) or 0
        e = tonumber(e)
        if 0 == e then l = l + 1 end
        e = tonumber(e) + 1
        t:set(n, r, o, e)
    end
    t:save(n)
    return true, l
end
local function u(e) return type(e) == "table" and next(e) == nil end
function save_diffdata(l, f)
    if type(l) ~= "userdata" or type(f) ~= "table" then return e.ENONE end
    local n = {}
    local r = nil
    local o = false
    function save_secinfo(n, i, f, o)
        local d = e.ENONE
        if not i then return e.ENONE end
        for a, i in pairs(i) do
            local a = i[DIFF_KEY_NAME]
            local _ = i[DIFF_KEY_TYPE]
            local c = i[DIFF_KEY_PARA] or {}
            local s = i[DIFF_KEY_FILTER_PARA]
            local E = i[SLP_UNIQUE_ID]
            if keyword_set_data_table[n] and f == DIFF_KEY_MOD and
                keyword_set_data_table[n][a] then
                exists, errcode, r = do_keyword_func(n, a,
                                                     keyword_set_data_table, c,
                                                     s, E)
                o = t.merge_table(o, r or {})
                if e.ENONE ~= errcode then d = errcode end
            elseif keyword_add_data_table[n] and f == DIFF_KEY_ADD and
                keyword_add_data_table[n][_] then
                exists, errcode, r = do_keyword_func(n, _,
                                                     keyword_add_data_table, c,
                                                     s, E)
                o = t.merge_table(o, r or {})
                i[DIFF_KEY_NAME] = nil
                o = t.merge_table(o, r or {})
                if e.ENONE ~= errcode then d = errcode end
            else
                l:set(n, a, _)
                for t, e in pairs(c) do
                    l:delete(n, a, t)
                    if u(e) == false then l:set(n, a, t, e) end
                end
            end
        end
        return d, o
    end
    function del_secinfo(i, n, o)
        local a = nil
        local d = nil
        local _ = nil
        if not n then return e.ENONE end
        for c, n in pairs(n) do
            if keyword_del_data_table[i] and
                keyword_del_data_table[i][n[DIFF_KEY_NAME]] then
                d = n[DIFF_KEY_FILTER_PARA]
                _ = n[SLP_UNIQUE_ID]
                exists, a, r = do_keyword_func(i, n[DIFF_KEY_NAME],
                                               keyword_del_data_table, nil, d, _)
                o = t.merge_table(o, r or {})
                if e.ENONE ~= a then return a, o end
            else
                l:delete(i, n[DIFF_KEY_NAME])
            end
        end
        return e.ENONE, o
    end
    function save_insert_data(a, o, _)
        local r = tonumber(o[1][DIFF_KEY_INSERT_INDEX]) + 1
        local i = o[1][DIFF_KEY_TYPE]
        local n = {}
        local t = nil
        l:foreach(a, i, function(e)
            t = {}
            n[#n + 1] = t
            t[DIFF_KEY_NAME] = e[".name"]
            t[DIFF_KEY_TYPE] = i
            t[DIFF_KEY_PARA] = e
        end)
        if r > #n then r = #n + 1 end
        l:delete_all(a, i)
        for e = 1, #o do
            table.insert(n, r, o[e])
            r = r + 1
        end
        for n, e in pairs(n) do
            l:section(a, i, e[DIFF_KEY_NAME], e[DIFF_KEY_PARA])
        end
        return e.ENONE, _
    end
    local o = nil
    local d = nil
    local _ = nil
    local l = e.ENONE
    for i, a in pairs(f) do
        o = a[DIFF_KEY_ADD]
        if o and o[1] and tonumber(o[1][DIFF_KEY_INSERT_INDEX]) ~= nil and
            not (keyword_add_data_table[i] and
                keyword_add_data_table[i][o[1][DIFF_KEY_TYPE]]) then
            l, r = save_insert_data(i, o, n)
            n = t.merge_table(n, r or {})
            if l ~= e.ENONE then return l, n end
        else
            l, r = save_secinfo(i, o, DIFF_KEY_ADD, n)
            n = t.merge_table(n, r or {})
            if l ~= e.ENONE then return l, n end
        end
        _ = a[DIFF_KEY_MOD]
        l, r = save_secinfo(i, _, DIFF_KEY_MOD, n)
        n = t.merge_table(n, r or {})
        if l ~= e.ENONE then return l, n end
        d = a[DIFF_KEY_DEL]
        l, r = del_secinfo(i, d, n)
        n = t.merge_table(n, r or {})
        if l ~= e.ENONE then return l, n end
    end
    return e.ENONE, n
end
function change_factory_status(t)
    local n = f.uciSystem
    local r = f.uciNetwork
    local r = f.uciDeviceInfo
    local r = t:get(n.fileName, n.secName.sys, n.optName.isFactory)
    if r ~= n.optValue.isFactory.yes then return e.ENONE end
    local e = t:set(n.fileName, n.secName.sys, n.optName.isFactory,
                    n.optValue.isFactory.no)
    e = e and t:commit(n.fileName)
    return e
end
function change_to_type_index(l)
    if type(l) ~= "table" then return nil end
    local n = nil
    local e = nil
    local t = {}
    local r = {DIFF_KEY_MOD, DIFF_KEY_DEL, DIFF_KEY_ADD}
    for o, r in pairs(r) do
        diffkey_data = l[r] or {}
        for o, l in pairs(diffkey_data) do
            n = l[DIFF_KEY_TYPE]
            e = t[n] or {}
            t[n] = e
            type_diffkey_data = e[r] or {}
            e[r] = type_diffkey_data
            type_diffkey_data[#type_diffkey_data + 1] = l
        end
    end
    return t
end
function do_callback(c, E, f)
    if type(c) ~= "string" and type(E) ~= "table" then return e.EEXPT end
    local o = require("luci.dispatcher")
    local _ = o.CB_KEY_TYPE_NAME
    local p = o.CB_KEY_TYPE_TYPE
    f = f or {}
    local r = e.ENONE
    local n = {}
    local i = nil
    local u = nil
    local l = nil
    local N = nil
    local I = nil
    local a = nil
    local d = o.context.datacbs or {}
    local h = {DIFF_KEY_MOD, DIFF_KEY_DEL, DIFF_KEY_ADD}
    function call_cb(t, s, u, o, a, i, f, E, c, _)
        local n = a
        local l = nil
        if t == p then n = i end
        local r = d[o] or {}
        local t = r[t] or {}
        local n = t[n] or {}
        local n = n[u]
        if not n then return e.ENONE end
        local t = e.ENONE
        for r, n in pairs(n) do
            local r = require(n.module)
            local r = r[n.func]
            assert(r ~= nil, 'Cannot resolve function "' .. n.func ..
                       '". Is it misspelled or local?')
            assert(type(r) == "function",
                   'The symbol "' .. n.func ..
                       '" does not refer to a function but data ' .. 'of type "' ..
                       type(r) .. '".')
            t, l = r(s, o, a, i, f, E, c, _)
            if t ~= e.ENONE then return t, l end
        end
        return t, l
    end
    for o, s in pairs(E) do
        local f = f[o] or {}
        for p, E in pairs(h) do
            a = s[E] or {}
            if d[o] and d[o][_] and d[o][_][DIFF_KEY_ALL] then
                r, i = call_cb(_, E, c, o, DIFF_KEY_ALL, DIFF_KEY_ALL, nil, nil,
                               s)
                n = t.merge_table(n, i or {})
                if r ~= e.ENONE then return r, n end
            end
            for d, a in pairs(a) do
                l = a[DIFF_KEY_NAME]
                u = a[DIFF_KEY_TYPE]
                N = a[DIFF_KEY_PARA] or {}
                I = a[DIFF_KEY_FILTER_PARA] or {}
                local a = f[l] or {}
                r, i = call_cb(_, E, c, o, l, u, N, a, nil, I)
                n = t.merge_table(n, i or {})
                if r ~= e.ENONE then return r, n end
            end
        end
        local _ = change_to_type_index(s)
        for s, d in pairs(_) do
            for E, _ in pairs(h) do
                a = d[_] or {}
                local E = {}
                for n, e in pairs(a) do
                    l = e[DIFF_KEY_NAME]
                    if f[l] then E[l] = f[l] end
                end
                if #a > 0 then
                    r, i = call_cb(p, _, c, o, nil, s, d[_], E)
                    n = t.merge_table(n, i or {})
                    if r ~= e.ENONE then return r, n end
                end
            end
        end
    end
    return r, n
end
function do_chkcb(n, e) return do_callback("chkfunc", n, e) end
function do_srvcb(e, n) return do_callback("srvfunc", e, n) end
function do_beforesavecb(n, e) return do_callback("beforesavefunc", n, e) end
function check_sec_full(e, n)
    if type(e) ~= "string" or type(n) ~= "string" or "" == e or "" == n then
        return false
    end
    local r = o.cursor()
    local t = UCI_SEC_LIMIT_EXT[e] and UCI_SEC_LIMIT_EXT[e][n] or
                  f.UCI_SEC_LIMIT[e] and f.UCI_SEC_LIMIT[e][n]
    if t then
        r:foreach(e, n, function(e) t = t - 1 end)
        return (t <= 0)
    end
    return false
end
function check_filter_options_is_register(n, r, l)
    if nil == l or 0 == #l then
        return true
    else
        local e = keyword_filter_options_data[n]
        if nil == e then return true end
        e = keyword_filter_options_data[n][r]
        if nil == e then return true end
        e = keyword_filter_options_data[n][r][n .. "_" .. r]
        if nil == e then return true end
        e =
            keyword_filter_options_data[n][r][n .. "_" .. r][KEY_FILTER_OPTIONS_OP]
        if nil == e then return true end
        e = t.split_string(e, ',')
        local n = {}
        for t = 1, #e do n[e[t]] = true end
        for e = 1, #l do
            for e, t in pairs(l[e]) do
                if nil == n[e] or false == n[e] then return false end
            end
        end
    end
    return true
end
function check_filter_options(r, n)
    if type(r) ~= "table" then return e.ENONE end
    local i = e.ENONE
    n = n or {}
    local n = nil
    local t = nil
    local a = {DIFF_KEY_MOD, DIFF_KEY_DEL, DIFF_KEY_ADD}
    for o, l in pairs(r) do
        for i, r in pairs(a) do
            diffkey_data = l[r] or {}
            for l, r in pairs(diffkey_data) do
                t = r[DIFF_KEY_NAME]
                n = r[KEY_FILT_PARA]
                if nil ~= n and nil ~= t then
                    if false == check_filter_options_is_register(o, t, n) then
                        return e.EINVARG
                    end
                end
            end
        end
    end
    return i
end
function filter_args(t, l)
    if type(t) ~= "table" then return e.ENONE end
    function filter_section(n, r, l, t, o)
        local r = n .. FILTER_UCI_SEC_SEP .. r
        local l = n .. FILTER_UCI_SEC_SEP .. l
        local n = e.ENONE
        if uci_sectype_filters[l] then
            n = filter(uci_sectype_filters[l], t, o)
            if n ~= e.ENONE then return n end
        end
        if uci_secname_filters[r] then
            n = filter(uci_secname_filters[r], t, o)
            if n ~= e.ENONE then return n end
        end
        return n
    end
    local n = e.ENONE
    l = l or {}
    local r = {DIFF_KEY_MOD, DIFF_KEY_DEL, DIFF_KEY_ADD}
    for t, o in pairs(t) do
        for r, a in pairs(r) do
            local r = o[a] or {}
            for o, r in pairs(r) do
                local o = r[DIFF_KEY_NAME]
                local i = r[DIFF_KEY_TYPE]
                local r = r[DIFF_KEY_PARA] or {}
                local l = l[t] or {}
                local l = l[o] or {}
                if DIFF_KEY_ADD == a and check_sec_full(t, i) then
                    return e.ETABLEFULL
                end
                n = filter_section(t, o, i, r, l)
                if e.ENONE ~= n then return n end
            end
        end
    end
    return n
end
function filter(r, n, i)
    local l = require("luci.util")
    if type(r) ~= "table" or type(n) ~= "table" then return e.ENONE end
    i = i or {}
    for l, a in pairs(n) do
        local r = r[l]
        if r == nil then n[l] = nil end
        if r and r[filter_key.validator] then
            local l = r[filter_key.validator]
            local _ = r[filter_key.args] or {}
            local l, o = t.split_module_func(l)
            local t = nil
            if l ~= nil then t = require(l) end
            local t = t and o and t[o] or nil
            local n = t and t(a, n, i, unpack(_))
            if not t and n ~= nil then
                debug_print(
                    "Please check your register check func whehter exact..")
                debug_print("module name: ")
                debug_print(l)
                debug_print("funcion name: ")
                debug_print(o)
            end
            n = type(n) == "number" and n or type(n) == "boolean" and n and
                    e.ENONE or e.EINVARG
            if n ~= e.ENONE then return n end
            if r[filter_key.injection_test] == true then
                if y.injection_test(a) == true then
                    return e.EFORBID
                end
            end
        end
    end
    return e.ENONE
end
function cloud_sync_mark(e, a)
    local n = nil
    local n = nil
    local t = o.cursor()
    local l = {}
    l["config_list"] = {}
    if nil == e or type(e) ~= "table" or nil == a then return end
    for e, n in pairs(e) do
        local r = {}
        if nil ~= n[KEY_TYPE] then
            table.insert(r, n[KEY_TYPE])
        else
            for e, n in pairs(n) do table.insert(r, e) end
        end
        for r, n in pairs(r) do
            local r = nil
            r = t:get(CLOUD_SYNC_UCI, e, n)
            if r ~= nil then
                r = 0
                local o = {}
                o["module_name"] = e
                o["config_name"] = n
                table.insert(l["config_list"], o)
                if a == METHOD_ADD and keyword_add_data_table[e] and
                    keyword_add_data_table[e][n] then
                    i.take_running_cfg_lock("luci-set_data")
                    t:set(CLOUD_SYNC_UCI, e, n, r)
                    t:commit(CLOUD_SYNC_UCI)
                    i.give_running_cfg_lock("luci-set_data")
                end
                if a == METHOD_MODIFY then
                    i.take_running_cfg_lock("luci-set_data")
                    t:set(CLOUD_SYNC_UCI, e, n, r)
                    t:commit(CLOUD_SYNC_UCI)
                    i.give_running_cfg_lock("luci-set_data")
                end
                if a == METHOD_DELETE and keyword_del_data_table[e] and
                    keyword_del_data_table[e][n] then
                    i.take_running_cfg_lock("luci-set_data")
                    t:set(CLOUD_SYNC_UCI, e, n, r)
                    t:commit(CLOUD_SYNC_UCI)
                    i.give_running_cfg_lock("luci-set_data")
                end
            end
        end
    end
    if table.getn(l["config_list"]) > 0 then
        s.jcsUI2uac('cloud_mngt_sync_module_config_report', s.JCS_OP_TYPE_SET,
                    true, l)
    end
end
function import_cfg_cloud_sync_handle(e)
    local t = nil
    local r = nil
    local l = o.cursor()
    local n = {}
    n["config_list"] = {}
    if nil == e or type(e) ~= "table" then return end
    for o = 1, #e do
        t = e[o]["module_name"]
        r = e[o]["config_name"]
        if nil ~= t and nil ~= r then
            value = l:get(CLOUD_SYNC_UCI, t, r)
            if value ~= nil then
                table.insert(n["config_list"], e[o])
                i.take_running_cfg_lock("import_handler")
                l:set(CLOUD_SYNC_UCI, t, r, 0)
                l:commit(CLOUD_SYNC_UCI)
                i.give_running_cfg_lock("import_handler")
            end
        end
    end
    if table.getn(n["config_list"]) > 0 then
        s.jcsUI2uac('cloud_mngt_sync_module_config_report', s.JCS_OP_TYPE_SET,
                    true, n)
    end
end
