#!/usr/bin/lua

--[[
    新版的IPSec模块会区分路由模式和策略模式（通过字段isRouteMode区分），如果升级过程中发现没有该
	字段，会认为是从旧版本升级至新版本，并会自动默认添加字段isRouteMode = "0"，表示为基于策略的路由（维持和旧版本一致）
]]

local dbg     = require "luci.torchlight.debug"
local uci     = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"

local conf_dir = "/tmp/etc/uc_conf"

local VPN_UCI_FILE = "vpn"
local IPSEC_SECTION_TYPE = "ipsec_connection"

local function ipsec_sync()
	local uci_r = uci.cursor()
	local syc_flag = false
	uci_r:load(conf_dir)

	uci_r:foreach(VPN_UCI_FILE, IPSEC_SECTION_TYPE,
		function(section)
			if nil == section["isRouteMode"] then
				uci_r:set(VPN_UCI_FILE, section[".name"], "isRouteMode", "0")
				syc_flag = true
			end
		end
	)

	if syc_flag then
		-- 如果从旧版本升级过来并且存在基于策略的条目，则默认关闭路由模式，维持和旧版一致
		uci_r:set(VPN_UCI_FILE, "ipsec_global", "route_enable", "off")
		uci_r:commit(VPN_UCI_FILE)
		cfgsync.set_config_changed()
	end
end

ipsec_sync()
