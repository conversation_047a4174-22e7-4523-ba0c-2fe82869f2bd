#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

START=84

start() {
	local stat_enable
	local stat_period
	config_load security_policy
	config_get stat_enable global stat_enable off
	config_get stat_period global stat_period 10

	if [ -x "$(which sfe_switch)" ]; then
		sfe_switch register sp_stats on

		if [  "${stat_enable}"X == "on"X  ]; then
			sfe_switch update sp_stats off
		else
			sfe_switch update sp_stats on
		fi
	fi

	echo "PARAM:"${stat_enable} ${stat_period}
	if [ "${stat_enable}"X == "on"X ]; then
		echo 1 > /proc/security_policy/tx_rx_stat
		sp_stat start ${stat_period} &
	fi
}

stop() {
	echo 0 > /proc/security_policy/tx_rx_stat
	killall -9 sp_stat 2>1
	sp_stat clean 2>1
}

restart() {
	stop
	start
}
