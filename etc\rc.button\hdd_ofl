#!/bin/sh

. /lib/functions.sh

TIMEOUT=4
PID_FILE=/var/run/sleep_hdd_ofl.pid

do_real_pressed_action(){
    echo "${BUTTON} pressed at `date`" >/dev/console

    if [ -f ${PID_FILE} ];then
        read PID < ${PID_FILE} && kill ${PID} && rm -f ${PID_FILE}
    fi;

    sleep "${TIMEOUT}" && /usr/bin/disk -umount &
    echo $! > ${PID_FILE}

    cat ${PID_FILE}
}

do_real_released_action(){
    echo "${BUTTON} released at `date`" >/dev/console

    if [ "${SEEN}" -lt ${TIMEOUT} ]; then
        if [ -f ${PID_FILE} ]; then
            read PID < ${PID_FILE} && kill ${PID} && rm -f ${PID_FILE}
            echo "hdd ofl abort at `date`" >/dev/console
        else
            echo "no ${PID_FILE} found" >/dev/console
        fi;
    fi;
}

case ${ACTION} in
pressed)
    do_real_pressed_action
;;

released)
    do_real_released_action
;;

*)
    #echo "${BUTTON} got unknown action[${ACTION}] at `date`" >/dev/console
;;

esac

exit 0