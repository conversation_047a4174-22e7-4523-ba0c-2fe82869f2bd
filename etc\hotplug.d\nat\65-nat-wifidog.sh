#!/bin/sh 
#/etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file    98-ipsec_4napt.sh 
# brief   This script is executed as part of the hotplug event with
#		  HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# 		  is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# 		  interface is available as INTERFACE, the real device as DEVICE.
# author   <PERSON>
# version  1.0.0
# date     08June15
# histry   arg 1.0.0, 08June15, <PERSON>, Create the file. 

case "$MODULE" in
	NAPT)
		echo "Got an NAPT hotplug clean=\"${CLEAN}\" " 
	if [ "${CLEAN}" == "on" ]
	then	
		if [ -e "/var/run/wifidog.pid" ];then
			echo "/usr/lib/lua/wifidog/wifidog_reload.lua wifidog_nat_reload" > /dev/console
			/usr/lib/lua/wifidog/wifidog_reload.lua wifidog_nat_reload
		fi        
	fi 
	;;

esac

