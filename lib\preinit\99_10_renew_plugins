#!/bin/sh
# Copyright (C) 2006 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

renew_plugins() {	
	# when opkg install plugin, need /var/lock to create lock.
	mkdir -p /var/lock
	
	# if /tmp/uc/config exist, means that firmware was updated.
	if [ -e /tmp/.update_firmware ]; then	
		# renew plugin
		[ -x /usr/sbin/plugin ] && /usr/sbin/plugin renew
	else
		# check factory plugins
		[ -x /usr/sbin/plugin ] && /usr/sbin/plugin check
	fi
}
      
boot_hook_add preinit_main renew_plugins
