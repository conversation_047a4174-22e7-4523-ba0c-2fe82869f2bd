#!/bin/sh

. /lib/functions.sh

_qos_delete_rule() {
    local section=${1}
    config_get valx ${section} if_pong
    config_get valxx ${section} if_ping

    for element in $interfaces; do
        [ "${element}" = "${valx}" ] && {
            uci delete qos.${section}
        }
        [ "${element}" = "${valxx}" ] && {
            uci delete qos.${section}
        }
    done
}

case ${ACTION} in
    DELETE)
        [ -n "${interfaces}" ] && {
            echo "interfaces=$interfaces" >> /tmp/qos_wanhook.log
            interfaces=${interfaces//,/ }
            config_load qos
            config_foreach _qos_delete_rule rule
            uci_commit qos
        }
    ;;
    ADD)
    ;;
    WANMOD)
        /etc/init.d/qos-tplink start
    ;;
    *)
    ;;
esac