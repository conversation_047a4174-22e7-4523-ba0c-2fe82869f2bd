#!/bin/sh /etc/rc.common
#如果SD初始化失败，返回-5 -84 -110时，则重启
#连续重启3次还不能恢复，则不再重启
#如果恢复正常，则重新开始计数

# shellcheck disable=SC2034
START=01

CNT_FILE=/sd_retry
MAX_RETRY_CNT=3

start()
{
    flag=0
    retry_cnt=0

    #依赖CONFIG_EXPORT_SD_CARD_ERR
    if [ ! -f /proc/sys/dev/sd_card_err ]; then
        echo "SD_CARD_ERR is NOT support."
        return
    fi
    sd_err=$(cat /proc/sys/dev/sd_card_err)

    [ "${sd_err}x" == "-5x" ] && { flag=1; }
    [ "${sd_err}x" == "-84x" ] && { flag=1; }
    [ "${sd_err}x" == "-110x" ] && { flag=1; }

    if [ $flag -ne 0 ]; then
        echo "> SD_CHECK: error detected" > /dev/console
        if [ -f $CNT_FILE ]; then
            retry_cnt=$(cat $CNT_FILE)
            echo "> SD_CHECK: current retried cnt $retry_cnt, errno $sd_err" > /dev/console
            if [ $retry_cnt -lt $MAX_RETRY_CNT ]
            then
                retry_cnt=$((retry_cnt+1))
            else
                echo "> SD_CHECK: Fatal error, we can't recovery the SD, errno $sd_err, abort"
                return
            fi
        else
            retry_cnt=1
        fi

        #Action
        echo $retry_cnt > $CNT_FILE && sync
        echo "> SD_CHECK: reboot" > /dev/console
        reboot -f
        sleep 10
    else
        #if recovery, remove the cnt_file
        [ -f $CNT_FILE ] && rm -f $CNT_FILE
        echo "> SD_CHECK: OK" > /dev/console
    fi
}
