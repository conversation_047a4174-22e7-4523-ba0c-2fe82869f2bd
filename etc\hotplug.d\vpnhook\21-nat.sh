#!/bin/sh
. /lib/functions/network.sh
. /lib/zone/zone_api.sh

_delete_rule() {
	local cfg="$1"
	local iface="$2"
	local interface

	interface=$(uci_get nat "$cfg" if)
	echo "cfg=$cfg,iface=$iface,interface=$interface" >> /tmp/nat_wanhook.log
	[ "$interface" == "$iface" ] && {
		uci delete nat.$cfg
		uci_commit nat
	}
}

_delete_rule_vs() {
	local cfg="$1"
	local iface="$2"
	local interface

	interface=$(uci_get firewall "$cfg" if)
	echo "cfg=$cfg,iface=$iface,interface=$interface" >> /tmp/nat_wanhook.log
	[ "$interface" == "$iface" ] && {
		uci delete firewall.$cfg
		uci_commit firewall
	}
}

_add_rule() {
	local iface="$3"
	local naptRuleName="NAT_LAN_$iface"
	local enable="on"
	local sysrule="1"
	local mask="$2"
	local ipaddr="$1"
	local ip="$1/$2"
	echo "naptRuleName is : $naptRuleName,ipaddr is $ipaddr,mask is $mask  "  >> /tmp/nat_wanhook.log
	uci add nat rule_napt
	uci set nat.@rule_napt[-1].name=${naptRuleName}
	uci set nat.@rule_napt[-1].enable=${enable}
	#uci set nat.@rule_napt[-1].mask=${mask}
	#uci set nat.@rule_napt[-1].ipaddr=${ipaddr}
	uci set nat.@rule_napt[-1].ip=${ip}
	uci set nat.@rule_napt[-1].if=${iface}
	uci set nat.@rule_napt[-1].sysrule=${sysrule}
	uci commit nat
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
			echo "interfaces=$interfaces" > /tmp/nat_wanhook.log
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				echo "element=$element"  >> /tmp/nat_wanhook.log
				config_load nat
				config_foreach _delete_rule rule_napt $element
				config_load nat
				config_foreach _delete_rule rule_onenat $element
				config_load firewall
				config_foreach _delete_rule_vs redirect $element
				config_load nat
				config_foreach _delete_rule rule_dmz $element
			done
			cfgSave -m set
		}
	;;
	ADD)
		[ -n "${interfaces}" ] && {
			#echo "sleep 20s................" >> /tmp/nat_wanhook.log
			#sleep 20;
			#echo "sleep 20s finished" >> /tmp/nat_wanhook.log
			echo "interfaces=$interfaces" > /tmp/nat_wanhook.log

			local ipaddr=""
			local subnet=""
			local netmask=""
			local prefix=""

			ipaddr=$(uci get network.lan.ipaddr)
			netmask=$(uci get network.lan.netmask)

			if [ -z "$ipaddr" -o -z "$netmask" ]; then
				echo "lan subnet is null error"  >> /tmp/nat_wanhook.log
				return
			else
				echo "lan ipaddr is : $ipaddr,netmask is : $netmask  "  >> /tmp/nat_wanhook.log
				prefix=$(ipcalc -p $ipaddr $netmask)
				prefix=${prefix#*=}
				subnet=$(ipcalc -n $ipaddr $netmask)
				subnet=${subnet#*=}
				echo "lan prefix is : $prefix,subnet is : $subnet  "  >> /tmp/nat_wanhook.log
			fi

			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				echo "element=$element"  >> /tmp/nat_wanhook.log
				#config_load nat
				_add_rule $subnet $prefix $element
			done
			cfgSave -m set
		}
	;;
	*)
	;;
esac
