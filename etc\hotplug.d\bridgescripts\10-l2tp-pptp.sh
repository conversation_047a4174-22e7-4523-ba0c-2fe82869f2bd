#!/bin/sh
# global variables:
#	RUNDIR RUNFILE CONFIF1 CONFIF2 BRIF ACTION

#################################################
# below defined by yourself                     #
#################################################

origdir=/etc/config
tmpdir=/tmp/vpnl2tppptp  # NOTE: tmpdir should not be /tmp/bridge, /tmp/bridge has been used for other purpose

mkdir -p ${tmpdir}

config="l2tp-client l2tp-server pptp-client pptp-server"

# 1. copy config files to tmpdir
for ff in ${config}; do
	cp -f ${origdir}/${ff} ${tmpdir}/${ff}
done

# 2. modify the config files
case ${ACTION} in
	ADDBR)
		[ -n "${CONFIF2}" -a -n "${BRIF}" ] && {
			cnt1=`sed -rn "/option\s+outif\s+'*${CONFIF2}'*/p"  ${tmpdir}/l2tp-client|wc -l`
			sed -ri "s/option\s+outif\s+'*${CONFIF2}'*/option outif '${BRIF}'/g"  ${tmpdir}/l2tp-client
			
			cnt2=`sed -rn "/option\s+outif\s+'*${CONFIF2}'*/p"  ${tmpdir}/pptp-client|wc -l`
			sed -ri "s/option\s+outif\s+'*${CONFIF2}'*/option outif '${BRIF}'/g"  ${tmpdir}/pptp-client
			
			cnt3=`sed -rn "/option\s+bindif\s+'*${CONFIF2}'*/p"  ${tmpdir}/l2tp-server|wc -l`
			sed -ri "s/option\s+bindif\s+'*${CONFIF2}'*/option bindif '${BRIF}'/g"  ${tmpdir}/l2tp-server
			
			cnt4=`sed -rn "/option\s+bindif\s+'*${CONFIF2}'*/p"  ${tmpdir}/pptp-server|wc -l`
			sed -ri "s/option\s+bindif\s+'*${CONFIF2}'*/option bindif '${BRIF}'/g"  ${tmpdir}/pptp-server
			
			# reference modify
			let cntx=cnt1+cnt2+cnt3+cnt4
			
			cnt=`uci get network.${CONFIF2}.t_reference`
			cnt=${cnt:-0}
			let cnt=cnt-cntx
			[ ${cnt} -lt 0 ] && cnt=0
			uci set network.${CONFIF2}.t_reference=${cnt}
			uci commit network
			
			cnt=`uci get network.${BRIF}.t_reference`
			cnt=${cnt:-0}
			let cnt=cnt+cntx
			uci set network.${BRIF}.t_reference=${cnt}
			uci commit network
		}
		;;
	UPDBR)
		# to do
		;;
	DELBR)
		# to do
		;;
	*)
		;;
esac

# 3. replace the config files
for ff in ${config}; do
	cp -f ${tmpdir}/${ff} ${origdir}/${ff}
done


