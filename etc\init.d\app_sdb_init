#!/bin/sh /etc/rc.common

# shellcheck disable=SC2034
START=92

. /lib/sdb/sdb_lock.sh
. /lib/sdb/sdb_config.sh

start() {
    (
        local sdb_upgrade_type="${2-load}"
        waitcse_status 0 6,9 120
        local extapp_exsit
        extapp_exsit=$(uci get signature_db.extapp.dbname 2>/dev/null)
        if [ x"$extapp_exsit" != "x" ]; then
            sec_db load extapp
            if [ "$?" -ne 0 ]; then
                dbg_info "load extapp failed,skip"
            fi
        fi
        sec_db load app $sdb_upgrade_type
        if [ "$?" -eq 0 ]; then
            is_sdb_inited app
            if [ "$?" -eq 1 ]; then
                SUBSHELL_PID=$(sh -c 'echo $PPID')
                sdb_lock_take app_init $SUBSHELL_PID
                echo "app_profile" >> /tmp/policy_db/db_modified
                echo "sec_app_ids" >> /tmp/policy_db/db_modified
                # 这个脚本中包含csedb_commit，所以不需要重新再提交一次
                sdb_submit
                sdb_lock_give app_init $SUBSHELL_PID
            else
                # 初始化时，统一进行提交
                dbg_info "SDB[app] first load, skip csedb_commit"
            fi
        fi

        # 执行完，该脚本后，无论load是否正确，都设置标志位为1
        set_sdb_init_flag app 1
        if [ x"$extapp_exsit" != "x" ]; then
            set_sdb_init_flag extapp 1
        fi
        sdb_lock_give app_write $SUBSHELL_PID
     ) > /dev/console &
    # cloud-client 通过popen r 打开shell进程，这时候子进程的标准输出
    # 是与cloud-client连接的管道，在xxx_sdb_init异步执行后，当该进程
    # 结束，cloud-client关闭管道。xxx_sdb_init所在的子进程如果输入
    # echo命令，向标准输出传消息，就会导致进程中断。所以，这里将
    # 其的标准输出重定向到/dev/console，避免echo出错
}

stop() {
    sec_db unload app
}
