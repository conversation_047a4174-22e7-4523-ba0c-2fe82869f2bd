#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=90

#Fix bug504902
#ifup the down ipv6-dhcp-iface after bootup
eth6_fail_recovery()
{
	for wan in wan1_eth6 wan2_eth6 wan3_eth6 wan4_eth6; do
		eth6_is_exist=$(ubus list | grep network.interface.${wan})
		if [ -n "$eth6_is_exist" ]; then
			eth6_is_bad=$(ubus call network.interface.${wan} status | grep '"up": false')
			if [ -n "$eth6_is_bad" ]; then
				echo "Try to ifup ${wan}" > /dev/console
				ifup ${wan}
			fi
		fi
	done
}

start()
{
	eth6_fail_recovery

	service_start /usr/sbin/dnsproxyd
	touch /tmp/dnsproxy.ready
	return 0
}

stop()
{
	service_stop /usr/sbin/dnsproxyd
	rm -rf /tmp/dnsproxy.ready
	return 0
}

restart()
{
	stop
	start
}

reload()
{
	restart
}

