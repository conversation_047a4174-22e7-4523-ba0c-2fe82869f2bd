#!/bin/sh
. /lib/zone/zone_api.sh
. /lib/functions/network.sh
. /lib/firewall/fw.sh

local IPT="iptables -w -t nat "
local PRE_AUTH="prerouting_auth"
local TUPLE="freeStrategy"
local URL_CHAIN="freeStrategy_url"
local PRE="PREROUTING"
local NEED_RELOAD="0"

fw_check()
{
	local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
	[ -n "$ret" ] && return 0||return 1
}

item_add()
{
	local name_union=$1
	local name=${name_union%%##*}
	local match_type=${name_union##*##}
	local url=$2
	local src_ip_set=$3
	local dst_ip_set=$4
	local src_port=$5
	local dst_port=$6
	local mac=$7
	local protocol=$8

	local mac_rule=""
	[ "$mac" != "default" ] && mac=${mac//-/:} && mac_rule="-m mac --mac-source $mac"
	local src=""
	[ "$src_ip_set" != "default" ] && src="-s $src_ip_set"
	local src_port_val=""
	[ "$src_port" != "default" ] && src_port=$(echo $src_port | tr '-' ':') && src_port_val="--sport $src_port"

	if ! $IPT -S $PRE_AUTH &>/dev/null;then
			$IPT -N $PRE_AUTH
			$IPT -A $PRE -j $PRE_AUTH
	fi

	if [ "$match_type" == "quintuple" ];then
		if ! $IPT -S $TUPLE &>/dev/null;then
			$IPT -N $TUPLE
			$IPT -I $PRE_AUTH -j $TUPLE
		fi

		local dst=""
		[ "$dst_ip_set" != "default" ] && dst="-d $dst_ip_set"

		local dst_port_val=""
		[ "$dst_port" != "default" ] && dst_port=$(echo $dst_port | tr '-' ':') && dst_port_val="--dport $dst_port"
		protocol=$(echo $protocol | tr '[A-Z]' '[a-z]')
		local proto=""
		if [ $protocol != "all" ];then
			proto="-p $protocol"
		else
			src_port_val=""
			dst_port_val=""
		fi

        iptables -w -t nat -A freeStrategy $proto $src $dst $mac_rule $src_port_val $dst_port_val -j ACCEPT -m comment --comment "xx_${name}_xx"
        iptables -w -A freeStrategy $proto $src $dst $mac_rule $src_port_val $dst_port_val -j ACCEPT -m comment --comment "xx_${name}_xx"
    else #url
        if ! $IPT -S $URL_CHAIN &>/dev/null;then
            $IPT -N $URL_CHAIN
            $IPT -I $PRE_AUTH -j $URL_CHAIN
        fi

        for url_item in ${url}; do {
            local result=$(echo $url_item | awk -F . '{if(NF==4){i=0;while(i<NF){if($i<0 || $i>255) break;i++}if(i==NF){print "good"}else{print "bad"}}else{print "bad"}}')

            if [ $result == "bad" ];then
                local iparray=$url_item
                iparray=$( dnsq -t 1000 -a $url_item | awk '{print $0}')
                [ -z "$iparray" ] && NEED_RELOAD="1" && continue
                for ip in ${iparray}; do {
                    iptables -w -t nat -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $ip -j ACCEPT -m comment --comment "xx_${name}_xx"
                    iptables -w -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $ip -j ACCEPT -m comment --comment "xx_${name}_xx"
                }
                done
                # echo "$iparray" >/tmp/freeStrategy_dir/$name
            else
                iptables -w -t nat -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $url_item -j ACCEPT -m comment --comment "xx_${name}_xx"
                iptables -w -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $url_item -j ACCEPT -m comment --comment "xx_${name}_xx"
            fi
        }
        done
    fi
}

item_delete()
{
	local name_set=$1
	local match_type=""
	local num=0
	for name_union in $name_set;do
		# name##strategy
		local name=${name_union%%##*}
		match_type=${name_union##*##}
		if [ $match_type == "quintuple" ];then
			num=$(iptables -w -t nat -nvL freeStrategy --line-number | grep "\bxx_${name}_xx\b" | wc -l)
			if [ $num != "0" ];then
				local rule_num=$(iptables -w -t nat -nvL freeStrategy --line-number | grep "\bxx_${name}_xx\b" | head -n 1 | awk '{print $1}')
				for i in $(seq 1 ${num});do
					iptables -w -t nat -D freeStrategy $rule_num
				done
				num=$(iptables -w  -nvL freeStrategy --line-number | grep "\bxx_${name}_xx\b" | wc -l)
				[ $num == "0" ] && return 0
				rule_num=$(iptables -w -nvL freeStrategy --line-number | grep "\bxx_${name}_xx\b" | head -n 1 | awk '{print $1}')
				for i in $(seq 1 ${num});do
					iptables -w -D freeStrategy $rule_num
				done
			fi

			local tuple_num=$($IPT -S $TUPLE | wc -l)
			if [ "$tuple_num" -eq 1 ];then
				tuple_num=$($IPT -nvL $PRE_AUTH --line-number | grep "\b$TUPLE\b" |awk '{ print $1}')
				$IPT -D $PRE_AUTH $tuple_num
				$IPT -X $TUPLE
			fi

		else #url
			num=$(iptables -w -t nat -nvL freeStrategy_url --line-number | grep "\bxx_${name}_xx\b" | wc -l)
			if [ $num != "0" ];then
				local rule_num=$(iptables -w -t nat -nvL freeStrategy_url --line-number | grep "\bxx_${name}_xx\b" | head -n 1 | awk '{print $1}')
				for i in $(seq 1 ${num});do
					iptables -w -t nat -D freeStrategy_url $rule_num
				done
				num=$(iptables -w  -nvL freeStrategy_url --line-number | grep "\bxx_${name}_xx\b" | wc -l)
				[ $num == "0" ] && return 0
				rule_num=$(iptables -w -nvL freeStrategy_url --line-number | grep "\bxx_${name}_xx\b" | head -n 1 | awk '{print $1}')
				for i in $(seq 1 ${num});do
					iptables -w -D freeStrategy_url $rule_num
				done
				# rm -f /tmp/freeStrategy_dir/$name
			fi
			local url_num=$($IPT -S $URL_CHAIN | wc -l)
			if [ "$url_num" -eq 1 ];then
				url_num=$($IPT -nvL $PRE_AUTH --line-number | grep "\b$URL_CHAIN\b" |awk '{ print $1}')
				$IPT -D $PRE_AUTH $url_num
				$IPT -X $URL_CHAIN
			fi
		fi
	done

	local pre_num=$($IPT -S $PRE_AUTH | wc -l)
	if [ "$pre_num" -eq 1 ];then
		pre_num=$($IPT -nvL $PRE --line-number | grep "\b$PRE_AUTH\b" |awk '{ print $1}')
		$IPT -D $PRE $pre_num
		$IPT -X $PRE_AUTH
	fi
	#then start it again?
}


_load_rule(){
	local name
	local match_type
	local url
	local src_ip_set
	local dst_ip_set
	local src_port
	local dst_port
	local protocol
	local interface
	local mac
	#local comment
	local enable
	local flag=$2

	config_get name $1 name
	config_get match_type $1 match_type
	[ $match_type == 'url' ] && config_get url $1 url
	config_get src_ip_set $1 src_ip_set
	config_get dst_ip_set $1 dst_ip_set
	config_get src_port $1 src_port
	config_get dst_port $1 dst_port
	config_get mac $1 src_mac
	config_get protocol $1 protocol
	config_get interface $1 interface
	config_get enable $1 enable

	[ $enable == 'on' ] && {
		if ! $IPT -S $PRE_AUTH &>/dev/null;then
			$IPT -N $PRE_AUTH
			$IPT -A $PRE -j $PRE_AUTH
		fi

		#echo "name=$name,match_type=$match_type,url=$url,src_ip_set=$src_ip_set,dst_ip_set=$dst_ip_set,src_port=$src_port,dst_port=$dst_port,mac=$mac,protocol=$protocol,interface=$interface,enable=$enable"
		local mac_rule=""
		[ x$mac != 'x' ] && mac=${mac//-/:} && mac_rule="-m mac --mac-source $mac"
		local src=""
		[ x$src_ip_set != 'x' -a x$src_ip_set != x"IPGROUP_ANY" ] && src="-s $src_ip_set"
		local src_port_val=""
		[ x$src_port != "x" ] && src_port=$(echo $src_port | tr '-' ':') && src_port_val="--sport $src_port"
		
		if [ $match_type == "quintuple" ];then
			if [ $flag == "1" -o $flag == "3" ];then
				if ! $IPT -S $TUPLE &>/dev/null;then
					$IPT -N $TUPLE
					$IPT -I $PRE_AUTH -j $TUPLE
				fi

				local dst=""
				[ x$dst_ip_set != 'x' -a x$dst_ip_set != x"IPGROUP_ANY" ] && dst="-d $dst_ip_set"
				
				local dst_port_val=""
				
				[ x$dst_port != "x" ] && dst_port=$(echo $dst_port | tr '-' ':') && dst_port_val="--dport $dst_port"
				protocol=$(echo $protocol | tr '[A-Z]' '[a-z]')
				local proto=""
				if [ $protocol != "all" ];then
					proto="-p $protocol"
				else
					src_port_val=""
					dst_port_val=""
				fi

                iptables -w -t nat -A freeStrategy $proto $src $dst $mac_rule $src_port_val $dst_port_val -j ACCEPT -m comment --comment "xx_${name}_xx"
                iptables -w -A freeStrategy $proto $src $dst $mac_rule $src_port_val $dst_port_val -j ACCEPT -m comment --comment "xx_${name}_xx"

			fi
		else # url
	
			#urlset create ${name}_free weburl 3 1
			#urlset add $url ${name}_free
			#urlset add '#end#' ${name}_free
			if [ $flag == "1" -o $flag == "2" -o $flag == "3" ];then

				if ! $IPT -S $URL_CHAIN &>/dev/null;then
					$IPT -N $URL_CHAIN
					$IPT -I $PRE_AUTH -j $URL_CHAIN
				fi

                for url_item in ${url}; do {
                    local result=$(echo $url_item | awk -F . '{if(NF==4){i=0;while(i<NF){if($i<0 || $i>255) break;i++}if(i==NF){print "good"}else{print "bad"}}else{print "bad"}}')

                    if [ "$result" == "bad" ];then
                        local iparray=$( dnsq -t 1000 -a $url_item | awk '{print $0}')
                        [ -z "$iparray" ] && NEED_RELOAD="1" && continue
                        for ip in ${iparray}; do {
                            iptables -w -t nat -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $ip -j ACCEPT -m comment --comment "xx_${name}_xx"
                            iptables -w -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $ip -j ACCEPT -m comment --comment "xx_${name}_xx"
                        }
                        done
                    else
                        iptables -w -t nat -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $url_item -j ACCEPT -m comment --comment "xx_${name}_xx"
                        iptables -w -A freeStrategy_url -p tcp $src $mac_rule $src_port_val -d $url_item -j ACCEPT -m comment --comment "xx_${name}_xx"
                    fi
                    # echo "$iparray" >/tmp/freeStrategy_dir/$name
                }
                done
			fi
		fi
	}
}

load_free_rules(){
	config_load authentication
	config_foreach _load_rule free_auth_list $1
	#urlset add "#end#" freestrategy1
	#if [ $count -gt $MAX_ELEMENT_PER_SET ];then
	#	urlset add "#end" freestrategy2
	#fi
}

add_nat_subchain(){
	iptables -w -t nat -N freeStrategy 2>/dev/null
	iptables -w -t nat -N freeStrategy_url 2>/dev/null
	local num=$(iptables -w -t nat -L prerouting_auth --line-number | grep '\bWiFiDog_Outgoing\b' | head -1|awk '{print $1}')
	fw_check "nat" "prerouting_auth" "-j freeStrategy"
	[ x$? == x0 ] && {
		if [ "x$num" == 'x' ];then
			iptables -w -t nat -A prerouting_auth -j freeStrategy
		else
            iptables -w -t nat -I prerouting_auth $num -j freeStrategy
		fi
	}

	fw_check "nat" "prerouting_auth" "-j freeStrategy_url"
	[ x$? == x0 ] && {
		if [ "x$num" == 'x' ];then
			iptables -w -t nat -A prerouting_auth -j freeStrategy_url
		else
            iptables -w -t nat -I prerouting_auth $num -j freeStrategy_url
		fi
	}
}

add_filter_subchain(){
	local num=$(iptables -w -L forward_auth --line-number | grep '\bWiFiDog_Internet\b' | head -1|awk '{print $1}')
	iptables -w -N freeStrategy 2>/dev/null
	iptables -w -N freeStrategy_url 2>/dev/null
	fw_check "filter" "forward_auth" "-j freeStrategy"
	[ x$? == x0 ] && {
		if [ "x$num" == 'x' ];then
			iptables -w -A forward_auth -j freeStrategy
		else
			iptables -w -I forward_auth $num -j freeStrategy
		fi
	}
	fw_check "filter" "forward_auth" "-j freeStrategy_url"
	[ x$? == x0 ] && {
		if [ "x$num" == 'x' ];then
			iptables -w -A forward_auth -j freeStrategy_url
		else
			iptables -w -I forward_auth $num -j freeStrategy_url
		fi
	}
}

check_chain_sequence(){
    #check sequence for nat
    local num_base=$(iptables -w -t nat -L prerouting_auth --line-number | grep '\bWiFiDog_Outgoing\b' | head -1|awk '{print $1}')
    local num_freeStrategy=$(iptables -w -t nat -L prerouting_auth --line-number | grep '\freeStrategy\b' | head -1|awk '{print $1}')
    local num_freeStrategy_url=$(iptables -w -t nat -L prerouting_auth --line-number | grep '\freeStrategy_url\b' | head -1|awk '{print $1}')

    [ "$num_base" == "" ] && {
        num_base=0
    }
    [ "$num_freeStrategy" == "" ] && {
        num_freeStrategy=0
    }
    [ "$num_freeStrategy_url" == "" ] && {
        num_freeStrategy_url=0
    }

    [ $num_base -ne 0 ] && {
        [ $num_base -lt $num_freeStrategy -o $num_base -lt $num_freeStrategy_url ] && {
            [ $num_freeStrategy -ne 0 -a $num_freeStrategy_url -ne 0 ] && {
                if [ $num_freeStrategy_url -gt $num_freeStrategy ];then
                    iptables -w -t nat -D prerouting_auth $num_freeStrategy_url;
                    iptables -w -t nat -D prerouting_auth $num_freeStrategy;
                else
                    iptables -w -t nat -D prerouting_auth $num_freeStrategy;
                    iptables -w -t nat -D prerouting_auth $num_freeStrategy_url;
                fi
            }

            [ $num_freeStrategy -ne 0 -a $num_freeStrategy_url -eq 0 ] && {
                iptables -w -t nat -D prerouting_auth $num_freeStrategy;
            }

            [ $num_freeStrategy_url -ne 0 -a $num_freeStrategy -eq 0 ] && {
                iptables -w -t nat -D prerouting_auth $num_freeStrategy_url;
            }
        }
    }

    #check sequence for filter
    num_base=$(iptables -w -L forward_auth --line-number | grep '\bWiFiDog_Internet\b' | head -1|awk '{print $1}')
    num_freeStrategy=$(iptables -w -L forward_auth --line-number | grep '\freeStrategy\b' | head -1|awk '{print $1}')
    num_freeStrategy_url=$(iptables -w -L forward_auth --line-number | grep '\freeStrategy_url\b' | head -1|awk '{print $1}')

    [ "$num_base" == "" ] &&
    {
        num_base=0
    }
    [ "$num_freeStrategy" == "" ] && {
        num_freeStrategy=0
    }
    [ "$num_freeStrategy_url" == "" ] && {
        num_freeStrategy_url=0
    }

    [ $num_base -ne 0 ] && {
        [ $num_base -lt $num_freeStrategy -o $num_base -lt $num_freeStrategy_url ] && {
            [ $num_freeStrategy -ne 0 -a $num_freeStrategy_url -ne 0 ] && {
                if [ $num_freeStrategy_url -gt $num_freeStrategy ];then
                    iptables -w -D forward_auth $num_freeStrategy_url;
                    iptables -w -D forward_auth $num_freeStrategy;
                else
                    iptables -w -D forward_auth $num_freeStrategy;
                    iptables -w -D forward_auth $num_freeStrategy_url;
                fi
            }

            [ $num_freeStrategy -ne 0 -a $num_freeStrategy_url -eq 0 ] && {
                iptables -w -D forward_auth $num_freeStrategy;
            }

            [ $num_freeStrategy_url -ne 0 -a $num_freeStrategy -eq 0 ] && {
                iptables -w -D forward_auth $num_freeStrategy_url;
            }
        }
    }
}

check_firewall_rules(){
    #check seqence
    check_chain_sequence

    #check and add subchain
    is_portal_chain_exist=`iptables -w -t nat -nvL prerouting_auth|grep WiFiDog_Outgoing`
	[ "$is_portal_chain_exist" != "" ] && {
        fw_check "nat" "prerouting_auth" "-j freeStrategy"
        [ x$? == x0 ] && {
            add_nat_subchain
        }
        fw_check "nat" "prerouting_auth" "-j freeStrategy_url"
        [ x$? == x0 ] && {
            add_nat_subchain
        }
    }

    fw_check "filter" "forward_auth" "-j freeStrategy"
	[ x$? == x0 ] && {
        add_filter_subchain
    }
    fw_check "filter" "forward_auth" "-j freeStrategy_url"
	[ x$? == x0 ] && {
        add_filter_subchain
    }
}

{
    flock 9
    check_firewall_rules
} 9<>/tmp/freeStrategy_lock


action=$1

case $action in
	add)
		{
			flock 9
			item_add "$2" "$3" "$4" "$5" "$6" "$7" "$8" "$9" "${10}"
		} 9<>/tmp/freeStrategy_lock
	;;
	delete)
		{
			flock 9
			item_delete "$2"
		} 9<>/tmp/freeStrategy_lock
	;;
	update)
		{
			flock 9
			item_delete "$2"
			local enable=${11}
			if [ "$enable" == "on" ];then
				item_add "$3" "$4" "$5" "$6" "$7" "$8" "$9" "${10}" "${12}"
			fi
		} 9<>/tmp/freeStrategy_lock
	;;
	nat)
		{
			flock 9
			iptables -w -N freeStrategy_url &>/dev/null
			fw_check "filter" "forward_auth" "-j freeStrategy_url"
			[ x$? == x0 ] && {
				# need to get rule number of wifidog chain
				iptables -w  -I forward_auth -j freeStrategy_url
			}

			iptables -w -F freeStrategy
			iptables -w -F freeStrategy_url
			iptables -w -t nat -F freeStrategy &>/dev/null
			iptables -w -t nat -F freeStrategy_url &>/dev/null
			load_free_rules 3
		} 9<>/tmp/freeStrategy_lock
	;;
	url)
	    iptables -w -N freeStrategy_url &>/dev/null
		fw_check "filter" "forward_auth" "-j freeStrategy_url"
		[ x$? == x0 ] && {
		    # need to get rule number of wifidog chain
			iptables -w  -I forward_auth -j freeStrategy_url
		}
		iptables -w -F freeStrategy_url
		iptables -w -t nat -F freeStrategy_url &>/dev/null
		{
			flock -n 9 || exit 0
			{
				flock 9
				load_free_rules 2
			} 9<>/tmp/freeStrategy_lock
		} 9<>/tmp/freeStrategy_reload_lock
	;;
	tuple)
		{
			flock 9
			iptables -w -F freeStrategy &>/dev/null
			iptables -w -t nat -F freeStrategy &>/dev/null
			load_free_rules 1
		} 9<>/tmp/freeStrategy_lock
	;;
	*)
		exit 1
	;;
	esac

if [ "$NEED_RELOAD" == "1" ];then
    {
        flock -n 9 || exit 0  # another process is working
        exit 1
    } 9<>/tmp/freeStrategy_reload_lock

    exit 0
else
    exit 0
fi
