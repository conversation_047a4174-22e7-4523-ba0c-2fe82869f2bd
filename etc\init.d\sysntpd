#!/bin/sh /etc/rc.common
# Copyright (C) 2011 OpenWrt.org

START=60

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1
SERVICE_PID_FILE=/var/run/sysntpd.pid

start() {
	local peers
	local args="-n"
    local peer1
    local peer2
    local user_flag=0

	config_load system
	config_get peers ntp server
    config_get peer1 ntp pri_ntp
    config_get peer2 ntp snd_ntp

    if [ "$peer1" != "" -a  "$peer1" != "0.0.0.0" ]; then
        append args "-p $peer1"
        user_flag=1
    fi
    if [ "$peer2" != "" -a "$peer2" != "0.0.0.0" ]; then
        append args "-p $peer2"
        user_flag=1
    fi
    [ $user_flag == 0 ] && {
        if [ -n "$peers" ]; then
            local peer
            for peer in $peers; do
                append args "-p $peer"
            done
        fi
    }
	if [ "$args" != "-n" ]; then
		service_start /usr/sbin/ntpd $args
	fi
}

stop() {
	service_stop /usr/sbin/ntpd
}
