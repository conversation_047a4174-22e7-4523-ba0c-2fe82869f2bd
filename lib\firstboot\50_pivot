#!/bin/sh

# Copyright (C) 2006-2010 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

with_fo_pivot() {
    # switch back to squashfs (temporarily)
    # and park the ramdisk ontop of /tmp/root
    # touch a file for luci, when overlay
    # switch taking place, delay web requests
    touch /tmp/luci-switchoverlay
    pivot /rom /mnt
    mount -o move /mnt /tmp/root

    # /overlay is the overlay
    # /rom is the readonly
    main_version=`uname -r |awk -F'.' '{print $1}'`
    minor_version=`uname -r |awk -F'.' '{print $2}'`
    if [ ${main_version} -eq 3 -a ${minor_version} -ge 18 ]||[ ${main_version} -ge 3 ]; then
        mkdir -p /overlay/root /overlay/work
        fopivot /overlay/root /overlay/work /rom
    else
        fopivot /overlay /rom
    fi
    rm -f /tmp/luci-switchoverlay
    echo "0" > /tmp/jffs2_ready
}

boot_hook_add switch2jffs with_fo_pivot
