#!/bin/sh 
#/etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file    ipsec_monitor_tunnel_wrap.sh 
# brief  ipsec模块启动的时候调用的脚本，目的是每个20s检测一下dns和接口变化情况。
# author   <PERSON>
# version  1.0.0
# date     08June15
# histry   arg 1.0.0, 15<PERSON><PERSON><PERSON>, <PERSON>, Create the file. 

{
	flock -x -w 5 30
    [ $? -eq 1 ] && {  exit 1;}   
	
	for i in `seq 10`;do
    	sleep 1
	done

	/lib/ipsec/ipsec_check_domain.sh 
	
	for i in `seq 20`;do
    	sleep 1
	done

	#the first time to check domain maybe cost more time
	while [ 1 ]
	do
		
		for i in `seq 20`;do
    		sleep 1
		done

		/lib/ipsec/ipsec_check_domain.sh
	done	
	flock -u 30
} 30>>/var/lock/ipsec_check_domain_wrap
	