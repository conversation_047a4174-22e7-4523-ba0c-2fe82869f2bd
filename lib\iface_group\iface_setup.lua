local uci = require "luci.model.uci"
local dbg = require "luci.tools.debug"
local sys = require "luci.sys"
local uci_r = uci.cursor()

local ipset_tmp_file = "/tmp/iface_ipset"

local ipset_file = io.open(ipset_tmp_file,"w")
if ipset_file then
    -- NORMAL下的每个iface创建对应的ipset集合
    local iface_list = uci_r:get_list("zone", "NORMAL", "iface")
    if iface_list ~= nil and #iface_list ~= 0 then
        for i, j in pairs(iface_list) do
            local cmd = string.format("create %s_IFACES hash:name -exist\n", j)
            ipset_file:write(cmd)
            local dev = uci_r:get("zone", j, "dev")
            if dev ~= nil then
                cmd = string.format("add %s_IFACES %s -exist\n", j, dev)
                ipset_file:write(cmd)
            end
        end
    end

    -- VPN接口下每个iface创建一个对应得ipset集合，路由模式下可以选择iface
    local ipsec_iface_list = uci_r:get_list("zone", "VPN", "iface")
    if ipsec_iface_list ~= nil and #ipsec_iface_list ~= 0 then
        for i, j in pairs(ipsec_iface_list) do
            local cmd = string.format("create %s_IFACES hash:name -exist\n", j)
            ipset_file:write(cmd)
            local dev = uci_r:get("zone", j, "dev")
            if dev ~= nil then
                cmd = string.format("add %s_IFACES %s -exist\n", j, dev)
                ipset_file:write(cmd)
            end
        end
    end

    -- 创建SDVPN_IFACES，云端添加访问控制时候生效在所有的SD-VPN接口
    ipset_file:write("create SDVPN_IFACES hash:name -exist\n")

    -- 每个zone创建一个对应的ipset集合
    uci_r:foreach("zone", "zone",
        function(section)
            -- zone name
            local name = section[".name"]
            local cmd = string.format("create %s_IFACES hash:name -exist\n", name)
            ipset_file:write(cmd)

            -- 遍历zone下所有iface对应的dev
            local iface_list =  section["iface"]
            if iface_list ~= nil and #iface_list ~= 0 then
                for i, j in pairs(iface_list) do
                    local dev = uci_r:get("zone", j, "dev")
                    if dev ~= nil then
                        cmd = string.format("add %s_IFACES %s -exist\n", name, dev)
                        ipset_file:write(cmd)
                    end
                end
            end

            -- 遍历zone下的dev
            if section["dev_eth"] then
                local cmd = string.format("add %s_IFACES %s -exist\n", name, section["dev_eth"])
                ipset_file:write(cmd)
            end
            if section["dev_1"] then
                local cmd = string.format("add %s_IFACES %s -exist\n", name, section["dev_1"])
                ipset_file:write(cmd)
            end
        end
    )

    -- 创建所有的WAN的ipset集合,LAN/WAN区分机型使用
    local wan_mode = uci_r:get("network", "if_mode", "wan_mode")
    if wan_mode ~= nil then
        ipset_file:write("create WAN_IFACES hash:name -exist\n")
        for i = 1, tonumber(wan_mode) do
            local wanx = "WAN" .. i

            -- 遍历zone下所有iface对应的dev
            local iface_list =  uci_r:get("zone", wanx, "iface")
            if iface_list ~= nil and #iface_list ~= 0 then
                for i, j in pairs(iface_list) do
                    local dev = uci_r:get("zone", j, "dev")
                    if dev ~= nil then
                        cmd = string.format("add WAN_IFACES %s -exist\n", dev)
                        ipset_file:write(cmd)
                    end
                end
            end

            -- 遍历zone下的dev
            local dev_eth = uci_r:get("zone", wanx, "dev_eth")
            local dev_1 = uci_r:get("zone", wanx, "dev_1")
            if dev_eth then
                local cmd = string.format("add WAN_IFACES %s -exist\n", dev_eth)
                ipset_file:write(cmd)
            end
            if dev_1 then
                local cmd = string.format("add WAN_IFACES %s -exist\n", dev_1)
                ipset_file:write(cmd)
            end
        end
    end

    ipset_file:close()
end

restore_cmd = string.format("ipset restore -file %s", ipset_tmp_file)
sys.fork_call(restore_cmd)
