#!/usr/bin/lua

local dbg = require "luci.tools.debug"
local dhcpd6 = require "luci.controller.admin.dhcpd6"

function notify_interface_prefix(ifname, prefix, prefix_len)
	if "string" ~= type(ifname) or "" == ifname or 
		not prefix or "" == prefix or not prefix_len or "" == prefix_len then
		dbg("Interface or prefix is empty")
		return
	end

	dhcpd6.handle_interface_update_event(ifname, nil, nil, prefix, prefix_len)
end

notify_interface_prefix(arg[1], arg[2], arg[3])