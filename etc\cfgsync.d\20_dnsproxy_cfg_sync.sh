#!/usr/bin/lua

local cfgsync = require "luci.torchlight.config_sync"
local dbg = require "luci.torchlight.debug"

--uci config_sync
local function dnsproxy_cfg_update_to_dns()
	local is_change = false
    local uci_r = uci.cursor()
    local old_uciname = "dnsproxy"
    local new_uciname = "dns"
    uci_r:foreach( old_uciname, "rule",
	   function(section)
			local user_section_name = string.sub(section[".name"],1,4)
			dbg("sec_name",section[".name"])
			if user_section_name == "rule" then
                uci_r:section(new_uciname,"rule",section[".name"],section)
                uci_r:delete(old_uciname,section[".name"])
				is_change = true
            end
		end
    )
	if true == is_change then
		uci_r:commit(new_uciname)
		uci_r:commit(old_uciname)
		cfgsync.set_config_changed()
	end
end
dnsproxy_cfg_update_to_dns()
--database config_sync
