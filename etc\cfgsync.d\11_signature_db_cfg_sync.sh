#!/usr/bin/lua

local dbg       = require "luci.torchlight.debug"
local uci       = require "luci.model.uci"
local cfgsync   = require "luci.torchlight.config_sync"

local function change_enable(old_val)
    if "yes" == old_val then
        return "on"
    elseif "no" == old_val then
        return "off"
    else
        -- 已经修改过，返回原值
        return old_val
    end
end

local function complete_time_type(old_val)
    if 1 == #old_val then
        old_val = "0" .. old_val
    end
    return old_val
end

local function num2day(old_val)
    local day_num_to_str = {
        ["0"] = "everyday",
        ["1"] = "monday",
        ["2"] = "tuesday",
        ["3"] = "wednesday",
        ["4"] = "thursday",
        ["5"] = "friday",
        ["6"] = "saturday",
        ["7"] = "sunday",
    }

    if tonumber(old_val) ~= nil then
        return day_num_to_str[old_val]
    end
    return old_val
end

local function set_empty()
    return ""
end

local function add_depend_module(sdbname)
    local depend_module_map = {
        ["ips"] = "cse",
        ["url"] = "cse",
        ["app"] = "cse",
        ["fpiapp"] = "fpi",
        ["av"] = "cse",
        ["asset"] = "tp_nmap",
        ["hotsite"] = "hotsite_search",
    }

    return depend_module_map[sdbname] or ""
end

local change_tables = {
    ["auto_upgrade"] =   {["new_item"]="auto_upgrade_enable",    ["func"]=change_enable},
    ["upgrade_hour"]  =   {["func"]=complete_time_type},
    ["upgrade_minute"] =  {["func"]=complete_time_type},
    ["upgrade_day"] =    {["func"]=num2day},
    ["status"]      =    {["func"]=set_empty}
}

local add_tables = {
    ["depend_module"] = {["func"]=add_depend_module}
}

local function signature_db_sync()
    local module_name = "signature_db"
    local conf_dir = "/tmp/etc/uc_conf"
    local has_changed = 0
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    uci_r.foreach(module_name, "signature_db", function(conf_section)
        for old_item, change_table in pairs(change_tables) do
            if nil ~= conf_section[old_item] then
                local new_value, item_name
                item_name = change_table.new_item or old_item

                -- 如果是需要替换的值，那么使用新的值进行替换
                if nil ~= change_table.func then
                    new_value = change_table.func(conf_section[old_item])
                else
                    new_value = conf_section[old_item]
                end

                uci_r:delete(module_name, conf_section[".name"], old_item)
                uci_r:set(module_name, conf_section[".name"], item_name, new_value)
                has_changed = 1
            end
        end

        for new_item, add_table in pairs(add_tables) do
            if nil == conf_section[new_item] then
                local new_value, item_name
                item_name = new_item

                if nil ~= add_table.func then
                    new_value = add_table.func(conf_section[".name"])
                else
                    new_value = add_table.value
                end

                uci_r:set(module_name, conf_section[".name"], item_name, new_value)
                has_changed = 1
            end
        end
    end)

    if has_changed == 1 then
        uci_r:commit(module_name)
        cfgsync.set_config_changed()
    end

    return
end

signature_db_sync()
