#!/bin/sh

_delete_rule()
{
	local cfg="$1"
	local iface="$2"
	local out_if

	out_if=$(uci_get network "$cfg" if)
	[ "$out_if" == "$iface" ] && {
		uci delete network.$cfg
		uci_commit network
    }
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
		    interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				config_load network
				config_foreach _delete_rule user_route $element
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac
