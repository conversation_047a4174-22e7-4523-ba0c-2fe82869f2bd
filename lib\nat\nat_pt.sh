#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat_pt.sh
# brief
# author   <PERSON> chen
# version  1.0.0
# date     20Apr15
# histry   arg 1.0.0, 21Apr15, <PERSON> chen, Create the file.

. /lib/nat/nat_public.sh

NAT_PT_MAX_EX_PORT_GROUP=5
NAT_PT_ENABLE=

PT_FILTER_CHAINS=
VALID_PT_CHAINS=
VALID_PT_EXT_IFACES=

nat_config_rule_pt() {
	nat_config_get_section "$1" rule_pt { \
		string name "" \
		string enable "" \
		string interface "" \
		string ext_interface ""\
		string trigger_port "" \
		string trigger_protocol "" \
		string external_port "" \
		string external_protocol "" \
	} || return
}

nat_load_rule_pt() {
	local tri_proto="all tcp udp"
	local pub_proto="all tcp udp"

	nat_config_rule_pt "$1"

	[ -z "$rule_pt_interface" ] && {
		echo "interface is not set"
		return 1
	}

	[ -n "$rule_pt_trigger_protocol" ] && {
		rule_pt_trigger_protocol=$(echo $rule_pt_trigger_protocol |tr '[A-Z]' '[a-z]')
	}

	[ -n "$rule_pt_external_protocol" ] && {
		rule_pt_external_protocol=$(echo $rule_pt_external_protocol |tr '[A-Z]' '[a-z]')
	}

	nat__do_pt_rule() {
		local tri_proto=$1
		local pub_proto=$2
		local device=$3

		for tri_port in $rule_pt_trigger_port; do
			local tri_port_start=${tri_port%-*}
			local tri_port_end=${tri_port#*-}

			for ex_port in $rule_pt_external_port; do
				local ex_port_start=${ex_port%-*}
				local ex_port_end=${ex_port#*-}

				[ -n "$tri_port_start" -a -n "$tri_port_end" ] && [ -n "$ex_port_start" -a -n "$ex_port_end" ] && {
					[ ${ex_port_start} -gt ${ex_port_end} ] && {
						local tmp_ex_port=$ex_port_start
						ex_port_start=$ex_port_end
						ex_port_end=$tmp_ex_port
					}

					[ ${tri_port_start} -gt ${tri_port_end} ] && {
						local tmp_tri_port=$tri_port_start
						tri_port_start=$tri_port_end
						tri_port_end=$tmp_tri_port
					}
					local opt_trigger="-j TRIGGER --trigger-proto ${pub_proto} --trigger-type out --trigger-match ${tri_port_start}-${tri_port_end} --trigger-relate ${ex_port_start}-${ex_port_end}"

					for pt_chain in $PT_FILTER_CHAINS; do
						# passby NOT-iface pt_chain
						echo $pt_chain | grep -q "${rule_pt_interface}_.*_pt$" || continue

						#fw add 4 f $pt_chain TRIGGER $ \
						#{ -i ${device} -p ${tri_proto}  --trigger-type out --trigger-proto ${pub_proto} \
						#--trigger-match ${tri_port_start}-${tri_port_end} --trigger-relate ${ex_port_start}-${ex_port_end} }

						# NO need to match in device, for it has matched in target "forward -> $iface_forward"
						#local mt_dev="-i $device"
						#local mt_dev="-o $device"
						local list=${tri_proto}
						[ "$tri_proto" = "all" ] && list="tcp udp"
						for pro in $list; do
							mt_proto="-p $pro"
							NAT_LOG_PRINT "    $IPT_F_ADD $pt_chain $mt_dev $mt_proto $opt_trigger"
							$IPT_F_ADD $pt_chain $mt_dev $mt_proto $opt_trigger
						done

						##### save the valid chain and iface list for PT-init-bottom half
						list_contains VALID_PT_EXT_IFACES $rule_pt_ext_interface || {
							append VALID_PT_EXT_IFACES $rule_pt_ext_interface
							append VALID_PT_CHAINS $pt_chain

						}
					done

				}
			done
		done
	}

	[ -n "$rule_pt_enable" -a "$rule_pt_enable" == "on" ] && {
		devices=$(zone_get_effect_devices "${rule_pt_ext_interface}")
		for device in $devices;do
			list_contains tri_proto ${rule_pt_trigger_protocol} && {
				list_contains pub_proto ${rule_pt_external_protocol} && {
					nat__do_pt_rule $rule_pt_trigger_protocol $rule_pt_external_protocol $device && {
					NAT_PT_ENABLE=1
					}
				}
			}
		done
	}
}

nat_rule_pt_operation() {
	NAT_LOG_PRINT "@install port-trigger rules "
	[ -n "$nat_filter_chains" ] && {
		for fc in $nat_filter_chains; do
			echo "$fc" |grep -q '_pt$' && {
				append PT_FILTER_CHAINS $fc
				fw flush 4 f $fc
			}
		done
	}

	config_foreach nat_load_rule_pt rule_pt

	unset PT_FILTER_CHAINS
	[ -n "$NAT_PT_ENABLE" ] || return 0
	unset NAT_PT_ENABLE

	NAT_LOG_PRINT " info, valid pt chains to trigger: $VALID_PT_CHAINS"
	for pt_chain in $VALID_PT_CHAINS; do
		NAT_LOG_PRINT "    $IPT_F_ADD ${pt_chain} -j TRIGGER --trigger-type in"
		$IPT_F_ADD ${pt_chain} -j TRIGGER --trigger-type in
	done

	# setup port-trigger on nat table
	fw flush 4 n ${NAT_PREROUTNIG_RULE_CHAIN}_${NAT_N_PT_CHAIN}
	NAT_LOG_PRINT " info, valid pt ext ifaces to do dnat: $VALID_PT_EXT_IFACES"
	#ifaces=$(zone_get_effect_ifaces "${rule_pt_interface}")
	for iface in $VALID_PT_EXT_IFACES; do
		device=$(uci_get_state nat env ${iface}_dev)
		[ -n "$iface" -a -n "$device" ] && {
			if_ip=$(uci_get_state nat env "${iface}_ip")
			if [ -n "$if_ip" -a "$if_ip" != "0.0.0.0" ]; then
				NAT_LOG_PRINT "    $IPT_N_ADD ${NAT_PREROUTNIG_RULE_CHAIN}_${NAT_N_PT_CHAIN} -d ${if_ip} -j TRIGGER --trigger-type dnat"
				$IPT_N_ADD ${NAT_PREROUTNIG_RULE_CHAIN}_${NAT_N_PT_CHAIN} -d ${if_ip} -j TRIGGER --trigger-type dnat
			else
				NAT_LOG_PRINT " info, iface $iface ip is invalid, skip setup nat pt item!"
			fi
		}
	done
}

