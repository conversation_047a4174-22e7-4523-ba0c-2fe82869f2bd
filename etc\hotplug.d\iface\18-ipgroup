#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

[ "$DEVICE" == "lo" ] && exit 0

. /lib/functions.sh
. /lib/ipgroup/core.sh

ipgroup_init


case "$ACTION" in
	ifup)
		ipgroup_interface_event
	;;
	ifdown)
		ipgroup_interface_event
	;;
	ifupdate)
		ipgroup_interface_event
	;;
esac
