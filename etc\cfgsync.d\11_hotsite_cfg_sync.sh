#!/usr/bin/lua

--[[
url过滤 和审计配置文件 会引用网站组:
root@TP-LINK:/# uci export audit_policy
config audit_profile 'audit_profile_6704386118'
        option mail_send_enable 'off'
        option ftp_cmd_enable 'off'
        option http_upload_enable 'off'
        list http_url_list 'video'                              //旧分类
        list http_url_list 'game'
        list http_url_list 'finance'
        option ftp_download_enable 'off'
        option ftp_upload_enable 'off'
        option im_login_enable 'off'
        option http_url_enable 'specify'
        option name 'test'
        option http_title_enable 'off'
        option mail_receive_enable 'off'
        option http_download_enable 'off'
        option ref '0'

root@TP-LINK:/# uci export sec_content_conf
package sec_content_conf

config capability 'module_version'
        option version '1.0'

config url_filter_conf 'url_filter_conf_6703238934'
        list web_list 'video'
        list web_list 'game'
        option name 'test'
        option filter_type 'weblist'
        option action 'block'


引入hotsite 网站分类库之后，分类信息发生变化。
旧分类信息要映射到新分类信息，映射关系由/etc/config/hotsite_id_map

root@TP-LINK:/# uci export hotsite_id_map
package hotsite_id_map

config hotsite_id_map 'video'
        list new_id '112'

config hotsite_id_map 'game'
        list new_id '114'
        list new_id '163'

新分类信息的section name为PREDEFINE_HOTSITE_${ID}
]]

local err = require("luci.torchlight.error")
local dbg 	  = require "luci.torchlight.debug"
local uci 	  = require "luci.model.uci"
local cfgsync = require "luci.torchlight.config_sync"
local ref_api = require("luci.torchlight.ref_api")
local hotsitedb = require("sdb.hotsitedb")

local const_websort_hotsite_prefix="PREDEFINE_HOTSITE_"    --websort中hotsite类型secname的前缀

local function hotsite_id_map_sync(config,section_type,list_name)
    local conf_dir = "/tmp/etc/uc_conf"
    local has_changed = 0
    local uci_r = uci.cursor()
    uci_r:load(conf_dir)

    -- audit_policy配置兼容
    uci_r.foreach(config, section_type, function(section)
        if section[list_name] ~= nil then
            if type(section[list_name]) == "string" then
                section[list_name] = {section[list_name]}
            end

            --处理该http_url_list
            local old_websort_secname_list = {}   --要处理的旧网站分类
            local new_websort_secname_list = {}   --要加进来的新网站分类
            local all_websort_secname_list = {}   --包含新网站分类和仍在使用的旧网站分类

            table.foreachi(section[list_name], function(_, websort_secname)
                local old_section = uci_r:get_all("websort", websort_secname)
                hotsite_id_map = uci_r:get("hotsite_id_map", websort_secname, "new_id")

                if hotsite_id_map ~= nil and old_section ~= nil and old_section["flag"] == "old" then  --只有第一次做配置兼容会进这个分支
                    --如果旧网站分类还在使用，也要加进来
                    if old_section["members"] ~= nil then
                        table.insert(all_websort_secname_list, websort_secname)
                    end
                    table.insert(old_websort_secname_list, websort_secname)
                    --至少有一个是旧网站分类
                    has_changed = 1
                    table.foreachi(hotsite_id_map, function(_, new_hotsite_id)
                        table.insert(new_websort_secname_list, const_websort_hotsite_prefix..new_hotsite_id)
                        table.insert(all_websort_secname_list, const_websort_hotsite_prefix..new_hotsite_id)
                    end)
                else
                    table.insert(all_websort_secname_list, websort_secname)  --是个新网站分类的secname，直接加进来
                end
            end)

            --[[dbg.dumptable(old_websort_secname_list)
            dbg.dumptable(new_websort_secname_list)
            dbg.dumptable(all_websort_secname_list)
            ]]

            if has_changed == 1 then
                uci_r:delete(config, section[".name"], list_name)
                uci_r:set_list(config, section[".name"], list_name, all_websort_secname_list)

                --更新引用计数
                ref_api.websort_update_reference(new_websort_secname_list, 1, "refer")

                uci_r:commit(config)
            end
        end
    end)

    if has_changed == 1 then
     	cfgsync.set_config_changed()
    end

    return
end

function remove_old_websort()
    local uci_r = uci.cursor()

    --旧的system条目，若无自定义组成员则直接删除
    --websort中system和数据库保持一致
    uci_r:foreach("websort","websort_list",function(section)
        if section["flag"] == "old" then
            if section["members"] == nil then
                uci_r:delete("websort",section[".name"])
            else
                uci_r:delete("websort",section[".name"],"flag")
                uci_r:set("websort",section[".name"],"comment","该网站分组已被弃用，并会自动映射成新的网站分组，仅自定义组成员仍生效")
            end
        end
    end)
    --仅提交到uci，并不会导致“配置未保存”
    uci_r:commit("websort")
end

local ret = hotsitedb.websort_uci_build_hotsite()   --添加新增的网站分类到websort，以便做配置兼容
if ret ~= err.ENONE then
    --网站分类库未加载，从默认配置中读取网站组条目
    hotsitedb.websort_uci_build_hotsite_from_fac()
end
hotsite_id_map_sync("audit_policy", "audit_profile", "http_url_list")
hotsite_id_map_sync("sec_content_conf", "url_filter_conf", "web_list")
hotsite_id_map_sync("sec_content_conf", "url_filter_conf", "except_web_list")
--删除旧条目，使得映射操作只会执行一次
remove_old_websort()
