#!/bin/bash

# 默认打印等级
DEBUG_LEVEL=${DEBUG_LEVEL:-2}
SHELL_NAME="${0##*/}"
COLOR_PRINT_TAIL="\033[0m"

# debug level
# error 严重错误,可能可以运行
# warn  非期望状态，需要注意
# info  描述程序运行流程，状态，不涉及具体细节
# debug 指明执行过程的细节

dbg_error()
{
    local COLOR_RED_HEAD="\033[31m"
    if [ "$DEBUG_LEVEL" -gt 0 ]; then
        echo -e "${COLOR_RED_HEAD}[error:${SHELL_NAME}]$@${COLOR_PRINT_TAIL}" > /dev/console
    fi
}

dbg_warn()
{
    local COLOR_YELLOW_HEAD="\033[33m"
    if [ "$DEBUG_LEVEL" -gt 1 ]; then
        echo -e "${COLOR_YELLOW_HEAD}[warn:${SHELL_NAME}]$@${COLOR_PRINT_TAIL}" > /dev/console
    fi
}

dbg_info()
{
    local COLOR_BLUE_HEAD="\033[34m"
    if [ "$DEBUG_LEVEL" -gt 2 ]; then
        echo -e "${COLOR_BLUE_HEAD}[info:${SHELL_NAME}]$@${COLOR_PRINT_TAIL}" > /dev/console
    fi
}

dbg_debug()
{
    if [ "$DEBUG_LEVEL" -gt 3 ]; then
        echo "[debug:${SHELL_NAME}]$@" > /dev/console
    fi
}

# 输出带有执行进程id和脚本名字，便于查找问题
dbg_echo()
{
    echo "[$$:${SHELL_NAME}]$@" > /dev/console
}



