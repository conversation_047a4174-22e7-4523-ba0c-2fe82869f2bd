#!/bin/sh

case "$ACTION" in
    ifup) 
		if [ -f /tmp/policy_route.ready ];then
			# . /lib/policy_route/api.sh

			# handle_iface_up $INTERFACE
			echo "[policy_route] iface $INTERFACE iface up" > /dev/console
			/etc/init.d/policy_route reload handle_iface_event $INTERFACE "up"
		fi
        ;;
    ifdown)
		if [ -f /tmp/policy_route.ready ];then
			# . /lib/policy_route/api.sh

			# handle_iface_down $INTERFACE
			echo "[policy_route] iface $INTERFACE iface down" > /dev/console
			/etc/init.d/policy_route reload handle_iface_event $INTERFACE "down"
		fi
    	;;
esac