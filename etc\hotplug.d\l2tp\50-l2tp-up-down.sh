#!/bin/sh
#author wangdechu

#L2TP_ACTION = l2tpup/l2tpdown
#L2TP_NAME 
#L2TP_ISSERVER
#L2TP_LOCALIP
#L2TP_LOCALPORT
#L2TP_REMOTEIP
#L2TP_REMOTEPORT
#L2TP_PROTOCOL
#L2TP_PPPDPID

ipsec_prerouting_chain="ipsec_pre_rule"
IPT="iptables -w -t mangle "
. /lib/functions.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/l2tp/l2tp-functions.sh

ipsecinfo=${pppox_l2tp_main_path}/l2tpserver-ipsec-info #do not change the line

ipsecdone=0

if [ "${L2TP_ACTION}" = "l2tpup" ]; then
	{
		flock -x 199
		if [ ${L2TP_ISSERVER} -eq 1 ]; then
			path=${pppox_l2tp_server_path}/${L2TP_NAME}/config
			ipsecenc=`uci -c ${path} get ${pppox_configname}.${L2TP_NAME}.ipsecenc 2>/dev/null`

			remoteip_list_name=${L2TP_REMOTEIP//./_}
			#echo "l2tp up remoteip_list_name:${remoteip_list_name}" > /dev/console

			mark_list=`uci -c ${pppox_l2tp_main_path} get l2tpserver-ipsec-info.${L2TP_NAME}.${remoteip_list_name} 2>/dev/null`

			for elem in ${mark_list}; do
				if [ "${elem}" = "${L2TP_MARK}" ]; then
					#echo "find the mark that in l2tpserver-ipsec-info, so ipsec is done" > /dev/console
					ipsecdone=1
					break
				fi
			done
			
			[ "${ipsecenc}" = "yes" ] && [ ${ipsecdone} -ne 1 ] && { 
				kill -TERM ${L2TP_PPPDPID}
				echo "l2tp up but the IPSec isn't ready, kill ${L2TP_PPPDPID}" > /dev/console
			}
			[ "${ipsecenc}" = "no" ] && [ ${ipsecdone} -eq 1 ] && {
				kill -TERM ${L2TP_PPPDPID}	
				echo "IPSec is ready but l2tp don't need enc, kill ${L2TP_PPPDPID}" > /dev/console
			}	
		
		fi
	} 199> /tmp/l2tp/l2tp_up.lock
elif [ "${L2TP_ACTION}" = "l2tpdown" ]; then
	{
		flock -x 199
		if [ ${L2TP_ISSERVER} -eq 1 ]; then
			#exit 0
			#path=${pppox_l2tp_server_path}/${L2TP_NAME}/config
			
			#remoteip=`uci -c ${pppox_l2tp_main_path} get l2tpserver-ipsec-info.${L2TP_NAME}.remoteip 2>/dev/null`
			#for elem in ${remoteip}; do
			#	if [ "${elem}" = "${L2TP_REMOTEIP}" ]; then
			#		ipsecdone=1
			#		break
			#	fi
			#done
			
			#[ ${ipsecdone} -ne 1 ] && exit 0
			#ipsecid=`uci -c ${pppox_l2tp_main_path} get l2tpserver-ipsec-info.${L2TP_NAME}.${L2TP_REMOTEIP//./_} 2>/dev/null`
			#[ -n "${ipsecid}" ] && { ipsec stroke down ${l2tp_ipsec_prefix}${L2TP_NAME}{${ipsecid}}
			#	ipsec purgeike
			#}

			remoteip_list_name=${L2TP_REMOTEIP//./_}
			#echo "l2tp down remoteip_list_name:${remoteip_list_name}" > /dev/console

			mark_list=`uci -c ${pppox_l2tp_main_path} get l2tpserver-ipsec-info.${L2TP_NAME}.${remoteip_list_name} 2>/dev/null`

			for elem in ${mark_list}; do
				if [ "${elem}" = "${L2TP_MARK}" ]; then
					#echo "find the mark that in l2tpserver-ipsec-info, so ipsec is still on" > /dev/console
					ipsecdone=1
					break
				fi
			done

			[ ${ipsecdone} -eq 1 ] && {
				echo "L2TP UP DOWN:The l2tp is down but the IPSec is still on, so need to down the tunnel with ${L2TP_MARK}" > /dev/console
				lua /usr/lib/lua/ipsec/ipsec_down_by_mark.lua "down" ${L2TP_MARK}
				sleep 1
			}

			# Check the ipsec_pre_rule, in case of the ipsec is down but the rule is still on
			[  ${L2TP_MARK} != "0x00000000/0xff000000" ] && {

				local mangle_rule_mark=`echo ${L2TP_MARK}|sed 's/0x0/0x/g'`				
				# echo "the L2TP_MARK:${L2TP_MARK}, mangle_rule_mark:${mangle_rule_mark}" > /dev/console
				local ipsec_mark_rule_num=`$IPT -nv --line-number -L ${ipsec_prerouting_chain} | grep ${mangle_rule_mark} | cut -d " " -f 1 | head -1`		
				if [ -n "${ipsec_mark_rule_num}" ];then	
					$IPT -D ${ipsec_prerouting_chain} ${ipsec_mark_rule_num}
					echo "L2TP UP DOWN:The l2tp and IPSec is down but the pre rule is still on, so need to delete the pre rule with ${mangle_rule_mark}" > /dev/console
				fi

				ipsec_mark_rule_num=`$IPT -nv --line-number -L ${ipsec_prerouting_chain} | grep ${mangle_rule_mark} | cut -d " " -f 1 | head -1`		
				if [ -n "${ipsec_mark_rule_num}" ];then	
					$IPT -D ${ipsec_prerouting_chain} ${ipsec_mark_rule_num}
					echo "L2TP UP DOWN:The pre rule with ${mangle_rule_mark} is not delete at the first time, try again!" > /dev/console
				fi
			}
	

		elif [ ${L2TP_ISSERVER} -eq 0 ]; then
			cpath=${pppox_l2tp_client_path}/${L2TP_NAME}/config
			ipsecenc=`uci -c ${cpath} get ${pppox_configname}.${L2TP_NAME}.ipsecenc 2>/dev/null`
			
			#client will redial, no need down the ipsec tunnel
			[ "${ipsecenc}" = "yes" ] && {
				# strongswan升级到5.8.4版本后CHILD SA的名字中增加了 "_child" 后缀
				ipsec stroke down-nb ${l2tp_ipsec_prefix}${L2TP_NAME}_child{*}
				ipsec purgeike
				#ipsec stroke up ${l2tp_ipsec_prefix}${L2TP_NAME}
			}
		fi
	} 199> /tmp/l2tp/l2tp_down.lock
fi
