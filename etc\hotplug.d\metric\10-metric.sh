#!/bin/sh
#author wangdechu

. /lib/functions.sh
. /lib/pppox/pppox-default-variables.sh

static=`uci get system.metric.static 2>/dev/null`
dhcp=`uci get system.metric.dhcp 2>/dev/null`
pppoe=`uci get system.metric.pppoe 2>/dev/null`
l2tp=`uci get system.metric.l2tp 2>/dev/null`
pptp=`uci get system.metric.pptp 2>/dev/null`

opath=/tmp
ostatic=`uci -c ${opath} get system.metric.static 2>/dev/null`
odhcp=`uci -c ${opath} get system.metric.dhcp 2>/dev/null`
opppoe=`uci -c ${opath} get system.metric.pppoe 2>/dev/null`
ol2tp=`uci -c ${opath} get system.metric.l2tp 2>/dev/null`
opptp=`uci -c ${opath} get system.metric.pptp 2>/dev/null`

changed_ifaces=""

set_metric()
{
	local section=$1
	local proto=`uci get network.${section}.proto 2>/dev/null`
	local flag=0
	
	if [ "${proto}" = "static" -a "${ostatic}" != "${static}" ]; then
		flag=1
	elif [ "${proto}" = "dhcp" -a "${odhcp}" != "${dhcp}" ]; then
		flag=1
	elif [ "${proto}" = "pppoe" -a "${opppoe}" != "${pppoe}" ]; then
		flag=1
	fi
	
	[ "${flag}" = "1" ] && {
		eval "uci set network.${section}.metric=\$${proto}"
		append changed_ifaces "$section"
	}
}

config_load network
config_foreach set_metric interface

[ -n "$changed_ifaces" ] && {
	for iface in $changed_ifaces; do
		ifup $iface
	done
}

dol2tp=0
dopptp=0

[ "${ol2tp}" != "${l2tp}" ] && dol2tp=1

[ "${opptp}" != "${pptp}" ] && dopptp=1

[ "${dol2tp}" = "0" -a "${dopptp}" = "0" ] && { rm -f ${opath}/system; exit 0; }

for file in `ls ${pppox_ppppath}/pid 2>/dev/null`; do
	local getisserver=`uci -c ${pppox_ppppath}/pid get ${file}.@info[0].isserver 2>/dev/null`
	getconfigpath=`uci -c ${pppox_ppppath}/pid get ${file}.@info[0].configpath 2>/dev/null`
	if [ -f "${getconfigpath}/config/pptp.type" -a "${dopptp}" = "1" ]; then
		if [ "${getisserver}" = "1" ]; then
			kill -TERM ${file}
		elif [ "${getisserver}" = "0" ]; then
			path=${getconfigpath}/config
			mgr_pid=${path}/mgr
			mgr_pid=$(cat $mgr_pid 2>/dev/null)
			kill -TERM ${mgr_pid}
			kill -TERM `cat ${path}/mgrfather`
			kill -HUP ${file}
		fi
		
	elif [ -f "${getconfigpath}/config/l2tp.type" -a "${dol2tp}" = "1" ]; then
		kill -TERM ${file}
	fi
done

rm -f ${opath}/system
