#!/bin/sh
#author qiufusheng
#brief dealing with interface up/down events

case ${ACTION} in
	master)
		server_iface=`uci get pppoe_server.setting.service_if`
		if [[ "$server_iface" == "$INTERFACE" ]]; then
			#Enable pppoe server
			uci set pppoe_server.setting.enable="on"
			uci commit pppoeserver_global
			/etc/init.d/pppoe-server restart
		fi
	;;
	backup)
		server_iface=`uci get pppoe_server.setting.service_if`
		if [[ "$server_iface" == "$INTERFACE" ]]; then
			#Disable pppoe server
			uci set pppoe_server.setting.enable="off"
			uci commit pppoe_server
			/etc/init.d/pppoe-server restart
		fi
	;;
	stop)
		server_iface=`uci get pppoe_server.setting.service_if`
		if [[ "$server_iface" == "$INTERFACE" ]]; then
			#restart pppoe server
			/etc/init.d/pppoe-server restart
		fi
	;;
	*)
		# do nothing
	;;
esac