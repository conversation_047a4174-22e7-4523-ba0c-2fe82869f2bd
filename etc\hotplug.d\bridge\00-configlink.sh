#!/bin/sh

export RUNDIR=/tmp/bridge

rm -rf ${RUNDIR}/*
mkdir -p ${RUNDIR}

scriptpath=bridgescripts
[ -d /etc/hotplug.d/${scriptpath} ] && {
	for script in $(ls /etc/hotplug.d/${scriptpath} 2>&-); do (
		export RUNFILE=${script}
		touch ${RUNDIR}/${RUNFILE} # RUNFILE should be deleted by script when exit
		[ -f /etc/hotplug.d/${scriptpath}/${script} ] && ( . /etc/hotplug.d/${scriptpath}/${script}; rm -f ${RUNDIR}/${RUNFILE}; )
		[ ! -f /etc/hotplug.d/${scriptpath}/${script} ] && rm -f ${RUNDIR}/${RUNFILE}
	); done
}

while :; do
	cnt=`ls ${RUNDIR}|wc -l 2>&-`
	[ "${cnt}" = "" -o "${cnt}" = "0" ] && {
		break
	}
	sleep 1
done


[ "${ACTION}" = "ADDBR" -a -n "${CONFIF2}" -a -n "${BRIF}" ] && { cfgSave -s; reboot -f; }
