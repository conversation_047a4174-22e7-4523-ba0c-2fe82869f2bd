#!/bin/sh
. /lib/zone/zone_api.sh
. /lib/pptp/pptp-option.sh
. /lib/pppox/pppox-default-variables.sh
. /lib/pptp/pptp-ifdevice-info.sh

#$1: cmd, ifup or ifdown
#$2: the tunnnel name(ifname) of the l2tp tunnel

local cmd=$1
local ifname=$2
local l2tp_client_configname="vpn"
local result=""
local secname=""

get_lac_param() {
    local section=${1}
    config_get result ${section} tunnelname

	[ $result == $ifname -o "l2tp-$result" == $ifname ] && {
		echo "find $result !" > /dev/console
		secname=${section}
	}
}

echo "L2TP: now need to $cmd $ifname" > /dev/console
config_load vpn
config_foreach get_lac_param lac

[ x$secname == 'x' ] && echo "not found corresponding l2tp item" > /dev/console && return 0
case $cmd in
	*ifup)
		uci set ${l2tp_client_configname}.${secname}.enable="on"
		uci commit ${l2tp_client_configname}
		/lib/l2tp/l2tp-reload.sh  modify lac "${secname}"
		lua /lib/l2tp/l2tp-ipsec-setstatus.lua "${secname}" on
	;;
	*ifdown)
		uci set ${l2tp_client_configname}.${secname}.enable="off"
		uci commit ${l2tp_client_configname}
		/lib/l2tp/l2tp-reload.sh  modify lac "${secname}"
		lua /lib/l2tp/l2tp-ipsec-setstatus.lua "${secname}" off
	;;
esac