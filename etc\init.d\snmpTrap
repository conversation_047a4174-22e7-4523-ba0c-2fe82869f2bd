#!/bin/sh /etc/rc.common
# Copyright (C) 2008 OpenWrt.org
START=85

APP=/usr/sbin/snmpJcsTrapd

start() {
	#check if snmpd enable is "on"
	enable_flag=`uci get snmpd.setting.enable`
	if [[ -z "$enable_flag" || "$enable_flag" != "on" ]]; then
		#echo "do not start snmpJcsTrapd" > /dev/console
		return 0
	fi

	$APP &
	#echo "start $APP" > /dev/console
}

stop() {
	local pid=`ps | grep -w snmpJcsTrapd | grep -v grep | awk '{print $1}'`
	#echo "$pid"
	for x in $pid; do
		#echo "kill -9 $x" > /dev/console
		kill -9 $x
	done	
	#echo "stop $APP" > /dev/console
}

restart(){
	stop
	#sleep 1
	start
}
