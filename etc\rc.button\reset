#!/bin/sh

. /lib/functions.sh

# if restorfactory is on-going , just return 1 ?
if [ -f /var/run/restorefactory.pid ];then
    logger -p user.info -t "restorefactory" "restorfactory is on-going!"
    echo "restorefactory is on-going!" >/dev/console
    exit 1
fi;

TIMEOUT=5

PID_FILE=/var/run/sleep_restorefacotry.pid
HWBTN_TEST_FILE=/tmp/test_hwbutton

setup_led_blink() {
    [ -e /sys/class/leds/ ] && {
        . /etc/init.d/led

        config_load system
        config_foreach load_led upgrade_led
        config_clear
    }
}

trigger_restore_event(){
    nms_event_report_notify -k 10001012 -p "{\"resetPattern\":2}">/dev/null
    sleep 2
}

# Original process when reset button pressed
do_real_pressed_action(){
    #echo "${BUTTON} pressed at `date`" >/dev/console

    if [ -f ${PID_FILE} ];then
        read PID < ${PID_FILE} && kill ${PID} && rm -f ${PID_FILE}
    fi;

    sleep "${TIMEOUT}" && trigger_restore_event && setup_led_blink && slprestore -rf &
    echo $! > ${PID_FILE}

    cat ${PID_FILE}
}

# Original process when reset button released
do_real_released_action(){
    #echo "${BUTTON} released at `date`" >/dev/console

    if [ "${SEEN}" -lt ${TIMEOUT} ]; then
        if [ -f ${PID_FILE} ]; then
            read PID < ${PID_FILE} && kill ${PID} && rm -f ${PID_FILE}
            #echo "restorefactor abort at `date`" >/dev/console
        fi;
    fi;
}

RESET_PRESSED=0x04
RESET_RELEASED=0x08

# Do bitwise or with values in test file
# $1 - bit to set
hwbtn_test_file_bitwise_or(){
    btnbits=$1
    read old < ${HWBTN_TEST_FILE} || old=0
    let btnbits=$(( $btnbits | $old ))

    echo $btnbits > ${HWBTN_TEST_FILE}
    sync
}

# When in hardware test mode, replace original event handling process with test process
case ${ACTION} in
pressed)
    if [ -f ${HWBTN_TEST_FILE} ];then
        hwbtn_test_file_bitwise_or $RESET_PRESSED
    else
        do_real_pressed_action
    fi;
;;

released)
    if [ -f ${HWBTN_TEST_FILE} ];then
        hwbtn_test_file_bitwise_or $RESET_RELEASED
    else
        do_real_released_action
    fi;
;;

*)
    #echo "${BUTTON} got unknown action[${ACTION}] at `date`" >/dev/console
;;

esac

exit 0


