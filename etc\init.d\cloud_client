#!/bin/sh /etc/rc.common
# Copyright (C) 2010 <PERSON><PERSON><PERSON> Wich

START=30

. /lib/functions/cloud_client_check.sh

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1

#cloud-client and cloud-server information
CLOUD_CLIENT_BIN="/bin/cloud-client"
CLOUD_SERVER_HOST_NAME="devs-ssmb.tplinkcloud.com.cn"
CLOUD_SERVER_LISTEN_PORT='50443'

#cloud client config file name
CLOUD_STATUS="cloud_status"
CLOUD_CONFIG="cloud_config"
PLUGIN_CONFIG="plugin_config"
PLUGIN_STATUS="plugin_status"

#init action status
cloud_init_status()
{
	#cloud client option name
	local optOwner="owner"
	local optActionStatus="action_status"
	local optErrorCode="err_code"

	uci_set "$CLOUD_STATUS" "$1" "$optOwner" 0
	uci_set "$CLOUD_STATUS" "$1" "$optActionStatus" 0
	uci_set "$CLOUD_STATUS" "$1" "$optErrorCode" 0
}



#renew brand_mac info
renew_brand_mac()
{
	local factory_mac="/rom/www/web-static/dynaform/macFactory.js"
	local cloud_mac="/www/web-static/dynaform/macFactory.js"
	[ -e "$factory_mac" ] || return 0
	
	local renew_time
	config_get renew_time brand_mac renew_time
	if [ -z "$renew_time" ]; then
		echo "current firmware do not support brand_mac online."
		return 0
	fi
	
	local md5_value
	local uci_value
	if [ "$renew_time" == "0" ]; then
		md5_value=$(md5sum "$factory_mac" | awk '{print $1;}')
		uci_set "$CLOUD_CONFIG" brand_mac check_value "$md5_value"
	else
		md5_value=$(md5sum "$cloud_mac" | awk '{print $1;}')
		config_get uci_value brand_mac check_value
		if [ "$uci_value" != "$md5_value" ]; then
			cp "$factory_mac" "$cloud_mac"  2>null
			md5_value=$(md5sum "$factory_mac" | awk '{print $1;}')
			uci_set "$CLOUD_CONFIG" brand_mac check_value "$md5_value" 
			uci_set "$CLOUD_CONFIG" brand_mac renew_time "0"
		fi
	fi	
}

#init config information
init_config()
{
	local offline_reason_value
	config_load "$CLOUD_STATUS"
	config_foreach cloud_init_status router_request
	
	uci_set "$CLOUD_STATUS" client_info connect_status 0
	uci_set "$CLOUD_STATUS" client_info fw_download_progress 0
	uci_set "$CLOUD_STATUS" client_info fw_download_status 3
	uci_set "$CLOUD_STATUS" client_info fw_verify_status 0
	uci_set "$CLOUD_STATUS" client_info disconnect_reason 0
	uci_set "$CLOUD_STATUS" client_info reconnect_time 0
	uci_set "$CLOUD_STATUS" client_info fw_download_errno 0

	uci commit "$CLOUD_STATUS"
	config_load "$CLOUD_CONFIG"	
	uci_set "$CLOUD_CONFIG" device_legality illegal 0
	uci_set "$CLOUD_CONFIG" device_legality illegal_type 0

	uci commit "$CLOUD_CONFIG"

}

check_fit_enable()
{
	local fit_enable

	config_load fit_specific
	config_get fit_enable config fit_enable "off"
	[ "on" == "$fit_enable" ] &&{ \
		echo "true"
		return
	}

	echo "false"
}
#start cloud-client program
start()
{
	local ret=$(check_fit_enable)
	[ "true" == "$ret" ] && { \
		echo "is FIT AP, do not start cloud-client" > /dev/console
		return
	}
	init_config

	check_cloud_cfg

	SERVICE_PID_FILE=/var/run/cloud-client.pid
	service_start $CLOUD_CLIENT_BIN -h $CLOUD_SERVER_HOST_NAME -p $CLOUD_SERVER_LISTEN_PORT

	# Check if daemon is running, if not then re-execute.
	(sleep 1 && service_check $CLOUD_CLIENT_BIN || \
		service_start $CLOUD_CLIENT_BIN -h $CLOUD_SERVER_HOST_NAME -p $CLOUD_SERVER_LISTEN_PORT) &
}

#stop cloud-client program
stop() 
{
	SERVICE_PID_FILE=/var/run/cloud-client.pid
	service_stop $CLOUD_CLIENT_BIN
}

restart()
{
	stop
	
	init_config	

	check_cloud_cfg

	SERVICE_PID_FILE=/var/run/cloud-client.pid
	service_start $CLOUD_CLIENT_BIN -h $CLOUD_SERVER_HOST_NAME -p $CLOUD_SERVER_LISTEN_PORT

	# Check if daemon is running, if not then re-execute.
	(sleep 1 && service_check $CLOUD_CLIENT_BIN || \
		service_start $CLOUD_CLIENT_BIN -h $CLOUD_SERVER_HOST_NAME -p $CLOUD_SERVER_LISTEN_PORT) &

}