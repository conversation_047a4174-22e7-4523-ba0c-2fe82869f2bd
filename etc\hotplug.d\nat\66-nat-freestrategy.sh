#!/bin/sh

case "$MODULE" in
    NAPT)
        echo "Got an NAPT hotplug clean=\"${CLEAN}\" "
        if [ "${CLEAN}" == "on" ]
        then
            if [ -e "/var/run/wifidog.pid" ];then
                echo "/etc/init.d/freeStrategy reload" > /dev/console
                touch /tmp/freeStrategy.log
                date >> /tmp/freeStrategy.log
                echo "/etc/init.d/freeStrategy reload " >> /tmp/freeStrategy.log
                /etc/init.d/freeStrategy reload
            fi
        fi
    ;;

esac

