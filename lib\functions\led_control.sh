#!/bin/sh

setup_ofl_led() {
    case ${1} in
    "on")
		echo 255 > /sys/class/leds/ofl/brightness
        ;;
    "off")
		echo 0 > /sys/class/leds/ofl/brightness
        ;;
    *)
        usage
        ;;
    esac
}

setup_ha_led() {
    case ${1} in
    "yellow")
		echo 0 > /sys/class/leds/ha_g/brightness
		echo 255 > /sys/class/leds/ha_y/brightness
        ;;
    "green")
		echo 255 > /sys/class/leds/ha_g/brightness
		echo 0 > /sys/class/leds/ha_y/brightness
        ;;
    "off")
		echo 0 > /sys/class/leds/ha_g/brightness
		echo 0 > /sys/class/leds/ha_y/brightness
        ;;
    *)
        usage
        ;;
    esac
}

setup_cloud_led() {
    case ${1} in
    "on")
		echo 255 > /sys/class/leds/cloud/brightness
        ;;
    "off")
		echo 0 > /sys/class/leds/cloud/brightness
        ;;
    *)
        usage
        ;;
    esac
}

setup_disk_warn_led() {
    case ${1} in
    "on")
		echo 255 > /sys/class/leds/diskwarn/brightness
        ;;
    "off")
		echo 0 > /sys/class/leds/diskwarn/brightness
        ;;
    *)
        usage
        ;;
    esac
}

usage()
{
    echo "Usage:"
    echo "led_control.sh [led_name] [option]"
    echo "led name: ofl/ha/cloud/disk_warn"
    echo "option: for ofl/cloud/disk_warn: on/off"
    echo "option: for ha: green/yellow/off "
}

if [ $# != 2 ]
then
    usage
else
    case ${1} in
    "ofl")
        setup_ofl_led ${2}
        ;;
    "ha")
        setup_ha_led ${2}
        ;;
    "cloud")
        setup_cloud_led ${2}
        ;;
    "disk_warn")
        setup_disk_warn_led ${2}
        ;;
    *)
        usage
        ;;
    esac
fi
