#
# Copyright (C) 2008-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# 
# brief      implement the core function of freeStrategy
# author     <PERSON><PERSON><PERSON>
# version    1.0.0 
# date         16Sep2015
# history     16Sep2015    Lu<PERSON> pei    Create the file
. /lib/firewall/fw.sh
 
RULE_LIBDIR=${RULE_LIBDIR:-/lib/freeStrategy}


fw_is_loaded() {
    local bool=$(uci_get_state firewall.core.loaded)
    return $((! ${bool:-0}))
}

fw_check()
{
	local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
	[ -n "$ret" ] && return 0||return 1
}

add_subchain(){
	# iptables -w -t nat -N freeStrategy 2>&1
	# iptables -w -t nat -N freeStrategy_url 2>&1
	local num=$(iptables -w -L forward_auth --line-number | grep '\bWiFiDog_Internet\b' | head -1|awk '{print $1}')
	# fw_check "nat" "prerouting_auth" "-j freeStrategy"
	# [ x$? == x0 ] && {
	# 	# need to get rule number of wifidog chain
	# 	iptables -w  -t nat -I prerouting_auth -j freeStrategy
	# }

	# fw_check "nat" "prerouting_auth" "-j freeStrategy_url"
	# [ x$? == x0 ] && {
	# 	# need to get rule number of wifidog chain
	# 	iptables -w -t nat -I prerouting_auth -j freeStrategy_url
	# }
	# WiFiDog_Internet
	iptables -w -N freeStrategy 2>&1
	iptables -w -N freeStrategy_url 2>&1
	fw_check "filter" "forward_auth" "-j freeStrategy"
	[ x$? == x0 ] && {
		if [ "x$num" == 'x' ];then
			iptables -w -A forward_auth -j freeStrategy
		else
			iptables -w -I forward_auth $num -j freeStrategy
		fi
	}

	fw_check "filter" "forward_auth" "-j freeStrategy_url"
	[ x$? == x0 ] && {
		if [ "x$num" == 'x' ];then
			iptables -w -A forward_auth -j freeStrategy_url
		else
			iptables -w -I forward_auth $num -j freeStrategy_url
		fi
	}		
}

fw_init() {
    [ -z "$RULE_INITIALIZED" ] || return 0
	
	. /lib/zone/zone_api.sh
	. /lib/functions/network.sh
    
    RULE_INITIALIZED=1
    return 0
}

rule_start(){
	fw_init
	fw_is_loaded || {
        #syslog $ACCESS_CONTROL_LOG_WNG_FIREWALL_NOT_LOADED
        echo "firewall hasnt start up yet"
        exit 1
    }
    #cp /etc/config/freeStrategy /tmp/freeStrategy
    echo "begin loading rules in freeStrategy"
    # add own rule here!!
    # create two urlset instances,each is a set of 200 number,so that means,support at most 400 rules whoes type is url
   # urlset create freestrategy1 ac 3
   # urlset create freestrategy2 ac 3
    add_subchain
	/lib/freeStrategy/add_delete.sh tuple
    #then a single rule is needed with urlsetmatch
}

rule_stop(){
	fw_init
	fw_is_loaded || {
        #syslog $ACCESS_CONTROL_LOG_WNG_FIREWALL_NOT_LOADED
        echo "firewall hasnt start up yet,in rule_stop"
        exit 1
    }
    ## try to destroy all the rules
    # first empty the rules that reference the 
    
    #clean the freeStrategy of nat
    iptables -w -t nat -F freeStrategy
    iptables -w -t nat -F freeStrategy_url
    fw_check "nat" "prerouting_auth" "-j freeStrategy"
    while [ x$? == 'x1' ];do
    	fw del 4 n prerouting_auth freeStrategy
    	fw_check "nat" "prerouting_auth" "-j freeStrategy"
    done
    fw del 4 n freeStrategy

    fw_check "nat" "prerouting_auth" "-j freeStrategy_url"
    while [ x$? == 'x1' ];do
    	fw del 4 n prerouting_auth freeStrategy_url
    	fw_check "nat" "prerouting_auth" "-j freeStrategy_url"
    done
    fw del 4 n freeStrategy_url

    # clean the freeStrategy of filter
    iptables -w -t filter -F freeStrategy
    iptables -w -t filter -F freeStrategy_url
    fw_check "filter" "forward_auth" "-j freeStrategy"
    while [ x$? == 'x1' ];
    do
    	fw del 4 f forward_auth freeStrategy
    	fw_check "filter" "forward_auth" "-j freeStrategy"
    done
    fw del 4 f freeStrategy

    fw_check "filter" "forward_auth" "-j freeStrategy_url"
    while [ x$? == 'x1' ];
    do
    	fw del 4 f forward_auth freeStrategy_url
    	fw_check "filter" "forward_auth" "-j freeStrategy_url"
    done
    fw del 4 f freeStrategy_url

    #urlset destroy 3  # destroy the set
}

rule_restart(){
	rule_stop
	rule_start
}

rule_reload(){
	rule_restart
}
