netifd#/etc/init.d/network restart
uhttpd#/etc/init.d/uhttpd restart
monitor#/etc/init.d/monitor restart
dnsproxyd#/etc/init.d/dnsproxy restart
uac#/etc/init.d/uac restart;touch /tmp/uac_restart_log;echo "uac restart" >> /tmp/uac_restart_log;echo `date` >> /tmp/uac_restart_log
cloud-client#/etc/init.d/cloud_sdk stop;/etc/init.d/cloud_client stop;sleep 1;killall cloud-client;killall cloud-brd;sleep 1;/etc/init.d/cloud_sdk start;/etc/init.d/cloud_client restart;touch /tmp/cloud_restart_log;echo "cloud restart" >> /tmp/cloud_restart_log;echo `date` >> /tmp/cloud_restart_log
cfg_save#/etc/init.d/cfg_save restart;touch /tmp/cfg_save_restart_log;echo "cfg_save restart" >> /tmp/cfg_save_restart_log;echo `date` >> /tmp/cfg_save_restart_log
nms_report#/etc/init.d/nms_report restart;touch /tmp/nms_report_log;echo "nms_report restart" >> /tmp/nms_report_log;echo `date` >> /tmp/nms_report_log
loggerd#/etc/init.d/loggerd restart
@upnp_proxy_check#/etc/init.d/upnp_proxy restart n
