#!/bin/sh /etc/rc.common

START=95
cpu_iface_dev_list="eth0 eth1"
vlan_iface_dev_list="eth0.1000 eth1.1001 eth0.1002 eth1.1003 eth0.1004 eth1.1005 eth0.1006 eth1.1007 eth0.1008 eth1.1009"

start() {
	for iface in ${cpu_iface_dev_list}; do
		echo f > /sys/class/net/${iface}/queues/rx-0/rps_cpus
		echo f > /sys/class/net/${iface}/queues/rx-1/rps_cpus
		echo e > /sys/class/net/${iface}/queues/rx-2/rps_cpus
		#reserve cpu0 resource refer the test result
		echo e > /sys/class/net/${iface}/queues/rx-3/rps_cpus
	done

	for iface in ${vlan_iface_dev_list}; do
		#reserve cpu0 resource refer the test result
		echo e > /sys/class/net/${iface}/queues/rx-0/rps_cpus
	done

	# Enable NSS RPS
	sysctl -w dev.nss.rps.enable=1 >/dev/null 2>/dev/null
}

stop() {
	for iface in ${cpu_iface_dev_list}; do
		echo 0 > /sys/class/net/${iface}/queues/rx-0/rps_cpus
		echo 0 > /sys/class/net/${iface}/queues/rx-1/rps_cpus
		echo 0 > /sys/class/net/${iface}/queues/rx-2/rps_cpus
		#reserve cpu0 resource refer the test result
		echo 0 > /sys/class/net/${iface}/queues/rx-3/rps_cpus
	done

	for iface in ${vlan_iface_dev_list}; do
		#reserve cpu0 resource refer the test result
		echo e > /sys/class/net/${iface}/queues/rx-0/rps_cpus
	done
}
