#!/bin/sh

. /lib/zone/zone_api.sh
. /lib/functions

PATH_PROJECTION_CONF="/etc/nginx/projection_conf"
SSLVPN_WEB_CONF_TEMPLATE="projection.conf.template"
PROJECTION_LISTEN_PORT="20098"
NGINX_BIN="/usr/sbin/nginx"

# 必须：$1:port
create_projection_gateway (){
    local port

    if [ ! $1 ]; then
         port=${PROJECTION_LISTEN_PORT}
    else
         port=$1
    fi


    local new_conf="${PATH_PROJECTION_CONF}/projection.conf"
    cp -p ${PATH_PROJECTION_CONF}/projection.conf.template ${new_conf}

    sed -i "s/\s*#REPLACE_LISTEN/listen ${port};/" ${new_conf}

    # ${NGINX_BIN} -s reload
}

delete_projection_gateway (){
    local web_conf="${PATH_PROJECTION_CONF}/projection.conf"
    [ ! -f ${web_conf} ] && return 1

    rm -rf ${web_conf}

    # ${NGINX_BIN} -s reload
}

