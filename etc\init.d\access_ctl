#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2010 OpenWrt.org

START=47

FW_ACL_LIBDIR=/lib/access_ctl

acl() {
	. $FW_ACL_LIBDIR/core.sh
	fw_acl_$1
}

start() {
	[ ! -f /tmp/access_ctl_lock ] && touch /tmp/access_ctl_lock
	[ ! -f /tmp/interface_lock ] && touch /tmp/interface_lock
	mkdir /tmp/access_ctl
	acl start
	touch /tmp/access_ctl.ready
}

stop() {
	rm /tmp/access_ctl_lock 2>/dev/null
	acl stop
	rm -rf /tmp/access_ctl.ready
}

restart() {
	acl restart
}

reload() {
	acl reload
}