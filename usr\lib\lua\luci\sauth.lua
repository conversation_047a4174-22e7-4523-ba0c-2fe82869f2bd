module("luci.sauth", package.seeall)
require("luci.util")
require("luci.sys")
require("luci.config")
local t = require "nixio", require "nixio.util"
local n = require "nixio.fs"
luci.config.sauth = luci.config.sauth or {}
sessionpath = luci.config.sauth.sessionpath
sessiontime = tonumber(luci.config.sauth.sessiontime) or 15 * 60
function prepare()
    n.mkdir(sessionpath, 700)
    if not sane() then error("Security Exception: Session path is not sane!") end
end
local function s(e)
    local e = n.readfile(sessionpath .. "/" .. e)
    return e
end
local function o(e, n)
    local e = t.open(sessionpath .. "/" .. e, "w", 600)
    e:writeall(n)
    e:close()
end
local function i(e) return not not (e and e:match("^[a-fA-F0-9_]+$")) end
function write(n, e)
    if not sane() then prepare() end
    assert(i(n), "Security Exception: Session ID is invalid!")
    assert(type(e) == "table", "Security Exception: Session data invalid!")
    e.atime = luci.sys.uptime()
    o(n, luci.util.get_bytecode(e))
end
function read(e)
    if not e or #e == 0 then return nil end
    assert(i(e), "Security Exception: Session ID is invalid!")
    if not sane(sessionpath .. "/" .. e) then return nil end
    local i = s(e)
    local i = loadstring(i)
    setfenv(i, {})
    local i = i()
    assert(type(i) == "table", "Session data invalid!")
    local s = n.stat(sessionpath .. "/" .. e, "mtime")
    local t = luci.sys.uptime()
    if s + sessiontime < t then
        if i.atime and i.atime + sessiontime < luci.sys.uptime() then
            kill(e)
            return nil
        end
    end
    n.utimes(sessionpath .. "/" .. e, t, t)
    return i
end
function sane(e)
    return luci.sys.process.info("uid") == n.stat(e or sessionpath, "uid") and
               n.stat(e or sessionpath, "modestr") ==
               (e and "rw-------" or "rwx------")
end
function kill(e) if i(e) then n.unlink(sessionpath .. "/" .. e) end end
function clear_expired_session(e)
    if not e or #e == 0 then return nil end
    assert(i(e), "Security Exception: Session ID is invalid!")
    if not sane(sessionpath .. "/" .. e) then return true end
    local n = s(e)
    local n = loadstring(n)
    setfenv(n, {})
    local n = n()
    if type(n) ~= "table" then
        kill(e)
        return true
    end
    if n.atime and n.atime + sessiontime < luci.sys.uptime() then
        kill(e)
        return true
    end
    return true
end
function reap()
    if sane() then
        local e
        for e in t.fs.dir(sessionpath) do
            if i(e) then clear_expired_session(e) end
        end
    end
end
function clear_sessions_butself()
    local e = require("luci.dispatcher")
    local e = e.context.authsession
    local e = luci.http.getenv("REMOTE_ADDR")
    e = e and string.gsub(e, "%.", "_")
    local n = t.fs.dir(sessionpath)
    if type(n) ~= "function" then return true end
    for n in n do if n ~= e then kill(n) end end
    return true
end
function clear_sessions()
    local e = require("luci.dispatcher")
    local e = e.context.authsession
    local e = t.fs.dir(sessionpath)
    if type(e) ~= "function" then return true end
    for e in e do kill(e) end
    return true
end
