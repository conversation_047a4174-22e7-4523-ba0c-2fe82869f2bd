#!/bin/sh /etc/rc.common

# Better to start before nms_report. Avoid msgQ buffering.
START=30

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1
SERVICE_BIN=/usr/sbin/alarmd
SERVICE_PID_FILE=/var/run/alarmd.pid
SERVICE_NAND_DIR=/tmp/system/alarm

start() {
    # Create a directory in nandfalsh. mkdir without -p,
    # should failed when /tmp/system directory doesn't exist.
    if [ ! -d ${SERVICE_NAND_DIR} ];then
        mkdir ${SERVICE_NAND_DIR} 2>/dev/null || true
    fi

    if [ ! -f ${SERVICE_PID_FILE} ];then
        service_start ${SERVICE_BIN} &
    fi
}

stop() {
    service_stop ${SERVICE_BIN}
    if [ -f ${SERVICE_PID_FILE} ];then
        rm -f ${SERVICE_PID_FILE}
    fi
}

restart() {
    echo "restaring alarmd service" > /dev/console
    stop
    start
}

reload() {
    restart
}
