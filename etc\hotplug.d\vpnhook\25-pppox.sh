#!/bin/sh

. /lib/functions.sh

delinfs=

l2tp_client_delete_rule() {                                        
    local section=${1}
	config_get valx ${section} outif
	valx1=$section
	
    for element in $interfaces; do  
		[ "${element}" = "${valx}" ] && {
			delifs="${delifs}${delifs:+,}`config_get ${section} tunnelname`"
			/lib/l2tp/l2tp-reload.sh delete lac ${valx1}
			uci delete vpn.${section}
			#lua /lib/l2tp/l2tp-ipsec-delete.lua  ${valx1}
		}
	done
}

l2tp_server_delete_rule() {                                        
    local section=${1}
	config_get valx ${section} bindif
	valx1=$section
	
    for element in $interfaces; do  
		[ "${element}" = "${valx}" ] && {
			/lib/l2tp/l2tp-reload.sh delete lns ${valx1}
			uci delete vpn.${section}
			#lua /lib/l2tp/l2tp-ipsec-delete.lua  ${valx1}
		}
	done
	
}

pptp_client_delete_rule() {                                        
    local section=${1}
	config_get valx ${section} outif
	
    for element in $interfaces; do  
		[ "${element}" = "${valx}" ] && {
			delifs="${delifs}${delifs:+,}`config_get ${section} tunnelname`"
			/lib/pptp/pptp-client-delete.sh `config_get ${section} confname` 1
			uci delete vpn.${section}
		}
	done
	
}

pptp_server_delete_rule() {                                        
    local section=${1}
	config_get valx ${section} bindif
	
    for element in $interfaces; do  
		[ "${element}" = "${valx}" ] && {
			/lib/pptp/delete-service.sh ${section}
			uci delete vpn.${section}
		}
	done
	
}


case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {   
		    interfaces=${interfaces//,/ } 
			
			config_load vpn
			
			config_foreach l2tp_client_delete_rule lac
			config_foreach pptp_client_delete_rule pac
			config_foreach l2tp_server_delete_rule lns
			config_foreach pptp_server_delete_rule pns

			uci commit vpn
			
			/etc/init.d/zone restart
			[ -n "${delifs}" ] && env -i ACTION=DELETE interfaces=${delifs} /sbin/hotplug-call vpnhook 
		}
	;;
	ADD)
	;;
	*)
	;;
esac
