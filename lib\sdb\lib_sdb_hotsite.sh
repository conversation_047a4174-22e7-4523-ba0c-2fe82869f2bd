#! /bin/sh
#
# lib_sdb_hotsite.sh
# author : f<PERSON><PERSON><PERSON>@tp-link.com.cn
# date   : 2022/2/14

. /lib/sdb/dbg.sh

# purpose: 读取特征库管理模块解包后的数据，将其导入到提交目录中
# call   : 当特征库从签名库导出后，会调用该接口对特征库数据进一步处理
#           从而得到可用的数据
prepare_commit(){
    # 读取后，自动存放至dataDir(/tmp/sdb/hotsite)下，无需处理
    # nothing to do
    lua /usr/lib/lua/sdb/hotsitedb.lua init
}

# purpose: 将提交目录和CSE中的数据清除
# call   : 当不需要该库时，会调用该接口清除commit目录下的文件，去除
#          中该特征库数据
restore_factory_commit(){
    local dataDir="/tmp/sdb/hotsite"
    rm -f ${dataDir}/*
    return 0
}

# purpose : 用于提交后，将占用内存较大的文件删除，减少内存占用
#           内存占用较小，可以不做处理
clean_after_commit(){
    # nothing to do
    true
}

case "$1" in
prepare)
    prepare_commit "$2" "$3"
    ;;
restore)
    restore_factory_commit "$2"
    ;;
clean)
    clean_after_commit "$2"
    ;;
esac

exit "$?"
