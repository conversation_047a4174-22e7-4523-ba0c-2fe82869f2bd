#!/bin/sh /etc/rc.common
# Copyright (C) 2008 OpenWrt.org

. /lib/functions/switch_port.sh

START=19
check_rsa()
{
	local rsa
	local trytimes=60
	local time=0
	while [ $time -lt $trytimes ];do
		if [ -f /tmp/rsa_check/rsa_result ]; then {
			rsa=`cat /tmp/rsa_check/rsa_result`
			if [ $rsa == "PASS" ]; then
				enable_all_phy
				break
			elif [ $rsa == "FAIL" ]; then
				enable_phy_GE5
				break
			else
				time=$(($time+1))
				sleep 1
			fi
		}
		else
			echo "file not exist, try after 1s, tritime:$time" >> /tmp/rsa_check/rsa_debug.log
			time=$(($time+1))
			sleep 1
		fi
	done
	if [ $time -eq $trytimes ]; then
		echo "check signature failed " > /dev/console
		enable_phy_GE5
	fi
}
start() {
	#if check ok
	#enable_all_phy
	check_rsa
}
stop() {
	disable_all_phy
}
restart() {
	stop
	start
}
