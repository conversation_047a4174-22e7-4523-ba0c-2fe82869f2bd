#!/bin/sh
# hotplug event for ipgroup update

# record the log
mkdir -p "/tmp/.qos"
local HOTP_LOG="/tmp/.qos/hotplug.log"
[ ! -f $HOTP_LOG ] && echo -e "time \ttrigger \taction" > $HOTP_LOG
[ "`ls -l $HOTP_LOG | awk '{print $5}'`" -gt 100000 ] && echo -e "time \ttrigger \taction" > $HOTP_LOG
echo -e "`date +%Y%m%d-%H:%M:%S` $group_name $ACTION" >> $HOTP_LOG

# check if qos module is ready
{
    flock -x 25
    [ ! -f /tmp/.qos/.ready ] && echo -e "`date +%Y%m%d-%H:%M:%S` \tqos not start, ignore hotplug msg!" >>$HOTP_LOG && exit
    flock -u 25
} 25<> /tmp/.qos/qos.lock

# response
case "$ACTION" in
		update)
			. /lib/qos-tplink/api.sh ipgroup $ACTION $group_name
			;;
		*)
			echo -e "`date +%Y%m%d-%H:%M:%S` \tillegal $ACTION!" >> $HOTP_LOG
			;;
esac
