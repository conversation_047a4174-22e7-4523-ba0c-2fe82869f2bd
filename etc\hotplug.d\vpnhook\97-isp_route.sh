#!/bin/sh

_delete_rule() {
    local cfg="$1"
    local iface="$2"
    local out_if

    out_if=$(uci_get isp_route "$cfg" if)
    [ "$out_if" == "$iface" ] && {
        uci delete isp_route.$cfg
        uci_commit isp_route
    }
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
		    interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				config_load isp_route
				config_foreach _delete_rule rule $element
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac