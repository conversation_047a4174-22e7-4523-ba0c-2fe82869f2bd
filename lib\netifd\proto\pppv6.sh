#!/bin/sh

[ -x /usr/sbin/pppd ] || exit 0

[ -n "$INCLUDE_ONLY" ] || {
	. /lib/functions.sh
	. ../netifd-proto.sh
	init_proto "$@"
}

pppv6_generic_init_config() {
	proto_config_add_string username6
	proto_config_add_string password6
	proto_config_add_string keepalive
	proto_config_add_int demand
	proto_config_add_string pppd_options
	proto_config_add_string 'connect:file'
	proto_config_add_string 'disconnect:file'
	proto_config_add_boolean ipv6
	proto_config_add_boolean authfail
	proto_config_add_int mtu
	proto_config_add_int mtu6
	proto_config_add_string pppname
	proto_config_add_string pppoe_sharev4
}

pppv6_generic_setup() {
	local config="$1"; shift
	local ip_config="auto"
	local accept_ra="1"

	local sharev4=`uci get network.$config.pppoe_sharev4 2>/dev/null`
	[ "$sharev4" == "on" ] && return

	local ip_config=`uci get network.$config.ip_config 2>/dev/null`
	if [ "static" != "$ip_config" ]; then
		accept_ra="2"
	fi

	local ipv4arg=""
	local ipv4=`uci get network.$config.ipv4 2>/dev/null`
	[ "$ipv4" == "1" ] && ipv4arg="ipv4"

	if [ "${demand:-0}" -gt 0 ]; then
		demand="precompiled-active-filter /etc/ppp/filter demand idle $demand"
	else
		demand="persist"
	fi
	[ "${keepalive:-0}" -lt 1 ] && keepalive=""
	[ -n "$mtu" ] || json_get_var mtu mtu
	. /lib/pppox/pppox-header.sh
	local mod_id=0
	[ -n "$pppname" ] || { if [ "${proto}" = "pppoev6" ]; then mod_id=82;pppname="${pppoe_header}$config"; else pppname="${proto:-ppp}-$config"; fi; }

	if [ "${proto}" == "pppoev6" ]; then
		local lcpechointerval=`uci get network.${config}.lcpechointerval 2>/dev/null`
		local lcpechofailure=`uci get network.${config}.lcpechofailure 2>/dev/null`
		lcpechofailure=${lcpechofailure:-5}
		lcpechointerval=${lcpechointerval:-2}
		[ "$keepalive" = "" ] && keepalive=${lcpechofailure},${lcpechointerval}
		poelogfile=/dev/null
	fi

	local interval="${keepalive##*[, ]}"
	[ "$interval" != "$keepalive" ] || interval=5
	[ -n "$connect" ] || json_get_var connect connect
	[ -n "$disconnect" ] || json_get_var disconnect disconnect
	# Get lan info to resolve LAN/WAN conflict.
	. /lib/zone/zone_api.sh
	lan_mode=$(uci get network.lan.ip_mode)
	lan_dev=$(zone_get_effect_devices LAN)

	local ifname=`uci get network.$config.ifname 2>/dev/null`
	[ -n "$ifname" ] && echo "0" > /proc/sys/net/ipv6/conf/$ifname/accept_ra

	[ -n "$username6" ] || username6=`uci get network.${config}.username6 2>/dev/null`
	[ -n "$password6" ] || password6=`uci get network.${config}.password6 2>/dev/null`

	proto_run_command "$config" /usr/sbin/pppd \
		nodetach ipparam "$config" \
		ifname "$pppname" \
		lan_dev "$lan_dev" \
		lan_mode "$lan_mode" \
		${poelogfile:+logfile /dev/null} \
		file /etc/ppp/options.default \
		${keepalive:+lcp-echo-interval $interval lcp-echo-failure ${keepalive%%[, ]*}} \
		noip +ipv6 \
		nodefaultroute \
		usepeerdns \
		$demand maxfail 3 \
		modId $mod_id \
		${username6:+user "$username6" password "$password6"} \
		${connect:+connect "$connect"} \
		${disconnect:+disconnect "$disconnect"} \
		ip-up-script /lib/netifd/ppp-up \
		ipv6-up-script /lib/netifd/pppv6-up \
		ip-down-script /lib/netifd/ppp-down \
		ipv6-down-script /lib/netifd/ppp-down-ipv6 \
		${mtu:+mtu $mtu mru $mtu} \
		"$@" $pppd_options	\
		accept_ra $accept_ra
}

pppv6_generic_teardown() {
	local interface="$1"

	local dhcp6c_dir="/tmp/dhcp6c/$interface"
	[ -d "$dhcp6c_dir" ] && {
		# kill dhcp6c
		local dhcp6c_pid="$dhcp6c_dir/dhcp6c.pid"
		if [ -f $dhcp6c_pid ]; then
			local pid=`cat $dhcp6c_pid`
			[ -n "$pid" ] && kill $pid
		fi
		rm -rf $dhcp6c_dir
	}

	case "$ERROR" in
		11|19)
			proto_notify_error "$interface" AUTH_FAILED
			json_get_var authfail authfail
			if [ "${authfail:-0}" -gt 0 ]; then
				proto_block_restart "$interface"
			fi
		;;
		2)
			proto_notify_error "$interface" INVALID_OPTIONS
			proto_block_restart "$interface"
		;;
	esac

	sleep 1
	proto_kill_command "$interface" 15
}

proto_pppoev6_init_config() {
	pppv6_generic_init_config
	proto_config_add_string "ac"
	proto_config_add_string "service"
}

proto_pppoev6_setup() {
	local config="$1"
	local iface="$2"

	for module in slhc ppp_generic pppox pppoe; do
		/sbin/insmod $module 2>&- >&-
	done

	json_get_var mtu mtu
	mtu="${mtu:-1492}"

	json_get_var ac ac
	json_get_var service service

	pppv6_generic_setup "$config" \
		plugin rp-pppoe.so \
		${ac:+rp_pppoe_ac "$ac"} \
		${service:+rp_pppoe_service "$service"} \
		"nic-$iface"
}

proto_pppoev6_teardown() {
	pppv6_generic_teardown "$@"
}

[ -n "$INCLUDE_ONLY" ] || {
	add_protocol pppoev6
}
