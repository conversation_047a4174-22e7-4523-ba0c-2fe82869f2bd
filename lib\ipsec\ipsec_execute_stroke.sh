#!/bin/sh 
# Copyright(c) 2011-2016 Shenzhen TP-LINK Technologies Co.Ltd.
# file    ipsec_execute_stroke.sh 
# brief   used to execute the ipsec stroke commands 
# author   <PERSON>
# version  1.0.0
# date     16Apr16
# histry   arg 1.0.0, 16Apr16, <PERSON>, Create the file. 

ipsec_stroke_for_add()
{
	local tunnel_name=$1
	{
		# 预留今后可能的扩展
		swanctl --load-pools
		swanctl --load-authorities

		# 加载所有secrets
		swanctl --load-creds
		# 加载新增的connection
		swanctl --load-conns --connection ${tunnel_name}

		echo "ipsec swanctl for add done" > /dev/console
		exit 0
	}&
	return 0
}

ipsec_stroke_for_modify()
{
	local tunnel_name=$1
	{
		# 按照 IKE SA 断开所有"平行"隧道
		# 解决共用 IKE_SA 的多条"平行"隧道的启用和断开生效问题
		swanctl --terminate --ike ${tunnel_name} --force

		# 预留今后可能的扩展
		swanctl --load-pools
		swanctl --load-authorities

		# 加载所有secrets
		swanctl --load-creds
		# 加载更新后的connection
		# (已经patch过strongswan，当swanctl.conf中存在最新更新的指定connection时，再次load该connction，就是重新加载该connection)
		swanctl --load-conns --connection ${tunnel_name}

		echo "ipsec swanctl for modify done" > /dev/console
		exit 0
	}&
	return 0
}

ipsec_stroke_for_del()
{
	local tunnel_name=$1
	{
		# 按照 IKE SA 断开所有"平行"隧道
		# 解决共用 IKE_SA 的多条"平行"隧道的启用和断开生效问题
		swanctl --terminate --ike ${tunnel_name} --force

		# 预留今后可能的扩展
		swanctl --load-pools
		swanctl --load-authorities

		# 卸载daemon程序中的指定creds
		swanctl --load-creds
		# 卸载daemon程序中的指定connection
		# (已经patch过strongswan，当swanctl.conf中不存在指定connection时，再次load该connction，就是删除此前daemon中存在的connection)
		swanctl --load-conns --connection ${tunnel_name}

		echo "ipsec swanctl for delete done" > /dev/console
		exit 0
	}&
	return 0
}


operation=$1
tunnel_name=$2
#echo "tunnel_name "$tunnel_name > /dev/console
if [ ! -n "$tunnel_name" ]; then
	echo "tunnel name is null" > /dev/console
	exit 1
fi

echo "ipsec execute swanctl ${operation} begin" > /dev/console

if [ "${operation}" = "modify" ]; then
  	ipsec_stroke_for_modify ${tunnel_name}

elif [ "${operation}" = "delete" ]; then
	ipsec_stroke_for_del ${tunnel_name}

elif [ "${operation}" = "add" ]; then
	ipsec_stroke_for_add ${tunnel_name}
fi

exit 0






