#!/bin/sh
# handle the user zone(sec_zone) affairs for the iface ifup/ifdown
#
#${ACTION}
#${INTERFACE} iface
#${DEVICE} device
#

[ "$DEVICE" == "lo" ] && exit 0
[ "${ACTION}" != "ifdown" -a "${ACTION}" != "ifup" ] && exit 1
[ -z "${INTERFACE}" -o "" = "${INTERFACE}" ] && exit 1

. /lib/functions.sh
. /lib/zone/zone_api.sh

user_zone=""
user_iface=""

#$1: section name
#find the user zone(sec_zone) the iface belong to
# tips:only the FW has the user zone
get_user_zone()
{
	type=`uci get zone.$1.type`
	#echo "type:${type}" > /dev/console
	#only handle the user_zone
	if [ -n "${type}" -a "user" = "${type}" ];then
		if_list=`uci get zone.$1.iface`
		if [ -n "${if_list}" ];then
			for iface in ${if_list};do
				if [ "${iface}" = "${INTERFACE}" ];then
					user_zone=`uci get zone.$1.name`
					#echo "user_zone: ${user_zone}" > /dev/console
				fi
			done
		fi
	fi
}

do_ifup()
{
	# set the user_zone and userif to the netdevice, so that the FW ko can read the userzone and userif of the flow
	[ "${DEVICE}" == "" ] && DEVICE=`zone_get_device_byif ${INTERFACE}`

	if [ -n "${DEVICE}" -a "" != "${DEVICE}" ];then
		user_iface=$(zone_iface_to_userif "${INTERFACE}")
		[ -z "${user_iface}" -o "" == "${user_iface}" ] && return 1

		config_load /etc/config/zone
		config_foreach get_user_zone "zone"
		config_foreach config_clear
		if [ -n "${user_zone}" -a "" != "${user_zone}" ];then
			# echo "dev:${DEVICE}" > /dev/console
			# echo "user_zone:${user_zone}" > /dev/console
			# echo "user_iface:${user_iface}" > /dev/console
			[ -f /sys/class/net/${DEVICE}/ifalias ] && echo "${user_zone};${user_iface}" > /sys/class/net/${DEVICE}/ifalias
		fi
	fi
}

case "${ACTION}" in
	"ifup")
		do_ifup;;
	*)
		return 1;;
esac

