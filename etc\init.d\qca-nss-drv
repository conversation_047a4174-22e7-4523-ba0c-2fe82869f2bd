#!/bin/sh /etc/rc.common
#
# Copyright (c) 2015-2017, The Linux Foundation. All rights reserved.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
# OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
#

START=70

sysctl_cmd() {
	sysctl -w $1=$2 >/dev/null 2>/dev/null
}

enable_rps() {
	irq_nss_rps=`grep nss_queue1 /proc/interrupts | cut -d ':' -f 1 | tr -d ' '`
	for entry in $irq_nss_rps
	do
		echo 2 > /proc/irq/$entry/smp_affinity
	done

	irq_nss_rps=`grep nss_queue2 /proc/interrupts | cut -d ':' -f 1 | tr -d ' '`
	for entry in $irq_nss_rps
	do
		echo 4 > /proc/irq/$entry/smp_affinity
	done

	irq_nss_rps=`grep nss_queue3 /proc/interrupts | cut -d ':' -f 1 | tr -d ' '`
	for entry in $irq_nss_rps
	do
		echo 8 > /proc/irq/$entry/smp_affinity
	done

	# Enable NSS RPS
	sysctl -w dev.nss.rps.enable=1 >/dev/null 2>/dev/null

}

# Set NSS FW-TO-HOST buffer pool cfg
set_nss_buffer_pool() {
	local mp_256="$(ls /proc/device-tree/ | grep -rw "MP_256")"
	local mp_512="$(ls /proc/device-tree/ | grep -rw "MP_512")"
	local board_name
	local hk_ol_num=0

	[ -f /tmp/sysinfo/board_name ] && {
		board_name=ap$(cat /tmp/sysinfo/board_name | awk -F 'ap' '{print$2}')
	}

	if [ "$mp_256" == "MP_256" ]; then
	case "$board_name" in
	ap-mp*)
			#extra pbuf core0 = (high_water_core0 - (NSS + OCM buffers)) * pbuf_size
			#where NSS+OCM buffers = 11720 and pbuf_size = 160 bytes
			#total pbuf size is 160 bytes,allocate memory for 4616 pbufs
			sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 800000
			sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 16336
			sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 0
			;;
	*)
			sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 1400000
			sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 20432
			sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 0
			;;
	esac
	elif [ "$mp_512" == "MP_512" ]; then
		[ -d /sys/module/qca_ol ] || { \
			hk_ol_num="$(cat /lib/wifi/wifi_nss_hk_olnum)"
			if [ $hk_ol_num -eq 3 ]; then
				#total pbuf size is 160 bytes,allocate memory for 19928 pbufs
				sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 3200000
				sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 31648
				sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 0
			else
				#total pbuf size is 160 bytes,allocate memory for 18904 pbufs
				sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 3100000
				sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 30624
				sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 8192
			fi
		}
	else
	case "$board_name" in
	ap-hk09*)
			local soc_version_major
			[ -f /sys/firmware/devicetree/base/soc_version_major ] && {
				soc_version_major="$(hexdump -n 1 -e '"%1d"' /sys/firmware/devicetree/base/soc_version_major)"
			}
			if [ $soc_version_major = 2 ];then
				[ -d /sys/module/qca_ol ] || { \
					#total pbuf size is 160 bytes,allocate memory for 55672 pbufs
					sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 9000000
					sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 67392
					#initially after init 4k buf for 5G and 4k for 2G will be allocated, later range will be configured
					sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 40960
				}
			else
				#total pbuf size is 160 bytes,allocate memory for 57184 pbufs
				sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 9200000
				sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 68904
				sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 32768
			fi
	;;
	ap-ac01)
		#total pbuf size is 160 bytes,allocate memory for 14712 pbufs
		sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 2400000
		sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 26432
		sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 0

	;;
	ap-ac02)
		#total pbuf size is 160 bytes,allocate memory for 18808 pbufs
		sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 3100000
		sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 30528
		sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 4096
	;;
	ap-hk* | ap-oak*)
		hk_ol_num="$(cat /lib/wifi/wifi_nss_hk_olnum)"
		[ -d /sys/module/qca_ol ] || { \
			if [ $hk_ol_num -eq 3 ]; then
				#total pbuf size is 160 bytes,allocate memory for 93560 pbufs
				#NSS general payload(8000),Rx Buffer per radio(4k),Tx queue buffer per radio(1k), intial TX allocation per radio(4k)
				#Below table is Tx desc allocation based on number of clients connected
				#Radio     Range0   Range1     Range2       Range3
				#           (<=64) (<=128)     (<=256)      (>256)
				#5G-Hi        24k	24k		24k		32k
				#2G           16k	16k		16k		16k
				#5G-Low       24k	24k		24k		32k
				#Absolute high water=NSS payloads + Rx buf per radio + Tx queue per radio + TxDescRange3(5g-low/5g-hi/2g)
				#wifi pool buff = Min(Total tx desc at range 3, device_limit) - total intial tx allocation
				#extra pbuf core0 = (high_water_core0 - (NSS + OCM buffers)) * pbuf_size
				#	where NSS+OCM buffers = 11720 and pbuf_size = 160
				sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 10000000
				sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 72512
				#initially after init 4k buf for 5G and 4k for 2G will be allocated, later range will be configured
				sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 36864
			else
				#total pbuf size is 160 bytes,allocate memory for 55672 pbufs
				sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 9000000
				sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 67392
				#initially after init 4k buf for 5G and 4k for 2G will be allocated, later range will be configured
				sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 40960
			fi
		}
	;;
	ap-cp* | ap-mp*)
		[ -d /sys/module/qca_ol ] || { \
			#total pbuf size is 160 bytes,allocate memory for 18808 pbufs
			sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 3100000
			sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 30528
			sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 4096
		}
	;;
	*)
		#total pbuf size is 160 bytes,allocate memory for 48456 pbufs
		sysctl_cmd dev.nss.n2hcfg.extra_pbuf_core0 7800000
		sysctl_cmd dev.nss.n2hcfg.n2h_high_water_core0 60176
		sysctl_cmd dev.nss.n2hcfg.n2h_wifi_pool_buf 35840
	;;
	esac
	fi

}

start() {
	local rps_enabled="$(uci_get nss @general[0] enable_rps)"
	if [ "$rps_enabled" -eq 1 ]; then
		enable_rps
	fi

	set_nss_buffer_pool
}
