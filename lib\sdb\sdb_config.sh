#!/bin/sh
# shellcheck disable=SC2034

. /lib/sdb/dbg.sh
. /lib/sdb/sdb_lock.sh


SDB_INSTALL_DIR="/tmp/sdb/nvram"
AV_INSTALL_DIR="/tmp/sdb/nvram_2"
SDB_STATU_DIR="/tmp/sdb/"
SDB_TMP_DIR="/tmp/sdb"
SDB_TMP_BAK_DIR="/tmp/sdb_bak"
COMMIT_PATH="/tmp/sdb/csedb_shadow/"

TMP_UCI_SUFFIX="_tmp_uci"
SDB_CONFIG="signature_db"
SDB_STATU_CONFIG="status"

# uci config
PLUGIN_CONFIG="plugin_config"
PLUGIN_STATUS="plugin_status"
CLOUD_STATUS='cloud_status'

#0xx表示稳定状态
STATUS_INIT="000"
STATUS_NONE="001"
STATUS_WAIT_DOWNLOAD="002"
STATUS_CAN_INSTALL="003"
STATUS_FALLBACK_SUCCESS="004"
STATUS_UNKNOWN="005"
STATUS_INSTALL_SUCCESS="006"

# 从1xx表示错误
STATUS_FILE_NOT_EXIST="100"
STATUS_CLOUD_ERROR="101"
STATUS_UNCOMPRESS_ERROR="102"
STATUS_COMMIT_ERROR="103"
STATUS_OPKG_FAIL="104"
STATUS_FALLBACK_ERROR="106"
STATUS_DOWNLOAD_FILE_ERROR="107"
STATUS_ERROR_VERSION="108"

# 从11x开始表示签名错误
STATUS_OTHER_ERROR="110"
STATUS_SIG_ERROR="111"
STATUS_COMPLETE_ERROR="112"

# 系统参数错误
STATUS_NO_PLUGIN_ID="105"
STATUS_PARAM_ERROR="109"

# 升级回退版本错误
STATUS_NOT_FOUND_CORRECT_VERSION="120"
STATUS_ERROR_FALLBACK_VERSION="121"

#2xx表示动作
STATUS_DOWNLOADING="200"
STATUS_LOADING="201"
STATUS_FALLBACKING="202"
STATUS_INSTALLING="203"
STATUS_CLEARING="204"

# alarm id
ALARM_LOAD_ERROR_ID="40001006"
ALARM_UPGRADE_SUCCESS_ID="40001007"
ALARM_UPGRADE_FAIL_ID="40001008"


get_download_dir()
{
    if [ -z "$1" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    if [ "$1" = "av" ]; then
        echo -n "/tmp/sdb/nvram_2/download/"
    else
        echo -n "/tmp/sdb/nvram/download/"
    fi
}

get_sdb_array()
{
    local sdb_array
    sdb_array="$(uci export ${SDB_CONFIG} | grep "config signature_db" | awk '{print $3}')"
    sdb_array="${sdb_array//\'/}"
    echo "${sdb_array}" | tr '\n' ' '
}

# 获取sdb保存目录,确定不同特征库应该存放在哪个目录中
get_sdb_save_dir()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    if [ "av"x = "${dbname}"x ]; then
        echo -n "${AV_INSTALL_DIR}"
    else
        echo -n "${SDB_INSTALL_DIR}"
    fi
}

# 通过插件id获取到特征库名 非特征库插件会返回空
get_sdb_name_by_plugin_id()
{
    local plugin_id="$1"
    local plugin_name prefix sdbName
    if [ -z "$plugin_id" ]; then
        dbg_error "lack param:need plugin id"
        return 1
    fi

    plugin_name="$(uci get "${PLUGIN_STATUS}.${plugin_id}".name 2>/dev/null)"
    prefix="${plugin_name:0:3}"
    if [ "${prefix}" = "sdb" ]; then
        sdbName="${plugin_name:3}"
        echo "$sdbName"
    else
        echo ""
    fi
}

# 获取sdb解压目录
get_sdb_tmp_dir()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo -n "${SDB_TMP_DIR}/${dbname}"
}

# 获取sdb解压备份目录
get_sdb_tmp_bak_dir()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo -n "${SDB_TMP_BAK_DIR}/${dbname}"
}

# 获取sdb备份目录
get_sdb_bak_dir()
{
    local dbname="$1"
    local sdb_save_dir
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    sdb_save_dir="$(get_sdb_save_dir "${dbname}")"

    echo -n "${sdb_save_dir}/sdb_bak"
}

# 获取特征库uci名字
get_sdb_uci()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo "${dbname}_plugin_uci"
}

# 获取特征库对应的插件名
get_plugin_name()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo "sdb${dbname}"
}

get_plugin_id()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    uci get "${SDB_CONFIG}.$(get_plugin_name "${dbname}")".plugin_id 2>/dev/null
}

# 获取特征库文件名
get_sdb_file_name()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo "release_${dbname}_signed.sdb"
}

# 获取opkg生成的临时插件UCI名字
get_tmp_plugin_uci()
{
    local dbname="$1"
    local pluginName
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    pluginName="$(get_plugin_name ${dbname})"

    echo "${pluginName}${TMP_UCI_SUFFIX}"
}

# 获取版本相关信息的文件名
get_version_file_name()
{
    echo "version.info"
}

# 获取tdb保存目录
get_tdb_save_dir()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi
    echo "$(/usr/sbin/av_database -d)"
}

# 获取tdb在仅下载时，暂存目录
get_tdb_bak_dir()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo -n "$(get_sdb_bak_dir ${dbname})"
}

# 获取tdb uci文件名
get_tdb_uci_name()
{
    echo "tdb_uci"
}

#获取tdb文件名
get_tdb_file_name()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    echo "release_${dbname}_db_signed.tdb"
}

# 获取当前tdb版本
get_tdb_version()
{
    local dbname="$1"
    local uci_dir="$2"
    if [ -z "$dbname" ] || [ -z "$uci_dir" ]; then
        dbg_error "lack param:need sdb name and uci dir"
        return 1
    fi

    uci get "$(get_tdb_uci_name).${dbname}.version" -c "${uci_dir}" 2>/dev/null
}

# 设置当前tdb版本
set_tdb_version()
{
    local dbname="$1"
    local version="$2"
    local tdb_save_dir="$3"
    local tdb_uci
    tdb_uci="$(get_tdb_uci_name)"
    if [ -z "$dbname" ] || [ -z "$version" ] || [ -z "$tdb_save_dir" ]; then
        dbg_error "lack param:need sdb name,tdb version and tdb uci dir"
        return 1
    fi

    mkdir -p "${tdb_save_dir}"
    if [ ! -f "${tdb_save_dir}/${tdb_uci}" ]; then
        touch "${tdb_save_dir}/${tdb_uci}"
    fi

    uci set ${tdb_uci}.${dbname}="tdb_info" -c ${tdb_save_dir}
    uci set ${tdb_uci}.${dbname}.version="$version" -c ${tdb_save_dir}
    uci commit ${tdb_uci} -c ${tdb_save_dir}
}

set_sdb_action()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi
    uci set ${SDB_CONFIG}.${dbname}.action="$2"
    uci commit ${SDB_CONFIG}
}

get_sdb_action()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi
    echo "$(uci get ${SDB_CONFIG}.${dbname}.action 2>/dev/null)"
}

sdb_status_lock()
{
    custom_lock -l "sdb_status" -f "sdb_status_lock" -p "$$" -t "$$" -w "1" -F "1"
}

sdb_status_unlock()
{
    custom_lock -n "sdb_status" -f "sdb_status_lock" -p "$$" -t "$$" -w "1" -F "1"
}

#数据面可能正常查询数据库，加文件锁
#参考vpp中policytarget remote_query
hotsitedb_lock()
{
    if [ "${HOTSITEDB_LOCKED}" != "1" ]; then
        ubus call maldomain disable_hotsite
        export HOTSITEDB_LOCKED=1
    fi
}

hotsitedb_unlock()
{
    if [ "${HOTSITEDB_LOCKED}" = "1" ]; then
        ubus call maldomain enable_hotsite
        export HOTSITEDB_LOCKED=0
    fi
}

sdb_get_depend_module()
{
    local dbname="$1"
    uci get "${SDB_CONFIG}.${dbname}".depend_module 2>/dev/null
}

sdb_engine_lock_take()
{
    local dbname="$1"
    if [ -z "${dbname}" ]; then
        dbg_error "lack params:dbname"
        return 1
    fi

    # 用于区分CSE引擎和其他模块引擎,根据不同引擎确定如何加锁。
    if [ "$(sdb_get_depend_module "${dbname}")" == "cse" ]; then
        sdb_lock_take "sdb" "$$"
    elif [ "$(sdb_get_depend_module "${dbname}")" == "hotsite_search" ]; then
        hotsitedb_lock
    fi
}

sdb_engine_lock_give()
{
    local dbname="$1"
    if [ -z "${dbname}" ]; then
        dbg_error "lack params:dbname"
        return 1
    fi

    if [ "$(sdb_get_depend_module "${dbname}")" == "cse" ]; then
        sdb_lock_give "sdb" "$$"
    elif [ "$(sdb_get_depend_module "${dbname}")" == "hotsite_search" ]; then
        hotsitedb_unlock
    fi
}

get_engine_sdb_version()
{
    local dbname="$1"
    local type="$2"
    local version depend_module
    if [ -z "$dbname" ]; then
        dbg_error "lack param:need sdb name and version type[major or minor]"
        return 1
    fi

    version="$(uci get ${SDB_CONFIG}.sdb$dbname.engine_version 2>/dev/null)"
    if [ "$(sdb_get_depend_module ${dbname})" == "hotsite_search" ]; then
        version="0.0"      #TODO
    fi
    if [ -z "$version" ]; then
        return "0"
    fi

    if [ "$type" = "major" ]; then
        echo "${version%.*}"
    elif [ "$type" = "minor" ]; then
        echo "${version#*.}"
    else
        echo "${version}"
    fi
}

get_error_message_en()
{
    case "$1" in
    "$STATUS_OPKG_FAIL")
        echo "unpack sdb file failed"
    ;;
    "$STATUS_CLOUD_ERROR")
        echo "download sdb file failed"
    ;;
    "$STATUS_UNCOMPRESS_ERROR")
        echo "decompress sdb file failed"
    ;;
    "$STATUS_FALLBACK_ERROR")
        echo "fallback error"
    ;;
    "$STATUS_DOWNLOAD_FILE_ERROR")
        echo "download sdb file failed"
    ;;
    "$STATUS_ERROR_VERSION")
        echo "sdb version is error"
    ;;
    "$STATUS_NO_PLUGIN_ID")
        echo "system params error"
    ;;
    esac
}

get_error_message_cn()
{
    case "$1" in
    "$STATUS_OPKG_FAIL")
        echo "解包失败"
    ;;
    "$STATUS_CLOUD_ERROR")
        echo "下载失败"
    ;;
    "$STATUS_UNCOMPRESS_ERROR")
        echo "解压失败"
    ;;
    "$STATUS_FALLBACK_ERROR")
        echo "回退失败"
    ;;
    "$STATUS_DOWNLOAD_FILE_ERROR")
        echo "下载失败"
    ;;
    "$STATUS_ERROR_VERSION")
        echo "版本错误"
    ;;
    "$STATUS_NO_PLUGIN_ID")
        echo "系统参数错误"
    ;;
    "$STATUS_SIG_ERROR")
        echo "签名错误"
    ;;
    "$STATUS_COMPLETE_ERROR")
        echo "完整性错误"
    ;;
    "$STATUS_OTHER_ERROR")
        echo "其他错误"
    ;;
    esac
}

get_error_message()
{
    if [ -z "$1" ]; then
        dbg_error "lack param:need sdb status"
        return 1
    fi
    #echo "$(get_error_message_en "$1")"
    echo "$(get_error_message_cn "$1")"
}

alarm_core()
{
    if [ -z "$1" ] || [ -z "$2" ]; then
        dbg_error "lack param:need Alarm ID and alerm message"
        return 1
    fi

    dbg_debug "nms_event_report_notify -k $1 -t 0 -p \"$2\""

    nms_event_report_notify -k $1 -t 0 -p "$2"
}

get_sdb_cur_version()
{
    local plugin_name plugin_version
    if [ -z "$1" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    plugin_name="sdb$1"
    plugin_version="$(uci get "$(get_sdb_uci ${1}).${plugin_name}.plugin_version" -c "$(get_sdb_save_dir ${1})" 2>/dev/null)"A
    echo "${plugin_version:0:10}"
}

get_sdb_bak_version()
{
    local plugin_name plugin_version
    if [ -z "$1" ]; then
        dbg_error "lack param:need sdb name"
        return 1
    fi

    plugin_name="sdb$1"
    plugin_version="$(uci get "$(get_sdb_uci ${1}).${plugin_name}.plugin_version" -c "$(get_sdb_bak_dir ${1})" 2>/dev/null)"
    echo "${plugin_version:0:10}"
}

get_upgrade_type()
{
    case "$1" in
    local)
        echo "本地"
    ;;
    fallback)
        echo "回退"
    ;;
    cloud)
        echo "在线"
    ;;
    esac
}

alarm_upgrade_success()
{
    if [ -z "$1" ]; then
        return 1
    fi

    local dbname="$2"
    local bak_version
    bak_version=$(get_sdb_bak_version $dbname)
    # 第一次升级的时候bak_version可能为空,如果为空则显示N/A
    if [ -z "${bak_version}" ]; then
        bak_version="N/A"
    fi

    local message
    message="{\"type\":\"$(get_upgrade_type $1)\", \"module_name\":\"sdb-$dbname\", \
\"pre_update_version\":\"${bak_version}\", \
\"update_version\":\"$(get_sdb_cur_version $dbname)\"}"

    alarm_core "$ALARM_UPGRADE_SUCCESS_ID" "$message"

    return 0
}

alarm_upgrade_fail()
{
    if [ -z "$1" ]; then
        return 1
    fi

    local dbname="$2"
    local error_message="$3"
    local bak_version
    bak_version=$(get_sdb_bak_version $dbname)
    # 第一次升级的时候bak_version可能为空,如果为空则显示N/A
    if [ -z "${bak_version}" ]; then
        bak_version="N/A"
    fi

    local message
    message="{\"type\":\"$(get_upgrade_type $1)\", \"module_name\":\"sdb-$dbname\", \
\"pre_update_version\":\"${bak_version}\", \
\"update_version\":\"$(get_sdb_cur_version $dbname)\", \
\"error_message\":\"$error_message\"}"

    alarm_core "$ALARM_UPGRADE_FAIL_ID" "$message"
    return 0
}

alarm_load_fail()
{
    local dbname="$1"
    local error_message="$2"
    local message
    message="{\"module_name\":\"sdb-$dbname\", \
\"version\":\"$(get_sdb_cur_version $dbname)\", \
\"error_message\":\"$error_message\"}"

    alarm_core "$ALARM_LOAD_ERROR_ID" "$message"
}

get_sdb_status()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi
    echo -n "$(uci get ${SDB_STATU_CONFIG}.${dbname}.status -c ${SDB_STATU_DIR})"
}


# 设置sdb status
set_sdb_status()
{
    local dbname="$1"
    local ret="$2"
    local type="$3"
    if [ -z "$dbname" ] || [ -z "$ret" ]; then
        dbg_error "lack param: sdb name and sdb status"
        return 1
    fi

    if [ -z "$type" ]; then
        dbg_info "if want to send alarm, please set param 3[action type]"
        dbg_info "  otherwise just set the sdb status"
    fi

    local status=""
    # 将返回值补充到3个字节
    case "${#ret}" in
    1)
        status="00${ret}"
    ;;
    2)
        status="0${ret}"
    ;;
    3)
        status="${ret}"
    ;;
    *)
        status="${STATUS_UNKNOWN}"
    ;;
    esac

    if [ "${status}" -eq ${STATUS_NONE} ]; then
        # success
        if [ "$type"x = "local"x ] || [ "$type"x = "cloud"x ] || [ "$type"x = "fallback"x ]; then
            alarm_upgrade_success "$type" "$dbname"
        fi
    elif [ "${status:0:1}" -eq 1 ]; then
        #fail
        if [ "$type"x = "load"x ]; then
            alarm_load_fail $dbname "$(get_error_message "$status")"
        else
            alarm_upgrade_fail "$type" $dbname "$(get_error_message "$status")"
        fi
    fi

    if [ "${status:0:1}" -eq 2 ]; then
        sdb_status_lock
        is_sdb_busy ${dbname}
        if [ "$?" -ne 0 ]; then
            sdb_status_unlock
            return 1
        fi
    fi

    uci set ${SDB_STATU_CONFIG}.${dbname}.status="${status}" -c ${SDB_STATU_DIR}
    uci commit ${SDB_STATU_CONFIG} -c ${SDB_STATU_DIR}

    if [ "${status:0:1}" -eq 2 ]; then
        sdb_status_unlock
    fi
    return 0
}

# purpose: 获取安全策略设置的use标志
get_sdb_use_flag()
{
    local dbname="$1"
    local use_flag
    if [ -z "$dbname" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi

    use_flag="$(uci get ${SDB_STATU_CONFIG}.${dbname}.use -c ${SDB_STATU_DIR} 2>/dev/null)"
    if [ -z "${use_flag}" ]; then
        dbg_warn "can't find use in ${SDB_STATU_DIR}/${SDB_STATU_CONFIG}"
        return 0
    fi

    return ${use_flag}
}

# purpose: 提供给策略模块设置use标志位
set_sdb_use_flag()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi
    uci set ${SDB_STATU_CONFIG}.${dbname}.use="$2" -c ${SDB_STATU_DIR}
    uci commit ${SDB_STATU_CONFIG} -c ${SDB_STATU_DIR}
}

# 如果CSE中文件为空(只包含#字符),则返回0,表示没有加载
# 反之，返回1,表示已加载
is_loaded()
{
    local fileName fileSize content
    fileName="$1"
    if [ -z "${fileName}" ]; then
        dbg_error "lack param: file name"
        return 0
    fi

    if [ ! -f "${fileName}" ]; then
        return 0
    fi

    fileSize="$(ls -l ${fileName} | awk '{print $5}')"
    if [ ${fileSize} -lt 3 ]; then
        content="$(cat ${fileName})"
        if [ "$content" = "#" ]; then
            return 0
        fi
    fi

    return 1
}

# purpose: 获取load标志,fw5600提交时，会重启CSE，需要重新导入病毒库
#         而在这种情况下，正常升级病毒特征库会加载两次病毒库，这里判断
#         已经加载了，就不再执行sec_db load了.
# attention: 需要在cse启动后，才能使用。不然cseenv会报错
# return : 1，已加载；0，未加载
get_sdb_load_flag()
{
    local dbname="$1"
    local load_flag
    if [ -z "$dbname" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi

    if [ "$dbname" = "av" ]; then
        #如果COMMIT目录下存在下述文件，则表示已经load了
        #由于av库，提交至CSE后，会删除掉
        #而如果commit目录下已经存在，则表示已加载了
        for commit_file in av_md5.bin av_sha2.bin av_pe_md5.bin;
        do
            is_loaded ${COMMIT_PATH}/${commit_file}
            [ "$?" -eq 1 ] && return 1
        done
    fi

    load_flag="$(cseenv get SDB_${dbname}_LOAD_FLAG)"
    if [ -z "${load_flag}" ] || [ "${load_flag}" -eq 0 ]; then
        dbg_info "can't find SDB_${dbname}_LOAD_FLAG in cse, return 0"
        return 0
    fi

    return 1
}

# purpose: 设置load标志位, 已加载为1，未加载为0
# attention: 需要在cse启动后，才能使用。不然cseenv会报错
set_sdb_load_flag()
{
    if [ -z "$1" ] || [ -z "$2" ]; then
        dbg_error "lack param: sdb name and load flag"
        return 1
    fi

    cseenv set SDB_${1}_LOAD_FLAG=$2
    return $?
}

# purpose: 获取sdb初始化标记
# return : 0 表示未初始化， 1表示已初始化
is_sdb_inited()
{
    if [ -z "$1" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi

    if [ ! -f "${SDB_STATU_DIR}/${SDB_STATU_CONFIG}" ]; then
        dbg_error "can't find config[${SDB_STATU_DIR}/${SDB_STATU_CONFIG}]"
        return 0
    fi

    return "$(uci get ${SDB_STATU_CONFIG}.${1}.init -c ${SDB_STATU_DIR} 2>/dev/null)"
}

# purpose: 设置sdb初始化标记
# param[1]: 特征库名称
# param[2]: 标志，0 表示未初始化，1表示已初始化
set_sdb_init_flag()
{
    local dbname="$1"
    if [ -z "$dbname" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi
    uci set ${SDB_STATU_CONFIG}.${dbname}.init="$2" -c ${SDB_STATU_DIR}
    uci commit ${SDB_STATU_CONFIG} -c ${SDB_STATU_DIR}
}

# purpose: 获取sdb初始化标记
# return : 0 表示有特征库未初始化， 1表示所有特征库已初始化
is_all_sdb_inited()
{
    for dbname in $(get_sdb_array);
    do
        is_sdb_inited ${dbname}
        if [ $? -ne 1 ]; then
            return 0
        fi
    done

    return 1
}

# purpose: 设置sdb初始化标记为1, 表示已初始化
set_all_sdb_inited()
{
    for dbname in $(get_sdb_array);
    do
        set_sdb_init_flag $dbname 1
    done

    return 0
}

# 避免同时进行安装或者回退的问题
is_sdb_busy()
{
    local dbname="$1"
    local status head
    if [ -z "$dbname" ]; then
        dbg_error "lack param: sdb name"
        return 1
    fi

    status="$(get_sdb_status $dbname)"
    head=${status:0:1}
    # 在进行动作时，不能够对db进行其他动作
    if [ "2"x = "${head}"x ]; then
        dbg_debug "sdb[$dbname] is busy, ret[1]"
        return 1
    fi

    return 0
}

# 判断是否某个特征库是否已经加载过
# 为1，表示加载
# 为0，表示未加载
is_sdb_loaded()
{
    local dbname="$1"
    case "${dbname}" in
        av)
            is_loaded ${COMMIT_PATH}/av.rules
            [ "$?" -eq 1 ] && return 1
        ;;
        ips)
            is_loaded ${COMMIT_PATH}/ips.rules
            [ "$?" -eq 1 ] && return 1
        ;;
        url)
            is_loaded ${COMMIT_PATH}/urlid.rules
            [ "$?" -eq 1 ] && return 1
        ;;
        app)
            is_loaded ${COMMIT_PATH}/appsig.rules
            [ "$?" -eq 1 ] && return 1
            is_loaded ${COMMIT_PATH}/appsig_port.conf
            [ "$?" -eq 1 ] && return 1
        ;;
    esac

    return 0
}

# 提交操作 接口
sdb_submit()
{
    # 当存在策略模块时，通过策略模块进行提交
    # 如果没有时，通过csedb_commit 进行提交
    if [ -f "/lib/fw_policy_manager/fw_policy_db_handler.sh" ]; then
        /lib/fw_policy_manager/fw_policy_db_handler.sh submit
    else
        csedb_commit
    fi
}
