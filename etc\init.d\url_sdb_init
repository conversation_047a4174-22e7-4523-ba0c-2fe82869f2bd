#!/bin/sh /etc/rc.common

# shellcheck disable=SC2034
START=92

. /lib/sdb/sdb_lock.sh
. /lib/sdb/sdb_config.sh

start() {
    (
        local sdb_upgrade_type="${2-load}"
        #Make sure the websort is OK in case of no sdb
        [ ! -f "/tmp/sdb/url/url.db" ] && lua /usr/lib/lua/sdb/urldb.lua init

        waitcse_status 0 6,9 120
        sec_db load url $sdb_upgrade_type
        if [ "$?" -eq 0 ]; then
            SUBSHELL_PID=$(sh -c 'echo $PPID')

            sdb_lock_take url_init $SUBSHELL_PID

            # 下面脚本会生成策略树，并执行csedb_commit
            is_sdb_inited url
            if [ "$?" -eq 1 ]; then
                echo "urlfilter_profile" >> /tmp/policy_db/db_modified
                sdb_submit
            else
                # 初始化时，统一进行策略生成和提交
                dbg_info "SDB[url] first load, skip csedb_commit"
            fi

            sdb_lock_give url_init $SUBSHELL_PID

            # 重启依赖于 SDB 的服务
            /etc/init.d/maldomain restart
        fi

        # 执行完，该脚本后，无论load是否正确，都设置标志位为1
        set_sdb_init_flag url 1
     ) > /dev/console &
    # cloud-client 通过popen r 打开shell进程，这时候子进程的标准输出
    # 是与cloud-client连接的管道，在xxx_sdb_init异步执行后，当该进程
    # 结束，cloud-client关闭管道。xxx_sdb_init所在的子进程如果输入
    # echo命令，向标准输出传消息，就会导致进程中断。所以，这里将
    # 其的标准输出重定向到/dev/console，避免echo出错
}


stop() {
    sec_db unload url
}
