#!/bin/sh

[ "$INTERFACE" = "loopback" ] && return

case ${ACTION} in
    ifup|ifupdate)
        . /lib/functions.sh
        . /lib/zone/zone_api.sh
        . /lib/functions/network.sh

        local dnsserver name
        network_get_dnsserver4 dnsserver $INTERFACE

        dns1=`echo $dnsserver | cut -d ' ' -f 1`
        dns2=`echo $dnsserver | cut -d ' ' -f 2`
        dev=`zone_get_effect_devices $INTERFACE`

        name=`zone_iface_to_userif $INTERFACE`

        [ -n "${dev}" ]  && {
            [ "$dns1" == "" ] && dns1="0.0.0.0"
            [ "$dns2" == "" ] && dns2="0.0.0.0"

            local current_status
            current_status=`ubus call online_check get_iface | grep -w "\"$name\""`
            if [ -z "$current_status" ]; then
                echo "[online_check] new interface: $name" > /dev/console
                ubus call online_check add_if "{\"name\":\"$name\"}"
            fi

            echo "[online_check] $name $dev $dns1 $dns2" > /dev/console
            ubus call online_check update_dns "{\"name\":\"$name\",\"dev\":\"$dev\",\"dns1\":\"$dns1\",\"dns2\":\"$dns2\"}"
        }
        ;;
    *)
    ;;
esac



