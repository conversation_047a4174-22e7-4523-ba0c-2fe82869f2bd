#!/bin/sh

[ "$INTERFACE" == "loopback" ] && exit 0

check_rule_state()
{
	config_get use_policy $1 use_policy

	if [ "$use_policy" == "$policy_name" ];then
		config_get enable $1 enable
		[ "$enable" == "on" ] && restart_needed=1
	fi
}

search_policy_iface()
{
	local tmp=0
	policy_name="$1"

	[ $flag -eq 1 ] && return

	ifaces=`uci get policy_route.$1.use_iface`
	for iface in $ifaces;do
		if list_contains iface "$INTERFACE";then
			tmp=1
		fi
	done
	[ "$tmp" = "1" ] && config_foreach check_rule_state policy_rule
}

check_and_reload_policy_route()
{
	restart_needed=0

	. /lib/functions.sh

	config_load policy_route
	config_foreach search_policy_iface policy

	[ "$restart_needed" = "1" ] && /etc/init.d/policy_route restart
}

. /lib/balance/api.sh

case "$ACTION" in
	ifup)
		# check_and_reload_policy_route
		echo "[policy_route] iface $INTERFACE balance up" > /dev/console
		/etc/init.d/policy_route reload handle_balance_event $INTERFACE "up"
		;;

	ifdown)
		# check_and_reload_policy_route
		echo "[policy_route] iface $INTERFACE balance down" > /dev/console
		/etc/init.d/policy_route reload handle_balance_event $INTERFACE "down"
		;;
esac
