#!/bin/sh /etc/rc.common
# Copyright(c) 2011-2015 Shenzhen TP-LINK Technologies Co.Ltd.
# file     nat.init
# brief
# author   <PERSON> chen
# version  1.0.0
# date     05June15
# histry   arg 1.0.0, 05June15, <PERSON> chen, Create the file.
. /lib/zone/zone_api.sh
. /lib/functions/network.sh

ZONE_TMP_FILE="/tmp/zone_tmp"

fw_build_zones()
{
	fw add 4 m zone_MSSFIX
	fw add 4 m FORWARD zone_MSSFIX $ { -p tcp }
	normal_useriflist=$(zone_get_normal_useriflist)
	for userif in $normal_useriflist; do
		ifaces=$(zone_userif_to_effective_iface "${userif}")

		for iface in $ifaces; do

			if_ipaddr=""
			network_get_ipaddr if_ipaddr "$iface"

			[ -z "$if_ipaddr" ] && {
				continue
			}
			device=$(zone_get_device_byif "$iface")
			uci_set_state firewall core ${iface}_if "$device"
			uci_set_state firewall core ${iface}_ip "$if_ipaddr"

			local chain=zone_${iface}

			fw add 4 f ${chain}_forward
			fw add 4 f forward ${chain}_forward $ { -i $device -m comment --comment [$iface] }

			fw add 4 f ${chain}_input
			fw add 4 f input ${chain}_input $ { -i $device -m comment --comment [$iface] }

			fw add 4 f ${chain}_output
			fw add 4 f output ${chain}_output $ { -o $device -m comment --comment [$iface] }


			local mtu=$(ifconfig $device | grep "MTU:" | cut -d : -f 2 |awk '{print $1}')
			[ x"$mtu" == 'x' ] && mtu=1500
			# local bind_if=$(uci get network.${iface}.t_bindif 2>/dev/null)
			local mss=$(($mtu-40))
			local rule="-p tcp -m tcp --tcp-flags SYN,RST SYN"
    		local target_size="TCPMSS --set-mss $mss"
    		local target_mtu="TCPMSS --clamp-mss-to-pmtu"

				# fw add 4 m ${chain}_MSSFIX "$target_size" $ { "$rule" }
				# fw add 4 m ${chain}_MSSFIX "$target_mtu" $ { "$rule" }
			iptables -w -t mangle -A zone_MSSFIX  $rule -o $device -j $target_size -m comment --comment "[$iface]"
			iptables -w -t mangle -A zone_MSSFIX  $rule -o $device -j $target_mtu -m comment --comment "[$iface]"

    		# accept icmp pkts
    		fw add 4 f ${chain}_input ACCEPT $ { -p icmp -m icmp --icmp-type 8 }



		done
	done
}

fw_zones_add() {
	local sub_chain=$1
	local dev=$2
	local mss=$3
	local has_rule=`ip6tables -w -t mangle -nvL zone_MSSFIX |grep $sub_chain`
	[ -z "$has_rule" ] && {
		ip6tables -w -t mangle -N $sub_chain
		ip6tables -w -t mangle -A zone_MSSFIX -i $dev -m comment --comment "i_${sub_chain}" -j $sub_chain
		ip6tables -w -t mangle -A zone_MSSFIX -o $dev -m comment --comment "o_${sub_chain}" -j $sub_chain
		ip6tables -w -t mangle -A $sub_chain -p tcp -m tcp --tcp-flags SYN,RST SYN -j TCPMSS --set-mss $mss
		ip6tables -w -t mangle -A $sub_chain -p tcp -m tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
	}
}

fw_zones_del() {
	local sub_chain=$1
	local has_rule=`ip6tables -w -t mangle -nvL zone_MSSFIX |grep $sub_chain`
	[ -n "$has_rule" ] && {
		# delete old rule
		ip6tables -w -t mangle -F $sub_chain
		local iNum=`ip6tables -w -t mangle --line-numbers -L zone_MSSFIX |grep "i_${sub_chain}" | awk '{print $1}'`
		[ -n "$iNum" ] && ip6tables -w -t mangle -D zone_MSSFIX $iNum
		local oNum=`ip6tables -w -t mangle --line-numbers -L zone_MSSFIX |grep "o_${sub_chain}" | awk '{print $1}'`
		[ -n "$oNum" ] && ip6tables -w -t mangle -D zone_MSSFIX $iNum
		ip6tables -w -t mangle -X $sub_chain
	}
}

fw_zones6_rule() {
	{
		flock -x 20
		local op=$1
		[ "$op" == "add" ] && {
			fw_zones_del $2
			fw_zones_add $2 $3 $4
		}
		[ "$op" == "del" ] && {
			fw_zones_del $2
		}
		flock -u 20
	} 20<>/tmp/zones6.lock
}

fw_do_build_zones6() {
	# add ip6tables zone_MSSFIX
	local iface=$1
	config_get proto  "$1" "proto"
	config_get ipv6_enable  "$1" "ipv6_enable"
	config_get mtu6  "$1" "mtu6"
	config_get ifname  "$1" "ifname"
	config_get share  "$1" "pppoe_sharev4"

	# 非静态Ip和PPPoE复用链路，过滤掉非IPv6接口
	local iface_list=$(uci get zone.NORMAL.iface 2>/dev/null)
	if [ "$proto" != "static" -a "$share" != "on" ]; then
		local ret=$(echo "$iface_list" | grep -q "\b${iface}\b" && echo "1")
		[ "x$ret" != "x" ] && return 0
	fi

	if [ "$ipv6_enable" == "on" ]; then
		if [ "$proto" != "pppoev6" ] && [ "$proto" != "pppoe" ]; then
			local sub_chain="zone_${iface}_MSSFIX"
			#local dev=`zone_get_effect_devices ${iface}`
			local dev=$ifname
			local mss=$(($mtu6-60))
			local has_ipv6=`ifconfig $dev |grep Scope:Global`
			[ -n "$has_ipv6" ] && {
				fw_zones6_rule add $sub_chain $dev $mss
				echo "fw_zones6_rule add $sub_chain $dev $mss" > /dev/console
			}

		else
			local mss=$(($mtu6-60))
			if [ "$share" == "on" ]; then
				local sub_chain="zone_${iface}_MSSFIX"
				local dev="pe-${iface}"
				local has_ipv6=`ifconfig $dev |grep Scope:Global`
				[ -n "$has_ipv6" ] && {
					fw_zones6_rule add $sub_chain $dev $mss
					echo "fw_zones6_rule add $sub_chain $dev $mss" > /dev/console
				}
			else
				local sub_chain="zone_${iface}_MSSFIX"
				local dev="pe-${iface}"
				local has_ipv6=`ifconfig $dev |grep Scope:Global`
				[ -n "$has_ipv6" ] && {
					fw_zones6_rule add $sub_chain $dev $mss
					echo "fw_zones6_rule add $sub_chain $dev $mss" > /dev/console
				}
			fi
		fi
	fi
}
fw_build_zones6()
{
	ip6tables -t mangle -N zone_MSSFIX
	ip6tables -t mangle -A FORWARD -j zone_MSSFIX
	config_load network
	config_foreach fw_do_build_zones6 interface

}

fw_event_interface6() {
	local iface=$1
	local dev=$3
	local op=$2

	local share=`uci get network.${iface}.pppoe_sharev4`
	local proto=`uci get network.${iface}.proto`

	# skip vlan and lan
	[ "$iface" == "lan" ] && return 0
	local iface_list=$(uci get zone.NORMAL.iface 2>/dev/null)

	# 静态路由和PPPoE复用链路接口IPv6和IPv4 iface一样
	# 非静态Ip和PPPoE复用链路，过滤掉非IPv6接口
	if [ "$proto" != "static" -a "$share" != "on" ]; then
		local ret=$(echo "$iface_list" | grep -q "\b${iface}\b" && echo "1")
		[ "x$ret" != "x" ] && return 0
	fi

	[ "$op" == "add" ] && {
		local ipv6_enable=`uci get network.${iface}.ipv6_enable 2>/dev/null`
		[ "$ipv6_enable" == "on" ] && {
			local sub_chain="zone_${iface}_MSSFIX"
			local mtu=`uci get network.${iface}.mtu6`
			local mss=$(($mtu-60))
			[ -z "$dev" ] && {
				if [ "$proto" == "static" -o "$share" == "on" ]; then
					dev=`zone_get_effect_devices ${iface}`
				else
					if [ "$proto" == "pppoe6" ]; then
						dev="pe-${iface}"
					else
						dev=`zone_get_effect_devices ${iface%?}`
					fi
				fi
			}
			[ -n "$dev" ] && {
				fw_zones6_rule add $sub_chain $dev $mss
			}
		}
	}

	[ "$op" == "del" ] && {
		local sub_chain="zone_${iface}_MSSFIX"
		fw_zones6_rule del $sub_chain
	}
}

fw_config_get_rule_acl_inner() {
	fw_config_get_section "$1" rule_acl_inner { \
		string name "" \
		string zone "" \
		string policy "" \
		string service "" \
		string src "" \
		string dest "" \
		string time "" \
		string flag "" \
	} || return

}

service_config_get() {
	fw_config_get_section "$1" service { \
		string name "" \
		string proto "" \
		string sport "" \
		string dport "" \
		string type "" \
		string code "" \
		string comment "" \
		string flag "" \
	} || return
}


fw_do_default_acl()
{

	fw_config_get_rule_acl_inner $1
	service_config_get $rule_acl_inner_service

	rule_acl_inner_policy=$(echo $rule_acl_inner_policy|tr '[a-z]' '[A-Z]')

	ifaces=$(zone_get_effect_ifaces "$rule_acl_inner_zone")
	for iface in $ifaces; do
		if_ipaddr=""
		network_get_ipaddr if_ipaddr "$iface"

		[ -z "$if_ipaddr" ] && {
			continue
		}

		chain_forward=zone_${iface}_forward
		chain_input=zone_${iface}_input
		chain_output=zone_${iface}_output

		device=$(zone_get_device_byif "$iface")




		[ "$rule_acl_inner_flag" == "0" ] && {

			set_src_rule=""
			if [ $rule_acl_inner_src != "IPGROUP_ANY" ];then
				src_group=$(echo $rule_acl_inner_src |grep "REV")
				if [ -n "$src_group" ]; then
					rule_acl_inner_src=${rule_acl_inner_src%_*}
					set_src_rule="-m set ! --match-set $rule_acl_inner_src src"

				else
					set_src_rule="-m set --match-set $rule_acl_inner_src src"
				fi
			else
				set_src_rule=""
			fi
			#echo "set_src_rule=$set_src_rule"

			set_dst_rule=""
			if [ $rule_acl_inner_dest != "IPGROUP_ANY" ];then
				dst_group=$(echo $rule_acl_inner_dest |grep "REV")
				if [ -n "$dst_group" ]; then
					rule_acl_inner_dest=${rule_acl_inner_dest%_*}
					set_dst_rule="-m set ! --match-set $rule_acl_inner_dest dst"

				else
					set_dst_rule="-m set --match-set $rule_acl_inner_dest dst"
				fi

			else
				set_dst_rule=""
			fi
			#echo "set_dst_rule=$set_dst_rule"

			if [ $rule_acl_inner_dest == "Me" ]; then
				chain=$chain_input
			elif [ $rule_acl_inner_src == "Me" ];then
				chain=$chain_output
			else
				chain=$chain_forward
			fi


			if [ "$service_name" == "ALL" ]; then
				local fw_rule="-i $device ""$set_src_rule"" ""$set_dst_rule"

				fw add 4 f $chain ${rule_acl_inner_policy} $ { "$fw_rule" }
			else
				if [ "$service_proto" != "icmp" ]; then

					service_sport=$(echo $service_sport|tr '-' ':')
					service_dport=$(echo $service_dport|tr '-' ':')

					for proto in $service_proto; do

						fw add 4 f $chain ${rule_acl_inner_policy} $ { -i $device -p $service_proto -m $service_proto \
							--sport $service_sport --dport $service_dport "$set_src_rule"" ""$set_dst_rule" }

					done

				else
					if [ "$service_name" == "ICMP_ALL" ]; then

						fw add 4 f $chain ${rule_acl_inner_policy} $ { -i $device -p $service_proto --icmp-type any \
							"$set_src_rule"" ""$set_dst_rule" }
					else

						fw add 4 f $chain ${rule_acl_inner_policy} $ { -i $device -p $service_proto -m $service_proto \
							--icmp-type $service_type/$service_code "$set_src_rule"" ""$set_dst_rule" }
					fi

				fi

			fi


		}


	done


}

fw_load_default_acl()
{
	fw_config_append service
	config_foreach fw_do_default_acl rule_acl_inner

}

fw_load_vpn()
{
	# load ipsec and l2tp
	#local forward_ipsec_chain=ipsec_forward_rule
	#fw add 4 f $forward_ipsec_chain
	#fw add 4 f forwarding_rule $forward_ipsec_chain

	local local_service_chain=local_service_rule
	fw add 4 f $local_service_chain
	fw add 4 f input_rule $local_service_chain
	fw add 4 f input_rule DROP $ { -p tcp --dport 1723 }

	fw add 4 f $local_service_chain ACCEPT $ { -p esp }
	fw add 4 f $local_service_chain ACCEPT $ { -p udp --dport 500 }
	fw add 4 f $local_service_chain ACCEPT $ { -p udp --dport 4500 }

	fw add 4 f $local_service_chain ACCEPT $ { -p udp --dport 1701 }

	fw add 4 f $local_service_chain ACCEPT $ { -p udp --dport 67 }
	fw add 4 f $local_service_chain ACCEPT $ { -p udp --dport 68 }
}



fw_event_interface()
{
	#echo "here" > /dev/console
	{
		flock 8
		fw_check()
		{
			local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
			[ -n "$ret" ] && return 0||  return 1
		}
		local iface=$1
		local action=$2


		local chain=zone_${iface}

		devices=$(zone_get_device_byif $iface)  # when the iface is deleted such as pppoe, nil will return

		# [ "$action" == "del" ] && {
		# 	iface="`cat $ZONE_TMP_FILE |grep "\<${zone}_iface\>" |cut -d '=' -f 2`"
		# 	devices="`cat $ZONE_TMP_FILE |grep "\<${zone}_dev\>" |cut -d '=' -f 2`"
		# 	chain=zone_${iface}
		# 	grep "^${zone}_iface=.*" $ZONE_TMP_FILE 2>&1 >/dev/null && sed "/^${zone}_iface=.*/d" -i $ZONE_TMP_FILE
		# 	grep "^${zone}_dev=.*" $ZONE_TMP_FILE 2>&1 >/dev/null && sed "/^${zone}_dev=.*/d" -i $ZONE_TMP_FILE
		# }

		#echo "$zone $iface $devices" > /dev/console

		[ -z "$iface" -o -z "$action" ] && {
			return
		}

		[ "$action" == "del" ] && {
			#echo "delete" > /dev/console
			#for device in $devices; do
				fw flush 4 f ${chain}_forward
				#fw $action 4 f forward ${chain}_forward $ { -i $device }
				local rule_num=$(iptables -w -nvL forward --line-numbers | grep "\b$iface\b" | head -n 1 | awk '{print $1}')
				[ "x$rule_num" != 'x' ] && {
					iptables -w -D forward $rule_num
				}
				fw $action 4 f ${chain}_forward


				fw flush 4 f ${chain}_input
				#fw $action 4 f input ${chain}_input $ { -i $device }
				rule_num=$(iptables -w -nvL input --line-numbers | grep "\b$iface\b" | head -n 1 | awk '{print $1}')
				[ "x$rule_num" != 'x' ] && {
					iptables -w -D input $rule_num
				}
				fw $action 4 f ${chain}_input

				fw flush 4 f ${chain}_output
				#fw $action 4 f output ${chain}_output $ { -o $device }
				rule_num=$(iptables -w -nvL output --line-numbers | grep "\b$iface\b" | head -n 1 | awk '{print $1}')
				[ "x$rule_num" != 'x' ] && {
					iptables -w -D output $rule_num
				}
				fw $action 4 f ${chain}_output

	  
	   			local num=$(iptables -w -t mangle -nL zone_MSSFIX | grep "\b$iface\b" | wc -l)
	   			local pos=$(iptables -w -t mangle -nL zone_MSSFIX --line-numbers| grep "\b$iface\b" | head -n 1 | awk '{print $1}')
				for i in `seq 1 $num`;do
					iptables -w -t mangle -D zone_MSSFIX $pos
				done
			#done

			
			
			
		}

		[ "$action" == "add" ] && {
			#echo  "add" > /dev/console
			for device in $devices; do
				iptables -w -N ${chain}_forward 2>/dev/null
				fw_check "filter" "forward" "-i $device -m comment --comment [$iface] -j ${chain}_forward"
				[ x$? == x0 ] && fw $action 4 f forward ${chain}_forward $ { -i $device -m comment --comment [$iface] }

				iptables -w -N ${chain}_input 2>/dev/null 
				fw_check "filter" "input" "-i $device -m comment --comment [$iface] -j ${chain}_input"
				[ x$? == x0 ] && fw $action 4 f input ${chain}_input $ { -i $device -m comment --comment [$iface] }

				iptables -w -N ${chain}_output 2>/dev/null 
				fw_check "filter" "output" "-o $device -m comment --comment [$iface] -j ${chain}_output"
				[ x$? == x0 ] && fw $action 4 f output ${chain}_output $ { -o $device -m comment --comment [$iface] }



	
				local mtu=$(ifconfig $device | grep "MTU:" | cut -d : -f 2 |awk '{print $1}')
				[ x"$mtu" == 'x' ] && mtu=1500
				
				local mss=$(($mtu-40))
				local rule="-p tcp -m tcp --tcp-flags SYN,RST SYN"
		    	local target_size="TCPMSS --set-mss $mss"
		    	local target_mtu="TCPMSS --clamp-mss-to-pmtu"
		  
		  		local num=$(iptables -w -t mangle -nL zone_MSSFIX | grep "\b$iface\b" | wc -l)
		  		if [ "x$num" != 'x' ];then
		  			local pos=$(iptables -w -t mangle -nL zone_MSSFIX --line-numbers| grep "\b$iface\b" | head -n 1 | awk '{print $1}')
					for i in `seq 1 $num`;do
						iptables -w -t mangle -D zone_MSSFIX $pos
				done
		  		fi
				iptables -w -t mangle -A zone_MSSFIX  $rule -o $device -j $target_size -m comment --comment "[$iface]"
				iptables -w -t mangle -A zone_MSSFIX  $rule -o $device -j $target_mtu -m comment --comment "[$iface]"

			done

			[ $ZONE == "LAN" ] && return
			
		# 	[ ! -f $ZONE_TMP_FILE ] && touch $ZONE_TMP_FILE

		# 	grep "^${zone}_iface=.*" "$ZONE_TMP_FILE" 2>&1 >/dev/null || {
		# 		echo -e "${zone}_iface=${iface}" >> "$ZONE_TMP_FILE" 2>/dev/null
		# 		echo -e "${zone}_dev=${devices}" >> "$ZONE_TMP_FILE" 2>/dev/null
		# 		return 0
		# }

		# 	sed -i "s/^${zone}_iface=.*/${zone}_iface=${iface}/" $ZONE_TMP_FILE	
		# 	sed -i "s/^${zone}_dev=.*/${zone}_dev=${devices}/" $ZONE_TMP_FILE	
		
		}
		flock -u 8
	} 8<>/tmp/fw_interface.lock
}
