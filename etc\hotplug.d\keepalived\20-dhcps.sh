#!/bin/sh
local iface=$INTERFACE
[ -z $iface -o $iface == "" ] && exit

local dhcpd_iface=`uci get dhcpd.$iface.interface 2>/dev/null`
[ -z $dhcpd_iface ] && exit


case ${ACTION} in
	master)
		# ac module
		local uac=`uci get apmng_set.settings.apmngr_status 2>/dev/null`
		if [ "$uac" == "on" ]; then
			uci set dhcpd.$iface.option60="TP-LINK"
			uci set dhcpd.$iface.option138=$VIP
		fi
		#modify dhcpd gateway and pri_dns snd_dns to virtual ip

		uci set dhcpd.$iface.gateway=$VIP
		uci set dhcpd.$iface.pri_dns=$VIP
		uci delete dhcpd.$iface.snd_dns
		uci set dhcpd.$iface.enable="on"
		uci commit dhcpd
		/etc/init.d/dhcpd start
	;;
	backup)
		#Disable dhcpd
		uci set dhcpd.$iface.enable="off"
		uci commit dhcpd
		/etc/init.d/dhcpd start
	;;
	*)
	;;
esac
