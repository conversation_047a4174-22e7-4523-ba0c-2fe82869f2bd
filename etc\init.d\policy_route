#!/bin/sh /etc/rc.common
START=96

handle_policy_rule_timeobj()
{
	config_get timeobj $1 timeobj
	[ -n "$timeobj" ] || return

	if [ "$timeobj" != "Any" ] ;then
		config_get name $1 name
		timeobj_api_add $timeobj "policy_route" $name
		timeobj_api_commit "policy_route"
	fi
}

start() {
	( flock -x 67

	/usr/sbin/policy_route start "not_apply_sdwan"

	. /lib/functions.sh
	. /lib/time_mngt/timeobj_api.sh

	config_load policy_route
	config_foreach handle_policy_rule_timeobj policy_rule	

	touch /tmp/policy_route.ready

	  flock -u 67
	) 67<>/tmp/.policy_route_lock
}

stop() {
	/usr/sbin/policy_route stop
}

restart() {
	( flock -x 67
		stop

		/usr/sbin/policy_route start
	  flock -u 67
	) 67<>/tmp/.policy_route_lock
}

reload() {
	( flock -x 67

		local api=$1
		shift
		. /usr/sbin/policy_route && $api $*

	  flock -u 67
	) 67<>/tmp/.policy_route_lock
}
