#!/bin/sh

. /lib/zone/zone_api.sh
USERIF=`zone_get_userif_bydev $DEVICE`
[ -z "$USERIF" ] && return

case "$ACTION" in
    ifup)
        # . /lib/policy_route/api.sh

        # handle_vpniface_up $USERIF
        echo "[policy_route] vpn iface $INTERFACE iface up" > /dev/console
        /etc/init.d/policy_route reload handle_balance_event $USERIF "up"
        ;;
    ifdown)
        # . /lib/policy_route/api.sh

        echo "[policy_route] vpn iface $INTERFACE iface down" > /dev/console
        /etc/init.d/policy_route reload handle_balance_event $USERIF "down"
        ;;
esac