#!/bin/sh
# This script is executed as part of the hotplug event with
# HOTPLUG_TYPE=iface, triggered by various scripts when an interface
# is configured (ACTION=ifup) or deconfigured (ACTION=ifdown).  The
# interface is available as INTERFACE, the real device as DEVICE.

. /lib/zone/zone_api.sh

[ "$DEVICE" == "lo" ] && exit 0

USERIF=$(zone_get_userif_bydev $DEVICE)
[ -z "$USERIF" ] && return

case "$ACTION" in
	ifup)
		#fw_acl_event_interface "$USERIF" add
		/lib/access_ctl/interface.sh "$USERIF" add
	;;
	ifdown)
		#fw_acl_event_interface "$USERIF" del
		/lib/access_ctl/interface.sh "$USERIF" del
	;;
	ifupdate)
		# do nothing
	;;
esac
