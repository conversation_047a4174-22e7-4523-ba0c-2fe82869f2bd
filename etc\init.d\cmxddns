#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=99


local CMXDDNS_INIT=/tmp/cmxddns.init
CMXDDNS_READY=/tmp/cmxddnsFlag

local cmxddns_enabled_rule_num=0

get_rule_num(){
	config_get interface $1 interface
	config_get enable $1 enable
	
	if [ -n "${interface}" ];then
		let cmxddns_enabled_rule_num++
	fi
}

get_cmxddns_num()
{
	config_load cmxddns
	config_foreach  get_rule_num cmxddns
}

start()
{
		get_cmxddns_num
		
		if [ ${cmxddns_enabled_rule_num} -gt 0 ];then
		{
			echo "cmxddns_enabled_rule_num = ${cmxddns_enabled_rule_num},restart cmxddns" > /dev/console
			touch $CMXDDNS_INIT
			touch $CMXDDNS_READY	
			service_start /usr/sbin/cmxddnsd
			
		}
		else 
		{
			if [ -f $CMXDDNS_INIT ];then
			{
				echo "cmxddns_enabled_rule_num = 0,but not init state,restart cmxddns" > /dev/console
				touch $CMXDDNS_INIT
				touch $CMXDDNS_READY	
				service_start /usr/sbin/cmxddnsd
			}
			fi
		}
		fi
        return 0
}

stop()
{
        service_stop /usr/sbin/cmxddnsd
        return 0
}

restart()
{
        stop
		start
}

reload()
{
        restart   
}

