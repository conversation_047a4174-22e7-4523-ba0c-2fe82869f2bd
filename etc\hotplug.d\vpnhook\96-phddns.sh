#!/bin/sh

_delete_rule() {                                        
    local cfg="$1"                                     
    local iface="$2"                                                                                       
    local interface   

    interface=$(uci_get phddns "$cfg" interface)
    echo "cfg=$cfg,iface=$iface,interface=$interface"  >> /tmp/phddns_vpnhook.log    	
    [ "$interface" == "$iface" ] && {      
        uci delete phddns.$cfg    
        uci_commit phddns  		
    }                                                         
}

case ${ACTION} in
	DELETE)  
		[ -n "${interfaces}" ] && {
		    echo "interfaces=$interfaces" >> /tmp/phddns_vpnhook.log    
		    interfaces=${interfaces//,/ } 
			for element in $interfaces   
			do  
				echo "element=$element"  >> /tmp/phddns_vpnhook.log    
				config_load phddns
				config_foreach _delete_rule phddns $element
			done  
		}
	;;
	ADD)
	;;
	*)
	;;
esac