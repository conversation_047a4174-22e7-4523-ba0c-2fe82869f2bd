#!/bin/sh
# shellcheck disable=SC2155 # this file does not use $? after assignment
. /lib/functions.sh

CFG_DIR="/etc/config/"

cp_non_uci_sepcific_cfg_to_tmp_cb()
{
	local src_file_path="$1"
	local cfg_running_path="$2"
	local dst_path="$3"
	if [ -n "$src_file_path" ]&&[ -n "$cfg_running_path" ]&&[ -n "$dst_path" ];then
		if [ -e "$src_file_path" ]&&[ -e "$cfg_running_path" ];then
			# local runing_cfg_dir=${cfg_running_path%/*}
			local dst_cfg_file_suffix=`echo $cfg_running_path | sed -e 's/\/etc\/nouci_config\///g'`
			local dst_cfg_file_dir=`echo $dst_path"/"$dst_cfg_file_suffix | sed -e 's/\/\//\//g'`
			local dst_cfg_dir=${dst_cfg_file_dir%/*}
			#echo "cp_non_uci_sepcific_cfg_to_tmp_cb=====  "$runing_cfg_dir $dst_cfg_file_suffix $dst_cfg_file_dir $dst_cfg_dir>/dev/console
			mkdir -p "$dst_cfg_dir"

			cp -rf $src_file_path $dst_cfg_dir
		fi
	fi
}

cp_uci_sepcific_cfg_to_tmp_cb()
{
	local src_file_path="$1"
	local cfg_running_path="$2"
	local dst_path="$3"
	if [ -n "$src_file_path" ]&&[ -n "$cfg_running_path" ]&&[ -n "$dst_path" ];then
		if [ -f "$src_file_path" ]&&[ -f "$cfg_running_path" ];then

			local dst_cfg_file_dir=`echo $dst_path"/"$CFG_DIR | sed -e 's/\/\//\//g'`
			#echo "cp_uci_sepcific_cfg_to_tmp_cb=====  "$cfg_running_path  $dst_cfg_file_dir>/dev/console
			mkdir -p "$dst_cfg_dir"

			cp -f $src_file_path $dst_cfg_file_dir
		fi
	fi
}
