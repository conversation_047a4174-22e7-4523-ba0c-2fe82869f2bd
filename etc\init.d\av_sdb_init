#!/bin/sh /etc/rc.common

# shellcheck disable=SC2034
START=92

. /lib/sdb/sdb_lock.sh
. /lib/sdb/sdb_config.sh

start() {
    (
        local sdb_upgrade_type="${2-load}"
        SUBSHELL_PID=$(sh -c 'echo $PPID')
        waitcse_status 0 6,9 120
        sec_db load av $sdb_upgrade_type
        if [ "$?" -eq 0 ]; then
            set_sdb_load_flag av 1

            is_sdb_inited av
            if [ "$?" -eq 1 ]; then
                sdb_lock_take av_init $SUBSHELL_PID
                csedb_commit
                sdb_lock_give av_init $SUBSHELL_PID
            else
                # 初始化时，统一进行提交, 不单独进行提交
                dbg_info "SDB[av] first load, skip csedb_commit"
            fi
        fi

        # 执行完，该脚本后，无论load是否正确，都设置标志位为1
        set_sdb_init_flag av 1
    ) > /dev/console &
    # cloud-client 通过popen r 打开shell进程，这时候子进程的标准输出
    # 是与cloud-client连接的管道，在xxx_sdb_init异步执行后，当该进程
    # 结束，cloud-client关闭管道。xxx_sdb_init所在的子进程如果输入
    # echo命令，向标准输出传消息，就会导致进程中断。所以，这里将
    # 其的标准输出重定向到/dev/console，避免echo出错
}

stop() {
    sec_db unload av
}
