#!/bin/sh /etc/rc.common
# Copyright (C) 2009-2012 OpenWrt.org

START=50
NGINX_BIN=/usr/sbin/nginx

NGINX_CONF="/etc/nginx/nginx.conf"
NGINX_BIN="/usr/sbin/nginx"
NGINX_CERT_CRT="/etc/nouci_config/nginx/server.crt"
NGINX_CERT_LINK_CRT="/etc/nginx/server.crt"
NGINX_CERT_KEY="/etc/nouci_config/nginx/server.key"
NGINX_CERT_LINK_KEY="/etc/nginx/server.key"
DEFAULT_PORTAL_PORT=8080

modify_nginx_worker_num_and_dict_size()
{
	if [ ! -f $NGINX_CONF ]; then
		echo "$NGINX_CONF file not exist"
		exit
	fi

	nginx_worker_connection=`uci get profile.portal_config.nginx_worker_connection 2>/dev/null`
	if [ "$nginx_worker_connection" != "" ];then
		sed -i "s/worker_connections.*;$/worker_connections ${nginx_worker_connection};/g" $NGINX_CONF
	fi

	nginx_thread_num=`uci get profile.portal_config.nginx_thread_num 2>/dev/null`
	if [ "$nginx_thread_num" != "" ];then
		sed -i "/worker_processes /c \worker_processes $nginx_thread_num;" $NGINX_CONF
	fi
	
	nginx_jumpPageCfgDict=`uci get profile.portal_config.nginx_jumpPageCfgDict 2>/dev/null`
	if [ "$nginx_jumpPageCfgDict" != "" ];then
		sed -i "s/jumpPageCfgDict.*;$/jumpPageCfgDict ${nginx_jumpPageCfgDict};/g" $NGINX_CONF
	fi
	
	nginx_workerInfoDict=`uci get profile.portal_config.nginx_workerInfoDict 2>/dev/null`
	if [ "$nginx_workerInfoDict" != "" ];then
		sed -i "s/workerInfoDict.*;$/workerInfoDict ${nginx_workerInfoDict};/g" $NGINX_CONF
	fi

	nginx_preloadJumpPageDict=`uci get profile.portal_config.preload_jump_page 2>/dev/null`
	if [ "$nginx_preloadJumpPageDict" != "1" ];then
		sed -i "/preloadJumpPageDict/d"  $NGINX_CONF
	fi
	
	nginx_portalPageCfgDict=`uci get profile.portal_config.nginx_portalPageCfgDict 2>/dev/null`
	if [ "$nginx_portalPageCfgDict" != "" ];then
		sed -i "s/portalPageCfgDict.*;$/portalPageCfgDict ${nginx_portalPageCfgDict};/g" $NGINX_CONF
	fi

	nginx_userManageDict=`uci get profile.portal_config.nginx_userManageDict 2>/dev/null`
	if [ "$nginx_userManageDict" != "" ];then
		sed -i "s/userManageDict.*;$/userManageDict ${nginx_userManageDict};/g" $NGINX_CONF
	fi

	nginx_radiusServerDict=`uci get profile.portal_config.nginx_radiusServerDict 2>/dev/null`
	if [ "$nginx_radiusServerDict" != "" ];then
		sed -i "s/radiusServerDict.*;$/radiusServerDict ${nginx_radiusServerDict};/g" $NGINX_CONF
	fi
	
	nginx_authServerGroupDict=`uci get profile.portal_config.nginx_authServerGroupDict 2>/dev/null`
	if [ "$nginx_authServerGroupDict" != "" ];then
		sed -i "s/authServerGroupDict.*;$/authServerGroupDict ${nginx_authServerGroupDict};/g" $NGINX_CONF
	fi	
	
	nginx_authlistDict=`uci get profile.portal_config.nginx_authlistDict 2>/dev/null`
	if [ "$nginx_authlistDict" != "" ];then
		sed -i "s/authlistDict.*;$/authlistDict ${nginx_authlistDict};/g" $NGINX_CONF
	fi	
	
	nginx_authUserDict=`uci get profile.portal_config.nginx_authUserDict 2>/dev/null`
	if [ "$nginx_authUserDict" != "" ];then
		sed -i "s/authUserDict.*;$/authUserDict ${nginx_authUserDict};/g" $NGINX_CONF
	fi	
	
	nginx_sslVpnUserDict=`uci get profile.portal_config.nginx_sslVpnUserDict 2>/dev/null`
	if [ "$nginx_sslVpnUserDict" != "" ];then
		sed -i "s/sslVpnUserDict.*;$/sslVpnUserDict ${nginx_sslVpnUserDict};/g" $NGINX_CONF
	fi	
	
	nginx_sslVpnAuthListDict=`uci get profile.portal_config.nginx_sslVpnAuthListDict 2>/dev/null`
	if [ "$nginx_sslVpnAuthListDict" != "" ];then
		sed -i "s/sslVpnAuthListDict.*;$/sslVpnAuthListDict ${nginx_sslVpnAuthListDict};/g" $NGINX_CONF
	fi	
	
	nginx_cookieDict=`uci get profile.portal_config.nginx_cookieDict 2>/dev/null`
	if [ "$nginx_cookieDict" != "" ];then
		sed -i "s/cookieDict.*;$/cookieDict ${nginx_cookieDict};/g" $NGINX_CONF
	fi	

	nginx_username_to_resourceGroupId_dict=`uci get profile.portal_config.nginx_username_to_resourceGroupId_dict 2>/dev/null`
	if [ "$nginx_username_to_resourceGroupId_dict" != "" ];then
		sed -i "s/username_to_resourceGroupId_dict.*;$/username_to_resourceGroupId_dict ${nginx_username_to_resourceGroupId_dict};/g" $NGINX_CONF
	fi

	nginx_resourceGroupId_to_resourceGroup_dict=`uci get profile.portal_config.nginx_resourceGroupId_to_resourceGroup_dict 2>/dev/null`
	if [ "$nginx_resourceGroupId_to_resourceGroup_dict" != "" ];then
		sed -i "s/resourceGroupId_to_resourceGroup_dict.*;$/resourceGroupId_to_resourceGroup_dict ${nginx_resourceGroupId_to_resourceGroup_dict};/g" $NGINX_CONF
	fi

	nginx_cookie_to_flow_dict=`uci get profile.portal_config.nginx_cookie_to_flow_dict 2>/dev/null`
	if [ "$nginx_cookie_to_flow_dict" != "" ];then
		sed -i "s/cookie_to_flow_dict.*;$/cookie_to_flow_dict ${nginx_cookie_to_flow_dict};/g" $NGINX_CONF
	fi	
}

modify_upload_max_size()
{
	if [ ! -f $NGINX_CONF ]; then
		echo "$NGINX_CONF file not exist"
		exit
	fi

	nginx_client_max_body_size=`uci get profile.portal_config.nginx_client_max_body_size 2>/dev/null`
	if [ "$nginx_client_max_body_size" != "" ];then
		sed -i "s/client_max_body_size.*;$/client_max_body_size ${nginx_client_max_body_size};/g" $NGINX_CONF
	fi
}

modify_nginx_port()
{
	if [ ! -f $NGINX_CONF ]; then
		echo "$NGINX_CONF file not exist"
		exit
	fi
	
	sed -i "1,70s/^\ \+listen\ \+[0-9]\+;/        listen        $1;/" $NGINX_CONF
	sed -i "1,70s/^\ \+listen\ \+\[::\]:[0-9]\+;/        listen        \[::\]:$1;/" $NGINX_CONF
	sed -i "100,230s/^\ \+listen\ \+[0-9]\+;/        listen       $2;/" $NGINX_CONF
}

check_portal_https_port_conflict() {
    portal_port=`uci get authentication.portal.portal_port`
    portal_https_port=`uci get authentication.portal.portal_https_port`
    http_port=`uci get system.service_port.http_port`
    https_port=`uci get system.service_port.https_port`    
    
    if [ "$portal_https_port" == "$portal_port" ]; then
        return 1
    elif [ "$portal_https_port" == "$http_port" ]; then
        return 1
    elif [ "$portal_https_port" == "$https_port" ]; then
        return 1
    fi
    
    return 0
}

remake_nginx_cert() {
	if [ -f $NGINX_CERT_LINK_CRT ] && [ -f $NGINX_CERT_LINK_KEY ]; then
		return
	fi
	ln -s  $NGINX_CERT_CRT $NGINX_CERT_LINK_CRT
	ln -s  $NGINX_CERT_KEY $NGINX_CERT_LINK_KEY
}

modify_portal_port() {
    check_count=0
    check_portal_https_port_conflict
    while [ $? -eq 1 -a $check_count -lt 3 ]; do
        let check_count+=1
        portal_https_port=`uci get authentication.portal.portal_https_port`
        if [ "$portal_https_port" != "" ];then
            let portal_https_port+=1
            uci set authentication.portal.portal_https_port="$portal_https_port"
            uci commit authentication
        fi
        
        check_portal_https_port_conflict
    done       

	config_load authentication
	config_get port "portal" "portal_port" $DEFAULT_PORTAL_PORT
	config_get https_port "portal" "portal_https_port" $DEFAULT_PORTAL_PORT	   
    
	modify_nginx_port $port $https_port
}


create_portal_table()
{
	rm -rf /tmp/create_portal_table.lua
	touch /tmp/create_portal_table.lua
	cmd_require='local sqlite = require "luci.torchlight.uac.wportal_sqlApi"'
	cmd_jump_page='sqlite.portal_create_jump_page_table()'
	cmd_portal='sqlite.portal_create_portal_entry_table()'
	cmd_user='sqlite.user_mngr_create_user_table()'
	cmd_user_ssl_vpn='sqlite.ssl_vpn_user_mngr_create_user_table()'
	cmd_radius='sqlite.auth_server_create_radius_table()'
	cmd_group='sqlite.auth_server_create_auth_group_table()'
	echo $cmd_require > /tmp/create_portal_table.lua
	echo $cmd_jump_page >> /tmp/create_portal_table.lua
	echo $cmd_portal >> /tmp/create_portal_table.lua
	echo $cmd_user >> /tmp/create_portal_table.lua
	echo $cmd_user_ssl_vpn >> /tmp/create_portal_table.lua
	echo $cmd_radius >> /tmp/create_portal_table.lua
	echo $cmd_group >> /tmp/create_portal_table.lua
	lua /tmp/create_portal_table.lua
	rm -rf /tmp/create_portal_table.lua
}


is_support_interface_mode()
{
	support_interface_mode=`uci get profile.portal_config.auth_mode_interface_attr 2>/dev/null`
	if [ "$support_interface_mode" == "1" ];then
		return 1
	else
		return 0
	fi
}

is_need_startup()
{
	power_on_disable=`uci get profile.portal_config.nginx_start_power_on_disable 2>/dev/null`
	#cloud_mngt_enable=`uci get cloud_config.conf_mngt.mngt_switch 2>/dev/null`
	portal_entry_item=`sqlite3 /etc/nouci_config/dbs/portal.db  'SELECT COUNT(*) FROM portalTable' 2>/dev/null`
	ssl_vpn_enable=`uci get ssl_vpn_config.sslvpn_server_config.enable`
	
	if [ "$portal_entry_item" == "" ];then
		portal_entry_item=0
	fi

	if [ "$ssl_vpn_enable" == "" ];then
                ssl_vpn_enable="off"
        fi

	if [ "$power_on_disable" == "1" -a "$portal_entry_item" == "0" -a "$ssl_vpn_enable" == "off" ];then
		return 0
	else
		return 1
	fi
}

check_back_img()
{
    src_img="/www/web-static/resources/cloud/12345678/background.jpg"
    dst_img="/www/web-static/resources/cloud/12345678/portalPagePreview/background.jpg"
    dst_img2="/www/web-static/resources/cloud/12345678/portalPagePreviewEdit/background.jpg"
    dst_img3="/www/web-static/resources/cloud/12345678/portalPagePreviewNew/background.jpg"
    

    [ ! -L "$dst_img" ] && ln -s $src_img $dst_img 2>/dev/null
    [ ! -L "$dst_img2" ] && ln -s $src_img $dst_img2 2>/dev/null
    [ ! -L "$dst_img3" ] && ln -s $src_img $dst_img3 2>/dev/null

}

start() {

	echo 20000 > /proc/sys/net/unix/max_dgram_qlen
	mkdir -p /var/log/nginx
	mkdir -p /var/lib/nginx

	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo 19","wait_time":0,"type":"feature","action":"add"}' &> /dev/null
	
	modify_nginx_worker_num_and_dict_size
	modify_portal_port
	create_portal_table

	is_need_startup
	if [ $? -eq 0 ];then
		echo "Don't need start nginx" > /dev/null
		return
	else
		lua /usr/lib/lua/luci/model/wechat_domain_handler.lua add
		echo "Now start nginx" > /dev/null
		lua /usr/lib/lua/luci/model/check_cert.lua
	fi
	
	is_support_interface_mode

	if [ $? -eq 1 ];then		
		ipset flush Atd_out_ipmac
		ipset flush Atd_out_ip
		ipset flush Atd_out_mac
		ipset flush AuthIplimit
	fi
    
	remake_nginx_cert
	$NGINX_BIN
	
	touch /tmp/nginx.ready     
    
}

stop() {
	$NGINX_BIN -s stop
	
	rm -f /tmp/nginx.ready	
	
}

reload() {
	$NGINX_BIN -s reload
}

restart() {
	
	remake_nginx_cert
	
	$NGINX_BIN -s stop
    
	sleep 3s
	is_support_interface_mode
	
	if [ $? -eq 1 ];then
		ipset flush Atd_out_ipmac
		ipset flush Atd_out_ip
		ipset flush Atd_out_mac
		ipset flush AuthIplimit		
	fi
	    
	$NGINX_BIN
    
	ubus call cloudclient register_cmd_callback '{"register_notify_cmd":"echo 19","wait_time":0,"type":"feature","action":"add"}' &> /dev/null
	lua /usr/lib/lua/luci/model/wechat_domain_handler.lua add
}

shutdown() {
	$NGINX_BIN -s quit
}

