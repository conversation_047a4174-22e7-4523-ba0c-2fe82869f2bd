#!/bin/sh /etc/rc.common

START=60
STOP=60

DHCPD_BOOTSTRAP_PID_FILE=/var/run/dhcpd6_bootstrap.pid
DHCPD_BOOTSTRAP_SH=/usr/sbin/dhcpd6_bootstrap.sh
DO_RECORD_SH=/usr/sbin/dhcpd6_do_record.sh
DHCPD_LOCK_FILE=/var/lock/dhcpd6_exec.lock
DEBUG_FILE=/tmp/dhcp6_debug.log

is_dhcpd_bootstrap_sh_started(){
	if [ -f ${DHCPD_BOOTSTRAP_PID_FILE} ]
	then
		ps_res=`ps | grep "dhcpd6_boot*" | grep -v "grep" | awk '{print $1}'`
		if [[ "$ps_res" != "" ]];then
			split=`echo $ps_res | cut -d " " -f 1`
			if [[ $split == `cat ${DHCPD_BOOTSTRAP_PID_FILE}` ]];then
				return 1
			fi
		fi
	fi
	return 0
}

kill_dhcpd_bootstrap_sh(){
	ps_res=`ps | grep "dhcpd6_boot*" | grep -v "grep" | awk '{print $1}'`
	if [[ "$ps_res" != "" ]];then
		if [[ `echo $ps_res | awk -F ' ' '{print NF - 1}'` -eq 0 ]];then
			split=`echo $ps_res | cut -d " " -f 1`
			kill -9 $split > /dev/null 2>&1
		else
			i=1
			while [ "$i" -ne 0 ]; do
				split=`echo $ps_res | cut -d " " -f $i`
				if [[ "$split" != "" ]];then
					kill -9 $split > /dev/null 2>&1
					let "i++"
					if [[ "$i" -gt 512 ]];then
						i=0
					fi
				else
					i=0
				fi
			done
		fi
	fi
	return 0
}

start_service() {
	if [ ! -d /var/etc ]; then
		mkdir /var/etc
	fi

	if [ ! -d /var/etc/config ]; then
		mkdir /var/etc/config
	fi

	flock -x ${DHCPD_LOCK_FILE} sh ${DO_RECORD_SH} inc
	is_dhcpd_bootstrap_sh_started
	if [ $? -eq 0 ]; then
		kill_dhcpd_bootstrap_sh
		/usr/sbin/dhcpd6_bootstrap.sh &
	fi
	return 0
}
verify_dhcp_config() {
	lua /usr/lib/lua/dhcp_server/dhcp6_config_verify.lua
}

start() {
	verify_dhcp_config
	start_service
}

stop() {
	echo "stop dhcpd not support"
}

reload() {
	start_service
}

restart() {
	echo "restart dhcpd not support"
}