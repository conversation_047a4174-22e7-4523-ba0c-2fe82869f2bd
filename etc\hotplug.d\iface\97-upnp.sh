#!/bin/sh

. /lib/zone/zone_api.sh

local userif=$(zone_iface_to_userif $INTERFACE)

case ${ACTION} in
	ifup)
		. /lib/functions/service.sh
		[ $(uci_get upnpd config enable_upnp) = "off" ] && return
		local zone
		for zone in $(uci_get upnpd config external_iface) $(uci_get upnpd config internal_iface); do
			[ "$zone" = "$userif" ] || {
				/etc/init.d/miniupnpd restart
				break
			}
		done
		;;
	*)
	;;
esac



