#!/bin/sh

. /lib/functions.sh
. /lib/zone/zone_api.sh


ISP_DATABASE_TMP="/tmp/isp_database.bin"
ISP_DATABASE_DIR="/etc/nouci_config/dbs"

ISP_USER_DB_TMP="/tmp/isp_user_database.bin"
ISP_USER_DB_DIR="/etc/nouci_config/dbs/isp_user_database.bin"

isp_route_restore_database()
{
	if [ ! -f $ISP_DATABASE_TMP ];then
		return 1;
	fi

	/usr/sbin/isp_route_db system -c

	if [ $? != "0" ]; then
		logger -t isp -p warn "The isp system database's format is invalid."
		rm $ISP_DATABASE_TMP -f
		return 1;
	fi

	#move the tmp isp database to /etc/nouci_config/dbs
	mv $ISP_DATABASE_TMP $ISP_DATABASE_DIR -f

	state=`uci get isp_route.global.state 2>/dev/null`
	[ "$state" == "on" ] && /etc/init.d/isp_route stop

	/usr/sbin/isp_route_db system -b

	if [ "$state" == "on" ]; then
		/etc/init.d/isp_route start
	fi
}

isp_restore_user_database()
{
	if [ ! -f $ISP_USER_DB_TMP ];then
		return 1;
	fi

	#check the valid of user's database
	/usr/sbin/isp_route_db user -c

	if [ $? != "0" ]; then
		logger -t isp -p warn "The isp user database's format is invalid."
		rm $ISP_USER_DB_TMP -f
		return 1;
	fi

	#move the tmp isp database to /etc/nouci_config/dbs
	mv $ISP_USER_DB_TMP $ISP_USER_DB_DIR -f

	state=`uci get isp_route.global.state 2>/dev/null`
	[ "$state" == "on" ] && /etc/init.d/isp_route stop

	/usr/sbin/isp_route_db user -b

	if [ "$state" == "on" ]; then
		/etc/init.d/isp_route start
	fi
}

#$1:inface
isp_route_add_rule()
{
	/etc/init.d/isp_route restart
}

isp_route_del_rule()
{
	/etc/init.d/isp_route restart
}
