#!/bin/sh
. /lib/firewall/fw.sh

# check whether the rule is exit in the iptable
# return:
# 	0 the rule is not exit
# 	1 the rule is exit
fw_check()
{
	#echo "iptables -w -t $1 -C $2 $3" > /dev/console
	local ret=$(iptables -w -t $1 -C $2 $3 2>&1)
	[ -n "$ret" ] && return 0||return 1
}

# $1==>table  mangle|filter
# $2==>subchain
add_subchain(){
	local table=$1
	local subchain="$2"
	echo "iptables -w -t $table -N $subchain" > /dev/console
	iptables -w -t $table -N $subchain 2>&1
}

# $1==>table  mangle|filter
# $2==>subchain
del_subchain(){
	local table=$1
	local subchain="$2"
	iptables -w -t $table -F $subchain 2>&1
	echo "iptables -w -t $table -F $subchain" > /dev/console
	iptables -w -t $table -X $subchain 2>&1
	echo "iptables -w -t $table -X $subchain" > /dev/console
}

# $1==>table  mangle|filter
# $2==>subchain
flush_subchain(){
	local table=$1
	local subchain="$2"
	iptables -w -t $table -F $subchain 2>&1
	echo "iptables -w -t $table -F $subchain" > /dev/console
}

# $1==>table  mangle|filter
# $2==>chain
# $3==>match
# #4==>target
# #5==>position
add_rule(){
	local table=$1
	local chain="$2"
	local match="$3"
	local target="$4"
	local position="$5"

	#echo "add match:${match}" > /dev/console
	fw_check $table $chain "$match -j $target"
	[ x$? == x0 ] && {
		echo "add rule--------------------------------------------------------------------------------------------" > /dev/console
		if [ x$position == x0 ];then
			# the position is 0, default add to the last
			echo "iptables -w -t $table -A $chain $match -j $target" > /dev/console
			iptables -w -t $table -A $chain $match -j $target
		elif [ x$position != x ];then
			# the position is not 0, add to the position
			echo "iptables -w -t $table -I $chain $position $match -j $target" > /dev/console
			iptables -w -t $table -I $chain $position $match -j $target
		fi
	}
}


# $1==>table  mangle|filter
# $2==>subchain
# $3==>key
del_rule(){

	local table=$1
	local chain=$2
	local key="${3}"
	local rulenumber=$(iptables -w -t $table -L $chain --line-numbers | grep "\b$key\b" | wc -l)
	if [ "x${rulenumber}" == "x0" ]; then
		# no rule exist in iptables
		echo "no rule in iptables for $key" > /dev/console
		return
	fi
	local firstposition=$(iptables -w -t $table -L $chain --line-numbers | grep "\b$key\b" | head -1 | awk '{print $1}')
	for i in `seq 1 ${rulenumber}`; do
		echo "iptables -w -t $table -D $chain ${firstposition}" > /dev/console
		iptables -w -t $table -D $chain ${firstposition}
	done
}

# $1==>table  mangle|filter
# $2==>subchain
# $3==>key
get_policy_position(){

	local table=$1
	local chain=$2
	local key="${3}"
	echo "iptables -w -t $table -L $chain --line-numbers | grep "\b$key\b" | tail -1 | awk '{print $1}'" > /dev/console
	local lasttposition=$(iptables -w -t $table -L $chain --line-numbers | grep "\b$key\b" | tail -1 | awk '{print $1}')
	if [ "x${lasttposition}" == "x" ]; then
		# no rule exist in iptables
		echo "0"
	else
		echo ${lasttposition}
	fi
}

# $1==>table  mangle|filter
# $2==>subchain
# $3==>action
set_chain_acction(){

	local table=$1
	local chain=$2
	local action="${3}"
	if [ "x${chain}" == "xFORWARD" -o "x${chain}" == "xFILTER" ]; then
		if [ "x${action}" == "xaccept" ]; then
			# set chain default action is accept
			echo "iptables -w -t $table -P $chain ACCEPT" > /dev/console
			iptables -w -t $table -P $chain ACCEPT
		elif [ "x${action}" == "xdrop" ]; then
			# set chain default action is drop
			echo "iptables -w -t $table -P $chain DROP" > /dev/console
			iptables -w -t $table -P $chain DROP
		fi
	elif [ "x${chain}" != "x" ]; then
		if [ "x${action}" == "xaccept" ]; then
			fw_check $table $chain "-j ACCEPT"
			[ x$? == x0 ] && {
				# set chain default action is accept
				echo "iptables -w -t $table -A $chain -j ACCEPT" > /dev/console
				iptables -w -t $table -A $chain -j ACCEPT
			}
		elif [ "x${action}" == "xdrop" ]; then
			fw_check $table $chain "-j DROP"
			[ x$? == x0 ] && {
				# set chain default action is accept
				echo "iptables -w -t $table -A $chain -j DROP" > /dev/console
				iptables -w -t $table -A $chain -j DROP
			}
		fi
	fi
}

{
	flock 258
	# $1==>action
	local action_type=$1
	case $action_type in
		add_rule)
			# $2==>table  mangle|filter
			# $3==>chain
			# $4==>match
			# #5==>target
			# #6==>position
			add_rule $2 "$3" "$4" "$5" "$6"
		;;
		del_rule)
			# $2==>table  mangle|filter
			# $3==>chain
			# $4==>key
			del_rule $2 "$3" "$4"
		;;
		add_subchain)
			# $2==>table  mangle|filter
			# $3==>subchain
			add_subchain $2 "$3"
		;;
		del_subchain)
			# $2==>table  mangle|filter
			# $3==>subchain
			del_subchain $2 "$3"
		;;
		flush_subchain)
			# $2==>table  mangle|filter
			# $3==>subchain
			flush_subchain $2 "$3"
		;;
		set_chain_acction)
			# $2==>table  mangle|filter
			# $3==>chain
			# $4==>acction
			set_chain_acction $2 "$3" "$4"
		;;
		get_policy_position)
			# $2==>table  mangle|filter
			# $3==>chain
			# $4==>key
			get_policy_position $2 "$3" "$4"
		;;
		*)
			return
		;;
	esac
} 258<>/tmp/ip_rule_executor_lock




