#!/bin/sh

_delete_rule() {
    local cfg="$1"
    local iface="$2"
    local out_if

    out_if=$(uci_get dns "$cfg" out_if)
    echo "cfg=$cfg,iface=$iface,out_if=$out_if"  >> /tmp/dnsproxy_wanhook.log
    [ "$out_if" == "$iface" ] && {
        uci delete dns.$cfg
        uci_commit dns
    }
}

_del_policy()
{
	config_get userif $1 userif
	if [ "$userif" == "$iface" ];then
		uci delete dns.${1}
		uci_commit dns
	fi
}

_del_zone()
{
	local iface=$1

	config_load dns
	config_foreach _del_policy dns_policy
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
		    echo "interfaces=$interfaces" >> /tmp/dnsproxy_wanhook.log
		    interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				echo "element=$element"  >> /tmp/dnsproxy_wanhook.log
				config_load dns
				config_foreach _delete_rule rule $element
				_del_zone ${element}
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac