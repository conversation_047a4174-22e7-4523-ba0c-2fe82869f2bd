#!/bin/sh /etc/rc.common
#Copyright (C) 2008-2015
#Author: luopei
#date: 2015.5.6
#brief:create the script file for ippool

START=42

CONFIGFILE="/var/etc/ippool.conf"
BIN=ippool
RUN_D=/var/run
PID_F=$RUN_D/$BIN.pid
ITEM_F="/var/etc/config_arg"

#USE_PROCD=1
PROG="/usr/sbin/ippoold"
SERVICE_DAEMONIZE=1

local rule_number=0

read_config(){
	local name
	local start_ip
	local end_ip
	
	config_get name $1 name  #variable section_name  option_name
	config_get start_ip $1 start_ip
	config_get end_ip  $1 end_ip
	
	echo "${name}#${start_ip}#${end_ip}" >>$CONFIGFILE
	rule_number=$(($rule_number+1))
}

start(){
	mkdir -p $(dirname $CONFIGFILE)
	
	[ -e $CONFIGFILE ] && rm -f $CONFIGFILE
        touch $CONFIGFILE   #avoid open error when /etc/config/ippool is empty
	#local max_item=$(uci -c /etc/profile.d get profile.@ippool[0].item_max)
    local max_item=$(uci get profile.@ippool[0].item_max)
	echo $max_item > $ITEM_F
    #echo 64 > $ITEM_F
	config_load ippool   #load config file
	
	config_foreach read_config ippool  #funcname section_name
	[ "$rule_number" -ge 1 ] && {
		# grep -q "ippoold#/etc/init.d/ippool restart" /etc/sys_monitor.conf || echo "ippoold#/etc/init.d/ippool restart" >>/etc/sys_monitor.conf
		# /usr/sbin/ippoold $CONFIGFILE $PID_F &  # make it run in background
		# procd_open_instance
		# procd_set_param command "$PROG" "$CONFIGFILE" "$PID_F"
		# procd_set_param respawn 3600 5 60
		# procd_close_instance

		service_start "$PROG" "$CONFIGFILE" "$PID_F"
	}

}

stop(){
	[ -e $CONFIGFILE ] && rm -f $CONFIGFILE
	# [ -f $PID_F ] && kill $(cat $PID_F)
	service_stop $PROG
}

restart(){
	stop
	start
}

reload(){  # notify the client to
   # # to do something here
   # config_load ippool   #load config file
   # config_foreach read_config ippool  #funcname section_name
	
   # return 1
   restart
}
