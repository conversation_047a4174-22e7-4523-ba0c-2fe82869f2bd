#!/bin/sh

# Copyright (C) 2006-2010 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

no_fo_pivot() {
    # switch to the new (empty) jffs2
    main_version=`uname -r |awk -F'.' '{print $1}'`
    minor_version=`uname -r |awk -F'.' '{print $2}'`
    if [ ${main_version} -eq 3 -a ${minor_version} -ge 18 ]||[ ${main_version} -ge 3 ]; then
        mkdir -p /overlay/root /overlay/work
        fopivot /overlay/root /overlay/work /rom 1
    else
        fopivot /overlay /rom 1
    fi
}

boot_hook_add no_fo no_fo_pivot
