#!/bin/sh /etc/rc.common

SERVICE_DAEMONIZE=1

START=90

add_iptables()
{
	config_get addr "$1" tv_address
	if ["$2" == "ip"];then
		ipset add projection_tv_ip_set "$addr"
	else
		mac_addr=${addr//-/:}
		ipset add projection_tv_ip_set "$mac_addr"
	fi

}

start()
{
	if [ "$1" == "n" ]; then
		echo "[upnp_proxy] nginx not reload" > /dev/console
	else
		echo "[upnp_proxy] nginx on" > /dev/console
		/lib/projection/projection_switch.sh on
	fi

	qr_code_band_switch=`uci get projection.setting.qr_code_band_switch 2>/dev/null`
	device_name_alter_switch=`uci get projection.setting.device_name_alter_switch 2>/dev/null`
	if [[ "$qr_code_band_switch" == "on" ]] && [[ "$device_name_alter_switch" == "on" ]];then
		bind_mode=`uci get projection.setting.bind_mode 2>/dev/null`
		ipset create projection_tv_ip_set hash:"$bind_mode"
		iptables -t mangle -I FORWARD -p udp -m set --match-set projection_tv_ip_set src -j replacetarget
		config_load projection
		config_foreach add_iptables room "$bind_mode"
	fi

	service_start /usr/sbin/airplayproxy
	service_start /usr/sbin/dlnaproxy
	return 0
}

stop()
{
	iptables -t mangle -D FORWARD -p udp -m set --match-set projection_tv_ip_set src -j replacetarget
	ipset destroy projection_tv_ip_set
	service_stop /usr/sbin/airplayproxy
	service_stop /usr/sbin/dlnaproxy
	if [ "$1" == 'n' ]; then
		echo "[upnp_proxy] nginx not reload" > /dev/console
	else
		echo "[upnp_proxy] nginx off" > /dev/console
		/lib/projection/projection_switch.sh off
	fi
	return 0
}

restart()
{
	stop $1
	start $1
}

reload()
{
	restart
}

