#!/bin/sh
# add/delete route rule, add/delete nat rule
#
#${ACTION}
#${IFNAME}
#${PPPD_USERNAME}
#${PPPD_PID}
#${PPPD_TYPE}
#${PPPD_CONFIGPATH}
#${IPREMOTE}
#${IPLOCAL}
#

. /lib/functions.sh
. /lib/firewall/fw.sh
. /lib/pppox/pppox-default-variables.sh

DEFAULT_GW_SUBNET="0.0.0.0/0"

get_param()
{
	config_get netmode $1 "netmode"
	config_get remotesubnet $1 "remotesubnet"
	config_get workmode $1 "workmode"
}

do_ifup()
{
	route del -host ${IPREMOTE} dev ${IFNAME}
	case "${PPPD_TYPE}" in
		"server")
			route add -host ${IPREMOTE} metric ${metric} dev ${IFNAME}
			ip route add scope link  ${IPREMOTE}  metric ${metric} dev ${IFNAME} table 20
			if [ "${netmode}" = "client2lan" -o "${netmode}" = "pc2lan" ]; then
#				pppd will do this
#				route add -host ${IPREMOTE} dev ${IFNAME}
				echo ""
			else
				if [ ${remotesubnet} == ${DEFAULT_GW_SUBNET} ]; then
				route add ${hostnet} ${remotesubnet} gw ${IPREMOTE} metric ${metric} dev ${IFNAME}
				else
					#route add ${hostnet} ${remotesubnet}  ${IPREMOTE} metric ${metric} dev ${IFNAME}
					route add ${hostnet} ${remotesubnet}  metric ${metric} dev ${IFNAME}
					ip route add ${remotesubnet} dev ${IFNAME} metric ${metric} via ${IPREMOTE} table 20
				fi

			fi;;
		"client")
			[ "${lns}" != "${IPREMOTE}" ] && {
				route add -host ${IPREMOTE} metric ${metric} dev ${IFNAME}
				ip route add scope link  ${IPREMOTE}  metric ${metric} dev ${IFNAME} table 20
			}
			
			if [ ${remotesubnet} == ${DEFAULT_GW_SUBNET} ]; then
			route add ${hostnet} ${remotesubnet} gw ${IPREMOTE} metric ${metric} dev ${IFNAME}
			else
				#route add ${hostnet} ${remotesubnet} ${IPREMOTE} metric ${metric} dev ${IFNAME}
				 route add ${hostnet} ${remotesubnet} metric ${metric} dev ${IFNAME}
			    ip route add ${remotesubnet} dev ${IFNAME} metric ${metric} via ${IPREMOTE} table 20
			fi
			
			[ "$?" != "0" ] && route add ${hostnet} ${remotesubnet} metric ${metric} dev ${IFNAME};;
	esac
	
	[ ${workmode} = "nat" ] && {
		iptables -w -t nat -C POSTROUTING -o ${IFNAME} -j MASQUERADE 2>/dev/null
		[ "$?" != "0" ] && fw add 4 nat POSTROUTING MASQUERADE  $ { -o ${IFNAME} }
	}	
}

do_ifdown()
{
	case "${PPPD_TYPE}" in
		"server")
			route del -host ${IPREMOTE} dev ${IFNAME}
			ip route del scope link ${IPREMOTE} dev ${IFNAME} table 20
			if [ "${netmode}" = "client2lan" -o "${netmode}" = "pc2lan" ]; then
#				pppd will do this
#				route del -host ${IPREMOTE} dev ${IFNAME}
				echo ""
			else
				route del ${hostnet} ${remotesubnet} dev ${IFNAME}
				ip route del ${remotesubnet} dev ${IFNAME} table 20
			fi;;
		"client")
			route del ${hostnet} ${remotesubnet} dev ${IFNAME}
			route del -host ${IPREMOTE} dev ${IFNAME}
			ip route del scope link ${IPREMOTE} dev ${IFNAME} table 20
			ip route del ${remotesubnet} dev ${IFNAME} table 20;;
	esac
	
	[ ${workmode} = "nat" ] && fw del 4 nat POSTROUTING MASQUERADE  $ { -o ${IFNAME} }	
}

#PPPD_CONFIGPATH like "/tmp/l2tp/server/server0", note: no "config"
path=${PPPD_CONFIGPATH}/config
UCI_CONFIG_DIR=${path}

metric=0
vvtype=l2tp
if echo ${PPPD_CONFIGPATH}|grep -q "^/tmp/l2tp"; then
	metric=`uci get system.metric.l2tp 2>/dev/null`
	vvtype=l2tp
elif echo ${PPPD_CONFIGPATH}|grep -q "^/tmp/pptp"; then
	metric=`uci get system.metric.pptp 2>/dev/null`
	vvtype=pptp
elif echo ${PPPD_CONFIGPATH}|grep -q "^/tmp/pppoe"; then
	metric=`uci get system.metric.pppoe 2>/dev/null`
	vvtype=pppoe
fi
metric=${metric:-0}

if [ "${PPPD_TYPE}" = "server" ]; then
#workmode for server must be route
	pppuser=`asciimaker -a "${PPPD_USERNAME}"`
	netmode=`uci -c ${path} get ${pppox_if_config}.${pppuser}.netmode 2>/dev/null`
	remotesubnet=`uci -c ${path} get ${pppox_if_config}.${pppuser}.remotesubnet 2>/dev/null`
	workmode=route
elif [ "${PPPD_TYPE}" = "client" ]; then
	configname=${pppox_configname}
	
	config_load ${configname}
	config_foreach get_param
	config_foreach config_clear

#	netmode=`uci -c ${path} get ${configname}.@${vvtype}[-1].netmode 2>/dev/null`
#	remotesubnet=`uci -c ${path} get ${configname}.@${vvtype}[-1].remotesubnet 2>/dev/null`
#	workmode=`uci -c ${path} get ${configname}.@${vvtype}[-1].workmode 2>/dev/null`

	lns=`cat ${path}/serverip`
	netmode=lan2lan
else
	return 1
fi

hostnet="-net"
[ "${remotesubnet#*/}" = "32" ] && { hostnet="-host"; 
		remotesubnet=${remotesubnet%/*}
	}
case "${ACTION}" in
	"ifup")
		do_ifup;;
	"ifdown")
		do_ifdown;;
	*)
		return 1;;
esac
