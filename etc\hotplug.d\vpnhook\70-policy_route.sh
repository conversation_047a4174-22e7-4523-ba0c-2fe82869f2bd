#!/bin/sh

. /lib/functions.sh
. /lib/policy_route/api.sh
. /lib/policy_route/time.sh

DELETED_POLICIES=
MODIFIED_POLICIES=

check_iface_exist()
{
	iface=`uci get policy_route.$1.use_iface 2>/dev/null`
	if [ "$iface" = "$INTERFACE" ]; then
		# 策略中只有被删掉的这一个出接口时，删除此策略
		uci delete policy_route.$1
		uci_commit policy_route
		append DELETED_POLICIES "$1"
	elif list_contains iface "$INTERFACE"; then
		# 策略中还有其他出接口时，只删掉这个接口，不删除策略
		list_remove iface "$INTERFACE"
		uci del_list policy_route.$1.use_iface=$INTERFACE
		uci_commit policy_route
		append MODIFIED_POLICIES "$1"
	fi
}

del_policy_rule()
{
	local src_ipgroup dst_ipgroup service timeobj
	config_get use_policy $1 use_policy

	# 删除路由时也要删除对应的对象引用。
	# 注意每条路由条目都会给接口引用 +1, 即便是共用 policy 的路由也如此
	if list_contains DELETED_POLICIES "$use_policy"; then
		# 此时 config_get 拿到的还是删除前 load 的值，可以使用已被删除的信息
		config_get src_ipgroup $1 src_ipgroup
		config_get dst_ipgroup $1 dst_ipgroup
		config_get service $1 service_type
		config_get timeobj $1 timeobj

		policy_route_ref_del "$INTERFACE" "$src_ipgroup $dst_ipgroup" "$service"
		[ "$timeobj" != "Any" ] && del_timeobj "$use_policy" "$timeobj"

		uci delete policy_route.$1
		uci_commit policy_route
	elif list_contains MODIFIED_POLICIES "$use_policy"; then
		iface_ref_del $INTERFACE
	fi
}

_delete_rule()
{
	local INTERFACE=$1 policy_name=""

	config_load policy_route
	config_foreach check_iface_exist policy

	#if the policy name exist and del the rule
	if [ -n "$DELETED_POLICIES" -o -n "$MODIFIED_POLICIES" ];then
		config_foreach del_policy_rule policy_rule
	fi
}

case ${ACTION} in
	DELETE)
		[ -n "${interfaces}" ] && {
			interfaces=${interfaces//,/ }
			for element in $interfaces
			do
				[ -n "$element" ] && _delete_rule $element
			done
		}
	;;
	ADD)
	;;
	*)
	;;
esac
