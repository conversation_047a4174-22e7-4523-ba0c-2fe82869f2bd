#!/bin/sh /etc/rc.common

START=96

. /lib/balance/api.sh

backup_timer_mode_handle()
{
	config_get state $1 state

	[ "$state" == "on" ] || return

	config_get mode $1 mode
	config_get timeobj $1 timeobj
	config_get slave_iface $1 slave_if
	config_get master_iface $1 master_if

	if [ "$mode" == "timer" ]; then
		# mode is timer
		if [ "$timeobj" == "Any" ];then
			logger -t backup -p notice "The backup timer for interface:[$slave_iface]."
			/usr/sbin/line_backup -t timer -s $slave_iface -o "on"
		else
			. /lib/time_mngt/timeobj_api.sh

			#add the new timeobj
			timeobj_api_add $timeobj line_backup $slave_iface
			timeobj_api_commit line_backup
		fi
	else
		balance_state=$(balance_get_state $master_iface)
		[ -z "$balance_state" ] && balance_state=off
		/usr/sbin/line_backup -t fault -i $master_iface -o "$balance_state"
	fi
}

DIR="/tmp/balance/system/"

start()
{
	. /lib/functions.sh
	. /lib/backup/backup.sh

	state=`get_balance_global_state`
	[ "$state" == "off" ] && return

	config_load line_backup
	config_foreach backup_timer_mode_handle rule

	if [ ! -f $DIR"backup" ]; then
		mkdir -p $DIR
		touch $DIR"backup"
	fi
}
