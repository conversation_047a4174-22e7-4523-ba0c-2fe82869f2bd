#!/bin/sh

[ "$INTERFACE" == "loopback" ] && exit 0

check_inface_reset_load_balance()
{
	state=`uci get load_balance.basic.balance_state 2>/dev/null`

	if [ "$state" == "on" ]; then
		infaces=`uci get load_balance.basic.use_if 2>/dev/null`

		[ -n "$infaces" ] || return 1

		for inface in $infaces;do
			if [ "$inface" == $INTERFACE ]; then
				/etc/init.d/load_balance restart
				return
			fi
		done
	fi
}

get_inface_name()
{
	[ $flag -eq 1 ] && return
	config_get balance $1 balance

	if [ "$balance" -eq 1 -a "$INTERFACE" == "$1" ]; then
	 	flag=1;
	fi
}

check_inface_reset_default_balance()
{
	local flag=0

	. /lib/functions.sh

	config_load mwan3
	config_foreach get_inface_name if

	if [ $flag -eq 1 ];then
		/etc/init.d/default_balance restart
	fi
}

. /lib/balance/api.sh

state=`get_balance_global_state`
[ "$state" == "off" ] && return

case "$ACTION" in
	ifup)

		[ -f /tmp/load_balance.ready ] && check_inface_reset_load_balance
		[ -f /tmp/default_balance.ready ] && check_inface_reset_default_balance
		;;

	ifdown)

		[ -f /tmp/load_balance.ready ] && check_inface_reset_load_balance
		[ -f /tmp/default_balance.ready ] && check_inface_reset_default_balance
		;;
esac



