#!/bin/sh
[ -z "$1" ] && echo "Error: should be run by udhcpc" && exit 1

. /lib/functions.sh
. /lib/netifd/netifd-proto.sh

set_classless_routes() {
	local max=128
	local type
	while [ -n "$1" -a -n "$2" -a $max -gt 0 ]; do
		proto_add_ipv4_route "${1%%/*}" "${1##*/}" "$2"
		max=$(($max-1))
		shift 2
	done
}

setup_interface () {
	proto_init_update "*" 1
	proto_add_ipv4_address "$ip" "${subnet:-*************}"
	# TODO: apply $broadcast

	for i in $router; do
		proto_add_ipv4_route 0.0.0.0 0 "$i"
	done

	# CIDR STATIC ROUTES (rfc3442)
	[ -n "$staticroutes" ] && set_classless_routes $staticroutes
	[ -n "$msstaticroutes" ] && set_classless_routes $msstaticroutes

	for dns in $dns; do
		proto_add_dns_server "$dns"
	done
	for domain in $domain; do
		proto_add_dns_search "$domain"
	done

	proto_add_data
	[ -n "$ZONE" ] && json_add_string zone "$ZONE"
	proto_close_data

	proto_send_update "$INTERFACE"


	if [ "$IFACE6RD" != 0 -a -n "$ip6rd" ]; then
		local v4mask="${ip6rd%% *}"
		ip6rd="${ip6rd#* }"
		local ip6rdprefixlen="${ip6rd%% *}"
		ip6rd="${ip6rd#* }"
		local ip6rdprefix="${ip6rd%% *}"
		ip6rd="${ip6rd#* }"
		local ip6rdbr="${ip6rd%% *}"

		[ -n "$ZONE" ] || ZONE=$(fw3 -q network $INTERFACE)
		[ -z "$IFACE6RD" -o "$IFACE6RD" = 1 ] && IFACE6RD=${INTERFACE}_6rd

		json_init
		json_add_string name "$IFACE6RD"
		json_add_string ifname "@$INTERFACE"
		json_add_string proto "6rd"
		json_add_string peeraddr "$ip6rdbr"
		json_add_int ip4prefixlen "$v4mask"
		json_add_string ip6prefix "$ip6rdprefix"
		json_add_int ip6prefixlen "$ip6rdprefixlen"
		json_add_string tunlink "$INTERFACE"
		[ -n "$IFACE6RD_DELEGATE" ] && json_add_boolean delegate "$IFACE6RD_DELEGATE"
		[ -n "$ZONE6RD" ] || ZONE6RD=$ZONE
		[ -n "$ZONE6RD" ] && json_add_string zone "$ZONE6RD"
		json_close_object

		ubus call network add_dynamic "$(json_dump)"
	fi

	# TODO
	# [ -n "$ntpsrv" ] && 	change_state network "$ifc" lease_ntpsrv "$ntpsrv"
	# [ -n "$timesvr" ] && 	change_state network "$ifc" lease_timesrv "$timesvr"
	# [ -n "$hostname" ] &&	change_state network "$ifc" lease_hostname "$hostname"
	# [ -n "$timezone" ] && 	change_state network "$ifc" lease_timezone "$timezone"
}

deconfig_interface() {
	proto_init_update "*" 0
	proto_send_update "$INTERFACE"
}

case "$1" in
	deconfig)
		deconfig_interface
	;;
	renew|bound)
		setup_interface
	;;
esac

# user rules
[ -f /etc/udhcpc.user ] && . /etc/udhcpc.user

exit 0
