#!/bin/sh /etc/rc.common
START=92

. /lib/sdb/sdb_lock.sh
. /lib/sdb/sdb_config.sh

# purpose : 用于处理load hotsite sdb 之后提交相关事宜
load_commit(){

    SUBSHELL_PID=$(sh -c 'echo $PPID')
    sdb_lock_take hotsite_load $SUBSHELL_PID
    echo "urlfilter_profile" >> /tmp/policy_db/db_modified
    sdb_submit
    sdb_lock_give hotsite_load $SUBSHELL_PID
}

clean_info()
{
    local DB_NAME="hotsite"
    local sdb_uci
    local plugin_name
    local sdb_save_dir
    local plugin_id
    local db_version_dir="/tmp/sdb/nvram/hotsite_plugin_uci"

    sdb_uci="$(get_sdb_uci ${DB_NAME})"
    plugin_name="$(get_plugin_name ${DB_NAME})"
    sdb_save_dir="$(get_sdb_save_dir ${DB_NAME})"
    plugin_id="$(uci get ${sdb_uci}.${plugin_name}.plugin_id -c ${sdb_save_dir} 2>/dev/null)"
    #clean db_version info
    if [ -f ${db_version_dir} ];then
        rm ${db_version_dir}
    fi

    #clean plugin info
    uci delete ${PLUGIN_CONFIG}.${plugin_id}
    uci commit ${PLUGIN_CONFIG}
    set_sdb_status "${DB_NAME}" "${STATUS_FILE_NOT_EXIST}"
}

check_db_is_broken()
{
    local DB_NAME="hotsite"
    local DB_HOTSITE="hotsite.db"
    local SDB_NVRAM="/tmp/sdb/nvram"

    dbg_info "check ${DB_NAME} sdb"
    if [ -f ${SDB_NVRAM}/${DB_HOTSITE} ]; then
        sqlite3 ${SDB_NVRAM}/${DB_HOTSITE} "select count(*) from tab_hotsite;"
        ret="$?"
        if [ "0" -ne "$ret" ]; then
            dbg_info "read ${DB_HOTSITE} tab_hotsite failed!"
            clean_info
            dbg_info "clean ${DB_NAME} info"
        else
            set_sdb_status $DB_NAME "${STATUS_NONE}" "load"
        fi
        dbg_info "check ${DB_NAME} success"
    else
        clean_info
    fi
}

start() {
    local load_db_flag="$1"
    local sdb_upgrade_type="${2-load}"
    local DB_NAME="hotsite"
    local ret=0

    # hotsite 在非x86中保存在nvram目录下，设备重启时检测hotsite是否可用
    (
        # load hotsite_sdb
        if [ -n "${load_db_flag}" ]; then
            dbg_info "load ${DB_NAME} sdb"
            sec_db load ${DB_NAME} $sdb_upgrade_type
            dbg_info "load ${DB_NAME} sdb finish"
            load_commit
        fi
        # check hotsite_sdb
        check_db_is_broken
        # 网站分类库规格可能变化，需重启maldomain
        /etc/init.d/maldomain restart
    ) > /dev/console &
    # 执行完，该脚本后，无论load是否正确，都设置标志位为1
    set_sdb_init_flag ${DB_NAME} 1
}

stop() {
    # nothing to do
    true
}
