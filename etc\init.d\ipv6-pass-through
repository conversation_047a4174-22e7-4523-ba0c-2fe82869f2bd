#!/bin/sh /etc/rc.common
# Copyright (C) 2015 TP-Link

START=52
. /lib/zone/zone_api.sh

start() {
	local wan_interface=$(uci -q get network.bridge_v6.ipv6_WAN)
	local lan_interface=$(uci -q get network.bridge_v6.ipv6_LAN)
	local ipv6_bridge_mode=$(uci -q get network.bridge_v6.bridge6_enable)
	
	if [ "$ipv6_bridge_mode" == "on" ]; then
		wan_device=$(zone_get_device_byif $wan_interface)
		lan_device=$(zone_get_device_byif $lan_interface)
		ifconfig $wan_device promisc
		ifconfig $lan_device promisc
		insmod ipv6-pass-through wan_eth_name=$wan_device lan_eth_name=$lan_device
	fi
}

stop() {
	local wan_interface=$(uci -q get network.bridge_v6.ipv6_WAN)
	local lan_interface=$(uci -q get network.bridge_v6.ipv6_LAN)
	local wan_device=$(zone_get_device_byif $wan_interface)
	local lan_device=$(zone_get_device_byif $lan_interface)

	ifconfig $wan_device -promisc
	ifconfig $lan_device -promisc

	rmmod ipv6-pass-through
}
