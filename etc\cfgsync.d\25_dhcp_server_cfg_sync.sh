#!/usr/bin/lua

local cfgsync = require "luci.torchlight.uac.config_sync"
local dbg = require "luci.tools.debug"

--The path prefix of userconfig
local uc_path = arg[2]

--The full path of your config file
local uci_path_dhcp = uc_path..'etc/config/dhcpd'
local static_host_db_path = uc_path..'tmp/etc/dbs/dhcp_host.db'


--[[Construct config format data, then call fit functions]]
local table_config_uci_dhcp = 
{
	path= uci_path_dhcp,
	name= "dhcp",
	sections=
	{
		{
			name=nil,
			stype="dhcpd_list",
			base_options= 
			{
				"enable",
				"lease_time",
				"option138",
				"option60",
				"pool_start",
				"pool_end",
				"gate_way",
				"domain",
				"pri_dns",
				"snd_dns",
				"dynamicdhcp",
				"addrtype",
				"interface"
			},
			options=
			{
				enable = '',
				option138 = '',
				option60 = '',
				lease_time = '',
				poo_start = '',
				poo_end = '',
				gate_way = '',
				domain = '',
				pri_dns = '',
				snd_dns ='',
				dynamicdhcp = '',
				addrtype = '',
				interface = ''
			}
		},
		{
			name="settings",
			stype="global",
			base_options= 
			{
				"alloc_target"
			},
			options=
			{
				alloc_target ='for_ap'
			}
		},
		{
			name=nil,
			stype="dhcp_static",
			base_options= 
			{
				"mac",
				"enable",
				"ip"
			},
			options=
			{
				mac = "",
				["enable"] = '',
				ip = "",
				note = ""
			}
		}
	}
}

local table_config_database_static_host = 
{
	db_path= static_host_db_path,
	db_name= "dhcp_host.db",
	tables=
	{
		{
			tbl_name= "host_tbl",
			base_columns=
			{
				"mac","ip","dhcp_static_id"
			},
 			columns=
 			{
 				{
 					name="enable",
 					types="VARCHAR(4)",
 					length=0,
 					db_attr=nil,
 					app_attr=nil,
 					default="on"
 				},
 				{
 					name="mac",
 					types="VARCHAR(32)",
 					length=32,
 					db_attr={"db_index"},
 					app_attr={"app_base"},
 					default=nil
 				},
 				{
 					name="ip",
 					types="VARCHAR(32)",
 					length=32,
 					db_attr={"db_unique","db_index"},
 					app_attr={"app_base"},
 					default=nil
 				},
 				{
 					name="name",
 					types="VARCHAR(128)",
 					length=128,
 					db_attr={"db_unique","db_index"},
 					app_attr=nil,
 					default=function(entry,entry_seq)
 							local ret = ""
 							if entry.mac then
	 							ret = string.gsub(tostring(entry.mac), "-", "")
							else
								dbg("[WARNING]set name value failed because mac is nil")
								return nil
							end
 							return ret
					    end
 				},
 				{
 					name="note",
 					types="VARCHAR(128)",
 					length=128,
 					db_attr=nil,
 					app_attr=nil,
 					default=""
 				},
 				{
 					name="dhcp_static_id",
 					types="INTEGER",
 					length=0,
 					db_attr={"db_key","db_auto_increment"},
 					app_attr={"app_base"},
 					default=nil
 				}
 			},--end columns
 			default_entry = nil
 		}
	}--end tables
}

--uci config_sync
cfgsync.config_sync(table_config_uci_dhcp,"configfile")
cfgsync.config_sync(table_config_database_static_host,"database")
